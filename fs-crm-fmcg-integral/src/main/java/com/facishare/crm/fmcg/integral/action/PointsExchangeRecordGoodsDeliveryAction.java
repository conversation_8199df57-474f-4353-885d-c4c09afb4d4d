package com.facishare.crm.fmcg.integral.action;

import com.alibaba.fastjson.annotation.JSONField;
import com.beust.jcommander.internal.Lists;
import com.beust.jcommander.internal.Maps;
import com.facishare.crm.fmcg.integral.ApiNames;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.AbstractStandardAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Map;


public class PointsExchangeRecordGoodsDeliveryAction extends AbstractStandardAction<PointsExchangeRecordGoodsDeliveryAction.Arg, PointsExchangeRecordGoodsDeliveryAction.Result> {
    private IObjectData objectData;

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList("GoodsDelivery");
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return Lists.newArrayList(arg.getDataId());
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        String productId = objectData.get("product_id", String.class);
        List<IObjectData> product = serviceFacade.findObjectDataByIds(actionContext.getUser().getTenantId(), Lists.newArrayList(productId), ApiNames.POINTS_GOODS_OBJ.getApiName());
        if (CollectionUtils.isEmpty(product)) {
            throw new ValidateException("商品不存在！");
        }
        //库存上线
        Double availableStock = product.get(0).get("stock", Double.class);
        //已兑换数量
        // todo : converted_quantity
        Double convertedQuantity = product.get(0).get("converted_quantity", Double.class);
        if (availableStock == null) {
            throw new ValidateException("当前商品库存不足，请补充库存后再操作！");
        }
        double flag = availableStock - (convertedQuantity == null ? 0D : convertedQuantity);
        if (flag <= 0) {
            throw new ValidateException("当前商品库存不足，请补充库存后再操作！");
        }
    }

    @Override
    protected Result doAct(Arg arg) {
        Map<String, Object> updateMap = Maps.newHashMap();
        // todo : use meaningful option value
        updateMap.put("order_state", "delivered");
        IObjectData data = serviceFacade.updateWithMap(actionContext.getUser(), objectData, updateMap);
        return Result.of(data);
    }


    protected IObjectData getPreObjectData() {
        return objectData;
    }

    protected IObjectData getPostObjectData() {
        return objectData;
    }

    protected String getButtonApiName() {
        return "GoodsDelivery_button_default";
    }

    @Override
    protected void init() {
        super.init();
        if (CollectionUtils.isNotEmpty(dataList)) {
            objectData = dataList.get(0);
        }
    }

    @Data
    public static class Arg {

        @SerializedName("objectDataId")
        @JSONField(name = "objectDataId")
        @JsonProperty("objectDataId")
        private String dataId;
    }


    @Data
    public static class Result {
        private ObjectDataDocument objectData;

        public static Result of(IObjectData objectData) {
            Result result = new Result();
            result.setObjectData(ObjectDataDocument.of(objectData));
            return result;
        }
    }
}

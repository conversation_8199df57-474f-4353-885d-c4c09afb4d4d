package com.facishare.crm.fmcg.integral.controller;

import com.facishare.crm.fmcg.integral.business.BusinessTradingIndustryService;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;

/**
 * <AUTHOR>
 * @create 2021-12-27
 **/
public class PointsExchangeRecordListHeaderController extends StandardListHeaderController {

    @Override
    protected Result after(Arg arg, Result result) {
        BusinessTradingIndustryService.filterObjButtons(controllerContext.getTenantId(), arg.getApiName(), result);
        BusinessTradingIndustryService.filterHeaderButtons(arg.getApiName(), result);
        return super.after(arg, result);
    }
}

package com.facishare.crm.fmcg.integral.controller;

import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2021 - 12 - 16  6:04 下午
 **/
public class BaseFilterWebDetailController extends StandardWebDetailController {

    private static final List<String> disableButtons = new ArrayList<>();
    private static final List<String> INVALID_EDIT_BUTTON_OBJ_APINAME = new ArrayList<>();
    private static final List<String> INVALID_EDIT_DISABLE_BUTTONS = new ArrayList<>();


    static {
        disableButtons.add("Edit");
        disableButtons.add("Clone");
    }

    static {
        INVALID_EDIT_DISABLE_BUTTONS.add("Edit");
        INVALID_EDIT_DISABLE_BUTTONS.add("Abolish");
        INVALID_EDIT_DISABLE_BUTTONS.add("Clone");
        INVALID_EDIT_DISABLE_BUTTONS.add("ChangeOwner");
    }

    static {
        INVALID_EDIT_BUTTON_OBJ_APINAME.add("PointsExchangeRecordObj");
    }

    /*
        处理预置对象，保证对象详情页不显示"编辑"按钮。

    */
    @Override
    protected Result after(Arg arg, Result result) {
        List components = (ArrayList) (result.getLayout().get("components"));
        List buttons = (ArrayList) (result.getLayout().get("buttons"));
        if (INVALID_EDIT_BUTTON_OBJ_APINAME.contains(arg.getObjectDescribeApiName())) {
            result.getLayout().put("component", getComponents(INVALID_EDIT_DISABLE_BUTTONS, components));
            result.getLayout().put("buttons", getButtons(INVALID_EDIT_DISABLE_BUTTONS, buttons));

        } else {
            result.getLayout().put("component", getComponents(disableButtons, components));
        }
        return super.after(arg, result);
    }

    private List getComponents(List<String> disableButtons, List components) {
        for (Object component : components) {
            Map com = (Map) component;
            if (Objects.equals(com.get("api_name"), "head_info")) {
                ArrayList buttons = (ArrayList) com.get("buttons");
                buttons.removeIf(button -> {
                    Map btn = (Map) (button);
                    return disableButtons.contains(btn.get("action"));
                });
                ((Map) component).put("buttons", buttons);
            }
        }
        return components;
    }

    private List getButtons(List<String> disableButtons, List buttons) {
        buttons.removeIf(button -> {
            Map btn = (Map) (button);
            return disableButtons.contains(btn.get("action"));
        });
        return buttons;
    }
}

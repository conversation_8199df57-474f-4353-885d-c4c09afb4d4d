package com.facishare.crm.fmcg.integral;

import com.facishare.paas.appframework.core.model.ActionClassInfo;
import com.facishare.paas.appframework.core.model.ControllerClassInfo;
import com.facishare.paas.appframework.core.model.PreDefineObject;
import com.facishare.paas.appframework.core.model.PreDefineObjectRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
public enum IntegralPreDefineObject implements PreDefineObject {

    PointsExchangeRecord("PointsExchangeRecordObj");


    private static final Logger logger = LoggerFactory.getLogger(IntegralPreDefineObject.class);

    private final String apiName;
    private static String PACKAGE_NAME = IntegralPreDefineObject.class.getPackage().getName();

    IntegralPreDefineObject(String apiName) {
        this.apiName = apiName;
    }

    public static IntegralPreDefineObject getEnum(String apiName) {
        List<IntegralPreDefineObject> list = Arrays.asList(IntegralPreDefineObject.values());
        return list.stream().filter(m -> m.getApiName().equalsIgnoreCase(apiName)).findAny().orElse(null);
    }

    public static void init() {
        for (IntegralPreDefineObject object : IntegralPreDefineObject.values()) {
            PreDefineObjectRegistry.register(object);
        }
    }

    @Override
    public String getApiName() {
        return this.apiName;
    }

    @Override
    public String getPackageName() {
        return PACKAGE_NAME;
    }

    @Override
    public ActionClassInfo getDefaultActionClassInfo(String actionCode) {
        String className = PACKAGE_NAME + ".action." + this + actionCode + "Action";
        return new ActionClassInfo(className);
    }

    @Override
    public ControllerClassInfo getControllerClassInfo(String methodName) {
        String className = PACKAGE_NAME + ".controller." + this + methodName + "Controller";
        return new ControllerClassInfo(className);
    }
}

package com.facishare.crm.fmcg.integral.business;

import com.alibaba.fastjson.JSONObject;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.google.common.collect.Lists;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 处理预置对象，保证对象详情页不显示"新建"、"导入"按钮。
 *
 * @create 2021 - 12 - 07  4:08 下午
 **/
public class BusinessTradingIndustryService {



    private static final Map<String, List<String>> DEFAULT_FILTER_BUTTON = new HashMap<>();
    private static final Map<String, List<String>> HEADER_FILTER_BUTTON = new HashMap<>();

    private static final String ADD = "Add";

    private static final String IMPORT = "Import";

    private static final String ABOLISH = "AsyncBulkInvalid";
    private static final String CHANGE_OWNER = "AsyncBulkChangeOwner";


    static {

        DEFAULT_FILTER_BUTTON.put("PointsExchangeRecordObj", Lists.newArrayList(ADD, IMPORT));
    }

    static {
        HEADER_FILTER_BUTTON.put("PointsExchangeRecordObj", Lists.newArrayList(ABOLISH,CHANGE_OWNER));
    }

    /**
     * 过滤掉对象描述不需要显示的按钮
     *
     * @param tenantId
     * @param apiName
     * @param result
     */
    public static void filterObjButtons(String tenantId, String apiName, StandardListHeaderController.Result result) {

        List<JSONObject> buttons = (List<JSONObject>) result.getLayout().get("buttons");
        result.getLayout().put("buttons", buttons.stream().filter(button -> {
            if (DEFAULT_FILTER_BUTTON.containsKey(apiName)) {
                return !DEFAULT_FILTER_BUTTON.get(apiName).contains(button.getString("action"));
            } else {
                return true;
            }
        }).collect(Collectors.toList()));

    }

    /**
     * 过滤掉选中数据之后表头的按钮
     */
    public static void filterHeaderButtons(String apiName, StandardListHeaderController.Result result) {
        if (!HEADER_FILTER_BUTTON.containsKey(apiName)) {
            return;
        }
        HEADER_FILTER_BUTTON.get(apiName).forEach(actionVal -> result.getButtons().removeIf(buttonDocument -> buttonDocument.containsValue(actionVal)));
    }
}

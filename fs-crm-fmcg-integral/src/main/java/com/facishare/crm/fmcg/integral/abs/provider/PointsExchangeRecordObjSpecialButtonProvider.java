package com.facishare.crm.fmcg.integral.abs.provider;

import com.facishare.crm.fmcg.integral.IntegralPreDefineObject;
import com.facishare.crm.fmcg.integral.abs.AbstractIntegralSpecialButtonProvider;
import com.facishare.paas.metadata.ui.layout.IButton;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class PointsExchangeRecordObjSpecialButtonProvider extends AbstractIntegralSpecialButtonProvider {

    @Override
    public String getApiName() {
        return IntegralPreDefineObject.PointsExchangeRecord.getApiName();
    }

    @Override
    public List<IButton> getSpecialButtons() {
        return super.getSpecialButtons();
    }
}

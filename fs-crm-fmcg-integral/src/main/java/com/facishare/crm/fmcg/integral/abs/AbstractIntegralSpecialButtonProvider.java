package com.facishare.crm.fmcg.integral.abs;

import com.facishare.crm.fmcg.integral.IntegralPreDefineObject;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.metadata.button.SpecialButtonProvider;
import com.facishare.paas.metadata.impl.ui.layout.Button;
import com.facishare.paas.metadata.ui.layout.IButton;
import de.lab4inf.math.util.Strings;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@SuppressWarnings("Duplicates")
public abstract class AbstractIntegralSpecialButtonProvider implements SpecialButtonProvider {

    @Override
    public String getApiName() {
        return IntegralPreDefineObject.PointsExchangeRecord.getApiName();
    }

    @Override
    public List<IButton> getSpecialButtons() {
        List<IButton> buttons = new ArrayList<>(1);
        buttons.add(buildButton(ObjectAction.GOODS_DELIVERY));
        return buttons;
    }

    private static IButton buildButton(ObjectAction objectAction) {
        IButton button = new Button();
        button.setAction(objectAction.getActionCode());
        button.setLabel(objectAction.getActionLabel());
        button.set("isActive", true);
        if (Strings.isNullOrEmpty(objectAction.getButtonApiName())) {
            button.setName(objectAction.getActionCode() + "_button_" + IButton.ACTION_TYPE_DEFAULT);
        } else {
            button.setName(objectAction.getButtonApiName());
        }
        button.setActionType(IButton.ACTION_TYPE_DEFAULT);
        return button;
    }
}

package com.facishare.crm.fmcg.others.controller;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.others.business.ListHeaderBusiness;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2020/11/10 4:39 PM
 */
public class CoinAccountListHeaderController extends StandardListHeaderController {

    @Override
    protected Result after(Arg arg, Result result) {
        ListHeaderBusiness.listHeaderFilter(controllerContext.getTenantId(),arg.getApiName(),result);
        return super.after(arg, result);
    }
}

package com.facishare.crm.fmcg.others.controller;

import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardDetailListHeaderController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2021 - 12 - 20  3:16 下午
 **/
public class BaseFilterStandardDetailListHeaderController extends StandardDetailListHeaderController {

    private static final List<String> disableButtons = new ArrayList<>();

    static {
        disableButtons.add("Add");
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Map<String, LayoutDocument> detailLayouts = result.getDetailLayouts();
        LayoutDocument default__c = detailLayouts.get("default__c");
        ArrayList buttons = (ArrayList)default__c.get("buttons");
        buttons.removeIf(button-> {
            Map btn = (Map) (button);
            return disableButtons.contains(btn.get("action"));
        });
        default__c.put("buttons",buttons);
        result.getDetailLayouts().put("default__c",default__c);
        return super.after(arg, result);
    }
}

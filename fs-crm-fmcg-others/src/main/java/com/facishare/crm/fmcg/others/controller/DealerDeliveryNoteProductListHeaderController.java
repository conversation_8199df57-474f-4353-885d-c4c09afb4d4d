package com.facishare.crm.fmcg.others.controller;

import com.facishare.crm.fmcg.others.business.BusinessTradingIndustryService;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;

/**
 * <AUTHOR>
 * @create 2021 - 12 - 07  7:06 下午
 **/
public class DealerDeliveryNoteProductListHeaderController extends StandardListHeaderController {

    @Override
    protected Result after(Arg arg, Result result) {
        BusinessTradingIndustryService.filterObjButtons(controllerContext.getTenantId(), arg.getApiName(), result);
        return super.after(arg, result);
    }
}

package com.facishare.crm.fmcg.others.business;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import de.lab4inf.math.util.Strings;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 处理商贸通的七个预置对象，保证对象详情页不显示"新建"、"导入"按钮。
 * DealerOrderObj、DealerOrderProductObj、DealerCheckinsObj、DealerStockObj、
 * DealerDeliveryNoteObj、DealerDeliveryNoteProductObj、DealerWarehouseObj
 *
 * <AUTHOR>
 * @create 2021 - 12 - 07  4:08 下午
 **/
public class BusinessTradingIndustryService {

    private static Map<String, Map<String, List<String>>> BUSINESS_LIST_HEADER_FILTER_CONFIG = new HashMap<>();

    private static final String CONFIG_NAME = "gray-rel-fmcg";

    private static final Map<String, List<String>> DEFAULT_FILTER_BUTTON = new HashMap<>();
    private static final Map<String, List<String>> HEADER_FILTER_BUTTON = new HashMap<>();

    private static final String ADD = "Add";

    private static final String IMPORT = "Import";
    private static final String INTELLIGENT_FORM = "IntelligentForm";
    private static final String EXPORT_FILE = "ExportFile";

    private static final String ABOLISH = "AsyncBulkInvalid";


    static {
        ConfigFactory.getConfig(CONFIG_NAME, config -> {
            String json = config.get("BUSINESS_LIST_HEADER_FILTER_CONFIG");
            if (!Strings.isNullOrEmpty(json)) {
                Map<String, Map<String, List<String>>> map = JSON.parseObject(json, new TypeReference<Map<String, Map<String, List<String>>>>() {
                });
                BUSINESS_LIST_HEADER_FILTER_CONFIG = map;
            }
        });
        DEFAULT_FILTER_BUTTON.put("DealerOrderObj", Lists.newArrayList(ADD, IMPORT));
        DEFAULT_FILTER_BUTTON.put("DealerOrderProductObj", Lists.newArrayList(ADD, IMPORT));
        DEFAULT_FILTER_BUTTON.put("DealerCheckinsObj", Lists.newArrayList(ADD, IMPORT));
        DEFAULT_FILTER_BUTTON.put("DealerStockObj", Lists.newArrayList(ADD, IMPORT));
        DEFAULT_FILTER_BUTTON.put("DealerDeliveryNoteObj", Lists.newArrayList(ADD, IMPORT));
        DEFAULT_FILTER_BUTTON.put("DealerDeliveryNoteProductObj", Lists.newArrayList(ADD, IMPORT));
        DEFAULT_FILTER_BUTTON.put("DealerWarehouseObj", Lists.newArrayList(ADD, IMPORT));
        DEFAULT_FILTER_BUTTON.put("PointsExchangeRecordObj", Lists.newArrayList(ADD, IMPORT));
        DEFAULT_FILTER_BUTTON.put("TelephoneVisitObj", Lists.newArrayList(ADD, IMPORT));
    }

    static {
        HEADER_FILTER_BUTTON.put("PointsExchangeRecordObj", Lists.newArrayList(ABOLISH));
    }

    /**
     * 过滤掉对象描述不需要显示的按钮
     *
     * @param tenantId
     * @param apiName
     * @param result
     */
    public static void filterObjButtons(String tenantId, String apiName, StandardListHeaderController.Result result) {

        List<JSONObject> buttons = (List<JSONObject>) result.getLayout().get("buttons");
        if (BUSINESS_LIST_HEADER_FILTER_CONFIG.get(tenantId) != null) {
            List<String> filterList = BUSINESS_LIST_HEADER_FILTER_CONFIG.get(tenantId).get(apiName);
            result.getLayout().put("buttons", buttons.stream().filter(v -> !filterList.contains(v.getString("action"))).collect(Collectors.toList()));
        } else {
            result.getLayout().put("buttons", buttons.stream().filter(button -> {
                if (DEFAULT_FILTER_BUTTON.containsKey(apiName)) {
                    return !DEFAULT_FILTER_BUTTON.get(apiName).contains(button.getString("action"));
                } else {
                    return true;
                }
            }).collect(Collectors.toList()));
        }

    }

    /**
     * 过滤掉选中数据之后表头的按钮
     */
    public static void filterHeaderButtons(String apiName, StandardListHeaderController.Result result) {
        if (!HEADER_FILTER_BUTTON.containsKey(apiName)) {
            return;
        }
        HEADER_FILTER_BUTTON.get(apiName).forEach(actionVal -> result.getButtons().removeIf(buttonDocument -> buttonDocument.containsValue(actionVal)));
    }
}

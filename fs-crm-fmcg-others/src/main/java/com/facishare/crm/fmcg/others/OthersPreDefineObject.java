package com.facishare.crm.fmcg.others;

import com.facishare.paas.appframework.core.model.ActionClassInfo;
import com.facishare.paas.appframework.core.model.ControllerClassInfo;
import com.facishare.paas.appframework.core.model.PreDefineObject;
import com.facishare.paas.appframework.core.model.PreDefineObjectRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/19 上午10:41
 */
public enum OthersPreDefineObject implements PreDefineObject {
    CoinAccountDetail("CoinAccountDetailObj"),
    CoinAccount("CoinAccountObj"),
    Ledger("LedgerObj"),
    LedgerDetail("LedgerDetailObj"),
    PointsRewardDetail("PointsRewardDetailObj"),
    DealerPointsRewardDetail("DealerPointsRewardDetailObj"),
    CheckinsVerify("CheckinsVerifyObj"),
    CheckinsVerifyDetail("CheckinsVerifyDetailObj"),
    DealerOrder("DealerOrderObj"),
    DealerOrderProduct("DealerOrderProductObj"),
    DealerCheckins("DealerCheckinsObj"),
    DealerStock("DealerStockObj"),
    DealerDeliveryNote("DealerDeliveryNoteObj"),
    DealerDeliveryNoteProduct("DealerDeliveryNoteProductObj"),
    TelephoneVisit("TelephoneVisitObj"),
    DealerWarehouse("DealerWarehouseObj");


    private static final Logger logger = LoggerFactory.getLogger(OthersPreDefineObject.class);

    private final String apiName;
    private static String PACKAGE_NAME = OthersPreDefineObject.class.getPackage().getName();

    OthersPreDefineObject(String apiName) {
        this.apiName = apiName;
    }

    public static OthersPreDefineObject getEnum(String apiName) {
        List<OthersPreDefineObject> list = Arrays.asList(OthersPreDefineObject.values());
        return list.stream().filter(m -> m.getApiName().equalsIgnoreCase(apiName)).findAny().orElse(null);
    }

    public static void init() {
        for (OthersPreDefineObject object : OthersPreDefineObject.values()) {
            PreDefineObjectRegistry.register(object);
        }
    }
    @Override
    public String getApiName() {
        return this.apiName;
    }

    @Override
    public String getPackageName() {
        return PACKAGE_NAME;
    }

    @Override
    public ActionClassInfo getDefaultActionClassInfo(String actionCode) {
        String className = PACKAGE_NAME + ".action." + this + actionCode + "Action";
        return new ActionClassInfo(className);
    }

    @Override
    public ControllerClassInfo getControllerClassInfo(String methodName) {
        String className = PACKAGE_NAME + ".controller." + this + methodName + "Controller";
        return new ControllerClassInfo(className);
    }
}

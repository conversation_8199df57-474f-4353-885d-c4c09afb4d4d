package com.facishare.crm.fmcg.service;

import com.facishare.crm.fmcg.integral.IntegralPreDefineObject;
import com.facishare.crm.fmcg.others.OthersPreDefineObject;
import com.facishare.crm.fmcg.tpm.TPMPreDefineObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.support.ApplicationObjectSupport;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

@Service
@Slf4j
public class CRMInitService extends ApplicationObjectSupport {

    @PostConstruct
    public void init() {

        log.info("start init tpm.");
        TPMPreDefineObject.init();

        log.info("start others schedule.");
        OthersPreDefineObject.init();

        log.info("start init integral.");
        IntegralPreDefineObject.init();
    }
}
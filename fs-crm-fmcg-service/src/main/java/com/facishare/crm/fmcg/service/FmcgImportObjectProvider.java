package com.facishare.crm.fmcg.service;

import com.beust.jcommander.internal.Lists;
import com.beust.jcommander.internal.Sets;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.dto.ImportObjectModule;
import com.facishare.paas.appframework.metadata.importobject.ImportObjectProvider;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @program: fs-crm-fmcg
 * @description:
 * @author: zhangsm
 * @create: 2022-04-07 14:31
 **/
@Component
public class FmcgImportObjectProvider implements ImportObjectProvider {
    @Autowired
    private DescribeLogicService describeLogicService;

    @Override
    public String getObjectModule() {
        return "FS-CRM-FMCG-TPM";
    }

    @Override
    public List<ImportObjectModule.ImportModule> getImportObjectModule(User user, ImportModuleContext importModuleContext) {
        Map<String, IObjectDescribe> iObjectDescribeMap = describeLogicService.findObjects(user.getTenantId(), Lists.newArrayList(
                "CoveredStoresObj", "PromotionActivityObj", "PromoterObj"
        ));
        return iObjectDescribeMap.values().stream()
                .map(ImportObjectModule.ImportModule::of)
                .collect(Collectors.toList());
    }

}

<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <!-- 日志中默认打印traceId和userId，方便定位问题,异常栈中去掉包含如下字符的行避免打印很多无用的信息 -->
    <property name="defaultPattern" value="%d{HH:mm:ss.SSS} [%thread] %-5level %logger{12} %X{traceId} %X{userId} %msg%rEx{full,
                java.lang.Thread,
                javassist,
                sun.reflect,
                org.springframework,
                org.apache,
                org.eclipse.jetty,
                $Proxy,
                java.net,
                java.io,
                javax.servlet,
                org.junit,
                com.mysql,
                com.sun,
                org.mybatis.spring,
                cglib,
                CGLIB,
                java.util.concurrent,
                okhttp,
                org.jboss,
                }%n"/>

    <appender name="CRMLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${catalina.home}/logs/crm.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${catalina.home}/logs/crm.%d{yyyy-MM-dd}.log.zip</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>1GB</maxFileSize>
            <totalSizeCap>10GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <pattern>${defaultPattern}</pattern>
        </encoder>
    </appender>

    <appender name="Error" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <file>${catalina.home}/logs/error.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${catalina.home}/logs/error.%d{yyyy-MM-dd}.log.zip</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>1GB</maxFileSize>
            <totalSizeCap>5GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <pattern>${defaultPattern}</pattern>
        </encoder>
    </appender>

    <appender name="OSS_Trace" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${catalina.home}/logs/trace.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${catalina.home}/logs/trace.%d{yyyy-MM-dd}.log.zip</fileNamePattern>
            <maxHistory>3</maxHistory>
            <maxFileSize>1GB</maxFileSize>
            <totalSizeCap>5GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <pattern>${defaultPattern}</pattern>
        </encoder>
    </appender>

    <appender name="PerfLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${catalina.home}/logs/perf.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${catalina.home}/logs/perf.%d{yyyy-MM-dd}.log.zip</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>1GB</maxFileSize>
            <totalSizeCap>10GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <pattern>${defaultPattern}</pattern>
        </encoder>
    </appender>

    <appender name="DebuggingLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${catalina.home}/logs/debugging.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${catalina.home}/logs/debugging.%d{yyyy-MM-dd}.log.zip</fileNamePattern>
            <maxHistory>3</maxHistory>
            <maxFileSize>1GB</maxFileSize>
            <totalSizeCap>10GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <pattern>${defaultPattern}</pattern>
        </encoder>
    </appender>

    <appender name="PaymentLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${catalina.home}/logs/payment.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${catalina.home}/logs/payment.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>1GB</maxFileSize>
            <totalSizeCap>10GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <pattern>${defaultPattern}</pattern>
        </encoder>
    </appender>

    <appender name="ElectronicSign" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${catalina.home}/logs/ElectronicSign.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${catalina.home}/logs/ElectronicSign.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>1GB</maxFileSize>
            <totalSizeCap>10GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <pattern>${defaultPattern}</pattern>
        </encoder>
    </appender>
    <appender name="ElectronicSign_ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>2048</queueSize>
        <appender-ref ref="ElectronicSign"/>
    </appender>

    <appender name="ServiceLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${catalina.home}/logs/service.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${catalina.home}/logs/member.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>1GB</maxFileSize>
            <totalSizeCap>10GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <pattern>${defaultPattern}</pattern>
        </encoder>
    </appender>

    <appender name="FcpAccessLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${catalina.home}/logs/fcp-access.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${catalina.home}/logs/fcp-access.%d{yyyy-MM-dd}.log.zip</fileNamePattern>
            <maxHistory>3</maxHistory>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <!-- 日志中默认打印traceId和userId，方便定位问题 -->
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{12} %X{traceId} %X{userId} %m%n
            </pattern>
        </encoder>
    </appender>

    <appender name="CalculateLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${catalina.home}/logs/calculate.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${catalina.home}/logs/calculate.%d{yyyy-MM-dd}.log.zip</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>1GB</maxFileSize>
            <totalSizeCap>10GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <pattern>${defaultPattern}</pattern>
        </encoder>
    </appender>

    <appender name="RequestLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${catalina.home}/logs/request.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${catalina.home}/logs/request.%d{yyyy-MM-dd}.log.zip</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>1GB</maxFileSize>
            <totalSizeCap>10GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <pattern>${defaultPattern}</pattern>
        </encoder>
    </appender>
    <appender name="CRM_ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>2048</queueSize>
        <appender-ref ref="CRMLog"/>
    </appender>

    <appender name="PERF_ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>2048</queueSize>
        <appender-ref ref="PerfLog"/>
    </appender>

    <appender name="Debugging_ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>2048</queueSize>
        <appender-ref ref="DebuggingLog"/>
    </appender>

    <appender name="OSS_ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>2048</queueSize>
        <appender-ref ref="OSS_Trace"/>
    </appender>

    <appender name="performance-logger" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${catalina.home}/logs/performance.log</file>
        <!-- 可让每天产生一个日志文件，最多 7 个，自动回滚 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${catalina.home}/logs/performance-%d{yyyyMMdd}.%i.log.zip</fileNamePattern>
            <maxHistory>5</maxHistory>
            <maxFileSize>10GB</maxFileSize>
            <totalSizeCap>15GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <!-- 日志中默认打印traceId和userId，方便定位问题,异常栈中去掉包含如下字符的行避免打印很多无用的信息-->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{12} %msg%rEx{full,
                java.lang.Thread,
                javassist,
                sun.reflect,
                org.springframework,
                org.apache,
                org.eclipse.jetty,
                $Proxy,
                java.net,
                java.io,
                javax.servlet,
                org.junit,
                com.mysql,
                com.sun,
                org.mybatis.spring,
                cglib,
                CGLIB,
                java.util.concurrent,
                okhttp,
                org.jboss,
                }%n
            </pattern>
        </encoder>
    </appender>
    <!-- 异步输出 -->
    <appender name="performance-async-appender" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="performance-logger"/>
        <appender-ref ref="error-log"/>
    </appender>

    <logger name="com.facishare.tools" level="WARN" additivity="false">
        <appender-ref ref="performance-async-appender"/>
        <appender-ref ref="error-log"/>
    </logger>
    <logger name="com.github.trace" level="INFO" additivity="false">
        <appender-ref ref="OSS_ASYNC"/>
    </logger>

    <logger name="com.facishare" level="WARN" additivity="false">
        <appender-ref ref="CRM_ASYNC"/>
        <appender-ref ref="Error"/>
    </logger>

    <logger name="com.facishare.paas.appframework.common.util.StopWatch" level="INFO" additivity="false">
        <appender-ref ref="PERF_ASYNC"/>
    </logger>

    <logger name="com.facishare.paas.expression.util.StopWatch" level="WARN" additivity="false">
        <appender-ref ref="PERF_ASYNC"/>
    </logger>


    <logger name="com.facishare.paas.metadata.util.MetadataStopWatch" level="WARN" additivity="false">
        <appender-ref ref="PERF_ASYNC"/>
    </logger>

    <logger name="memberAccess" level="INFO" additivity="false">
        <appender-ref ref="MemberLog"/>
    </logger>

    <logger name="com.facishare.crm.fmcg" level="INFO" additivity="false">
        <appender-ref ref="ServiceLog"/>
    </logger>

    <logger name="ElectronicSignAccess" level="INFO" additivity="false">
        <appender-ref ref="ElectronicSign_ASYNC"/>
    </logger>

    <logger name="com.facishare.fcp.log.FcpAccessLogger" level="INFO" additivity="false">
        <appender-ref ref="FcpAccessLog"/>
    </logger>

    <logger name="com.facishare.paas.appframework.metadata.MetaDataComputeServiceImpl" level="INFO" additivity="false">
        <appender-ref ref="CalculateLog"/>
    </logger>

    <logger name="com.facishare.paas.appframework.core.predef.service.CalculateService" level="INFO" additivity="false">
        <appender-ref ref="CalculateLog"/>
    </logger>

    <logger name="com.facishare.paas.appframework.metadata.expression" level="INFO" additivity="false">
        <appender-ref ref="CalculateLog"/>
    </logger>

    <logger name="com.facishare.paas.appframework.metadata.relation" level="INFO" additivity="false">
        <appender-ref ref="CalculateLog"/>
    </logger>

    <logger name="com.facishare.paas.appframework.jaxrs.provider.RequestLogFilter" level="INFO" additivity="false">
        <appender-ref ref="RequestLog"/>
    </logger>

    <logger name="com.facishare.paas.appframework.fcp.model.AppFrameworkPreProcessFilter" level="INFO" additivity="false">
        <appender-ref ref="RequestLog"/>
    </logger>

    <logger name="com.facishare.paas.appframework.common.util.DebuggingLogger" level="INFO" additivity="false">
        <appender-ref ref="Debugging_ASYNC"/>
    </logger>

    <root level="INFO">
        <appender-ref ref="CRM_ASYNC"/>
    </root>
</configuration>

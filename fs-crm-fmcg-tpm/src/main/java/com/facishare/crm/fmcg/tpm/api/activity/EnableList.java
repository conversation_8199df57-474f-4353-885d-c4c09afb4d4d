package com.facishare.crm.fmcg.tpm.api.activity;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public interface EnableList {

    @Data
    @ToString
    class Arg implements Serializable {

        @JSONField(name = "visit_status")
        @JsonProperty("visit_status")
        @SerializedName("visit_status")
        private String visitStatus;

        @SerializedName("visit_id")
        @JSONField(name = "visit_id")
        @JsonProperty("visit_id")
        private String visitId;

        @SerializedName("action_id")
        @JSONField(name = "action_id")
        @JsonProperty("action_id")
        private String actionId;

        @SerializedName("store_id")
        @JSONField(name = "store_id")
        @JsonProperty("store_id")
        private String storeId;

        @SerializedName("activity_type_list")
        @JSONField(name = "activity_type_list")
        @JsonProperty("activity_type_list")
        private List<String> activityTypeList;
    }

    @Data
    @ToString
    class Result implements Serializable {

        /**
         * activity_list
         * proof_audit
         * proof
         * no_activity
         */
        @SerializedName("navigate_strategy")
        @JSONField(name = "navigate_strategy")
        @JsonProperty("navigate_strategy")
        private String navigateStrategy;

        @SerializedName("store_id")
        @JSONField(name = "store_id")
        @JsonProperty("store_id")
        private String storeId;

        @SerializedName("store_id__r")
        @JSONField(name = "store_id__r")
        @JsonProperty("store_id__r")
        private String storeName;

        @SerializedName("function_code_map")
        @JSONField(name = "function_code_map")
        @JsonProperty("function_code_map")
        private Map<String, Map<String, Boolean>> functionCodeMap;

        @SerializedName("system_time")
        @JSONField(name = "system_time")
        @JsonProperty("system_time")
        private Long systemTime;


        private List<ActivityGroupVO> data = Lists.newArrayList();
    }

    @Data
    @ToString
    class ActivityGroupVO implements Serializable {

        private String groupKey;

        private String groupName;

        @SerializedName("activity_list")
        @JSONField(name = "activity_list")
        @JsonProperty("activity_list")
        private List<EnableList.ActivityVO> activityList = Lists.newArrayList();
    }

    @Data
    @ToString
    class ActivityVO implements Serializable {

        private String id;

        private String name;

        @SerializedName("begin_date")
        @JSONField(name = "begin_date")
        @JsonProperty("begin_date")
        private long beginDate;

        @SerializedName("end_date")
        @JSONField(name = "end_date")
        @JsonProperty("end_date")
        private long endDate;

        @SerializedName("proof_count")
        @JSONField(name = "proof_count")
        @JsonProperty("proof_count")
        private long proofCount;

        @SerializedName("agreement_required")
        @JSONField(name = "agreement_required")
        @JsonProperty("agreement_required")
        private boolean agreementRequired;

        @SerializedName("activity_agreement_id")
        @JSONField(name = "activity_agreement_id")
        @JsonProperty("activity_agreement_id")
        private String activityAgreementId;

        @SerializedName("agreement_begin_date")
        @JSONField(name = "agreement_begin_date")
        @JsonProperty("agreement_begin_date")
        private long agreementBeginDate;

        @SerializedName("agreement_end_date")
        @JSONField(name = "agreement_end_date")
        @JsonProperty("agreement_end_date")
        private long agreementEndDate;

        @SerializedName("data_api_name")
        @JSONField(name = "data_api_name")
        @JsonProperty("data_api_name")
        private String dataApiName;

        @SerializedName("data_id")
        @JSONField(name = "data_id")
        @JsonProperty("data_id")
        private String dataId;

        /**
         * in_progress      - 待举证
         * completed        - 已举证
         * no_agreement     - 缺少协议
         * agreement_expired          -已过期
         * schedule_agreement  -未生效
         */
        private String status;

        @SerializedName("agreement_life_status")
        @JSONField(name = "agreement_life_status")
        @JsonProperty("agreement_life_status")
        private String agreementLifeStatus;

        @SerializedName("agreement_count")
        @JSONField(name = "agreement_count")
        @JsonProperty("agreement_count")
        private long agreementCount;

        @SerializedName("agreement_status")
        @JSONField(name = "agreement_status")
        @JsonProperty("agreement_status")
        private String agreementStatus;

        @SerializedName("proof_record_type")
        @JSONField(name = "proof_record_type")
        @JsonProperty("proof_record_type")
        private String proofRecordType;
    }
}
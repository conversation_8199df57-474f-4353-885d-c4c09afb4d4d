package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.tpm.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.apiname.CommonFields;
import com.facishare.crm.fmcg.tpm.apiname.TPMActivityProofAuditDetailFields;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/21 下午6:23
 */
public class TPMActivityItemCostStandardObjInvalidAction extends StandardInvalidAction {

    @Override
    protected void before(Arg arg) {
        validateRelated(arg);
        super.before(arg);
    }


    private void validateRelated(Arg arg) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter idFilter = new Filter();
        idFilter.setFieldName(TPMActivityProofAuditDetailFields.ACTIVITY_ITEM_COST_STANDARD_ID);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(arg.getObjectDataId()));

        Filter deletedFilter = new Filter();
        deletedFilter.setFieldName(CommonFields.IS_DELETED);
        deletedFilter.setOperator(Operator.EQ);
        deletedFilter.setFieldValues(Lists.newArrayList("false"));

        query.setFilters(Lists.newArrayList(idFilter, deletedFilter));

        List<IObjectData> data = serviceFacade.findBySearchQuery(User.systemUser(actionContext.getTenantId()), ApiNames.TPM_ACTIVITY_PROOF_AUDIT_DETAIL_OBJ, query).getData();

        if (!data.isEmpty()) {
            throw new ValidateException(I18N.text(I18NKeys.COST_STANDARD_RELATED_BY_AUDIT_DETAIL));
        }

        query.getFilters().clear();
        Filter idFilter2 = new Filter();
        idFilter2.setFieldName(TPMActivityProofAuditDetailFields.ACTIVITY_ITEM_COST_STANDARD_ID);
        idFilter2.setOperator(Operator.EQ);
        idFilter2.setFieldValues(Lists.newArrayList(arg.getObjectDataId()));

        query.setFilters(Lists.newArrayList(idFilter2, deletedFilter));

        data = serviceFacade.findBySearchQuery(User.systemUser(actionContext.getTenantId()), ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ, query).getData();

        if (!data.isEmpty()) {
            throw new ValidateException(I18N.text(I18NKeys.COST_STANDARD_RELATED_BY_PROOF_DETAIL));
        }
    }
}

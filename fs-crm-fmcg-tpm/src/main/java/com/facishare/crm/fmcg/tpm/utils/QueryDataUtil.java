package com.facishare.crm.fmcg.tpm.utils;

import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/4/29 20:13
 */
@SuppressWarnings("Duplicates")
public class QueryDataUtil {

    private QueryDataUtil() {
        // empty ctor
    }

    // one million
    public static final int MAX_QUERY_SIZE = 1000000;

    public static List<IObjectData> find(
            ServiceFacade facade,
            String tenantId,
            String apiName,
            SearchTemplateQuery query,
            List<String> fields) {
        return find(facade, User.systemUser(tenantId), null, apiName, query, fields);
    }

    public static List<IObjectData> find(
            ServiceFacade facade,
            ActionContext context,
            String apiName,
            SearchTemplateQuery query,
            List<String> fields) {
        return find(facade, User.systemUser(context.getTenantId()), context.getRequestContext(), apiName, query, fields);
    }

    public static List<IObjectData> find(
            ServiceFacade facade,
            ControllerContext context,
            String apiName,
            SearchTemplateQuery query,
            List<String> fields) {
        return find(facade, User.systemUser(context.getTenantId()), context.getRequestContext(), apiName, query, fields);
    }

    private static List<IObjectData> find(
            ServiceFacade facade,
            User user,
            RequestContext context,
            String apiName,
            SearchTemplateQuery query,
            List<String> fields
    ) {
        int max = query.getLimit() == 0 || query.getLimit() == -1 ? MAX_QUERY_SIZE : query.getLimit();

        int limit = Math.min(250, max);
        int offset = 0;

        query.setLimit(limit);
        query.setNeedReturnCountNum(false);

        List<IObjectData> data = Lists.newArrayList();
        QueryResult<IObjectData> result;

        SearchTemplateQuery innerQuery = copy(query);
        while (!(result = facade.findBySearchTemplateQueryWithFields(ActionContextExt.of(user, context).getContext(), apiName, innerQuery, fields)).getData().isEmpty() && data.size() < max) {

            data.addAll(result.getData());
            offset += result.getData().size();

            innerQuery = copy(query);
            innerQuery.setOffset(offset);
        }
        return data;
    }


    public static SearchTemplateQuery copy(SearchTemplateQuery query) {
        SearchTemplateQuery clone = new SearchTemplateQuery();
        clone.setFilters(query.getFilters());
        clone.setOrders(query.getOrders());
        clone.setWheres(query.getWheres());
        clone.setLimit(query.getLimit());
        clone.setOffset(query.getOffset());
        clone.setPattern(query.getPattern());
        clone.setSearchSource(query.getSearchSource());
        return clone;
    }
}

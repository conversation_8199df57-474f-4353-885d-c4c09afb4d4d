package com.facishare.crm.fmcg.tpm.utils;

import java.util.Calendar;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 12/24/20 7:15 PM
 */
public class TimeUtils {

    private TimeUtils() {
    }

    public static long convertToDayEnd(long time) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(time);

        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTimeInMillis();
    }

    public static long convertToDayEndIfTimeWasDayBegin(long time) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(time);
        if (calendar.get(Calendar.HOUR_OF_DAY) == 0 &&
                calendar.get(Calendar.MINUTE) == 0 &&
                calendar.get(Calendar.SECOND) == 0
        ) {
            calendar.set(Calendar.HOUR_OF_DAY, 23);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);
            calendar.set(Calendar.MILLISECOND, 999);
            return calendar.getTimeInMillis();
        }
        return time;
    }

    public static boolean isIntervalOverlap(long range1Left, long range1Right, long range2Left, long range2Right) {
        return (range1Left <= range2Left && range1Right >= range2Left) || (range1Left <= range2Right && range1Right >= range2Right) || (range2Left <= range1Left && range1Right <= range2Right);
    }
}

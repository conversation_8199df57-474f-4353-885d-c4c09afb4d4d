package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.tpm.apiname.*;
import com.facishare.crm.fmcg.tpm.common.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.utils.TimeUtils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.crmrestapi.common.contants.LifeStatusEnum;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2020/11/10 3:46 PM
 */
@SuppressWarnings("Duplicates")
public class TPMActivityAgreementObjEditAction extends StandardEditAction {

    public static final Logger log = LoggerFactory.getLogger(TPMActivityAgreementObjEditAction.class);

    @Override
    protected void before(Arg arg) {
        super.before(arg);

        log.info("action arg : {}", arg);

        validateProof(arg);
        validateEndDate(arg);
        validateActivity(actionContext, arg);
        validateAgreementStatus(arg);

        log.info("after validate : {}", arg);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        String lifeStatus = (String) result.getObjectData().get(CommonFields.LIFE_STATUS);
        String agreementStatus = (String) result.getObjectData().get(TPMActivityAgreementFields.AGREEMENT_STATUS);
        Result finalResult = super.after(arg, result);

        if (!LifeStatusEnum.Normal.getValue().equals(lifeStatus) && TPMActivityAgreementFields.AGREEMENT_STATUS__IN_PROGRESS.equals(agreementStatus)) {

            Map<String, Object> updater = new HashMap<>();
            updater.put(TPMActivityAgreementFields.AGREEMENT_STATUS, TPMActivityAgreementFields.AGREEMENT_STATUS__SCHEDULE);
            serviceFacade.updateWithMap(User.systemUser(actionContext.getTenantId()), result.getObjectData().toObjectData(), updater);

            result.getObjectData().put(TPMActivityAgreementFields.AGREEMENT_STATUS, TPMActivityAgreementFields.AGREEMENT_STATUS__SCHEDULE);
        }

        return finalResult;
    }

    private void validateProof(Arg arg) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(10);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter activityFilter = new Filter();
        activityFilter.setFieldName(TPMActivityProofFields.ACTIVITY_AGREEMENT_ID);
        activityFilter.setOperator(Operator.EQ);
        activityFilter.setFieldValues(Lists.newArrayList(arg.getObjectData().getId()));

        query.setFilters(Lists.newArrayList(activityFilter));
        List<IObjectData> list = serviceFacade.findBySearchQuery(User.systemUser(actionContext.getTenantId()), ApiNames.TPM_ACTIVITY_PROOF_OBJ, query).getData();
        if (!list.isEmpty()) {
            throw new ValidateException(I18N.text(I18NKeys.AGREEMENT_WHICH_HAS_RELATED_BY_PROOF_CAN_NOT_EDIT));
        }
    }

    private void validateAgreementStatus(Arg arg) {
        IObjectData old = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), arg.getObjectData().getId(), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);
        String oldStatus = old.get(TPMActivityAgreementFields.AGREEMENT_STATUS, String.class);
        if (TPMActivityAgreementFields.AGREEMENT_STATUS__INVALID.equals(oldStatus)) {
            throw new ValidateException("已终止的协议不允许进行编辑操作。");
        }

        long begin = (long) arg.getObjectData().get(TPMActivityAgreementFields.BEGIN_DATE);
        long end = (long) arg.getObjectData().get(TPMActivityAgreementFields.END_DATE);

        long now = System.currentTimeMillis();
        String status;
        if (now < begin) {
            status = TPMActivityAgreementFields.AGREEMENT_STATUS__SCHEDULE;
        } else if (now < end) {
            status = TPMActivityAgreementFields.AGREEMENT_STATUS__IN_PROGRESS;
        } else {
            status = TPMActivityAgreementFields.AGREEMENT_STATUS__END;
        }
        arg.getObjectData().put(TPMActivityAgreementFields.AGREEMENT_STATUS, status);
    }

    private void validateActivity(ActionContext actionContext, Arg arg) {
        String activityId = (String) arg.getObjectData().get(TPMActivityAgreementFields.ACTIVITY_ID);

        if (TPMGrayUtils.agreementNotRelatedToActivity(actionContext.getTenantId()) && Strings.isNullOrEmpty(activityId)) {
            return;
        }

        IObjectData activity = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), activityId, ApiNames.TPM_ACTIVITY_OBJ);

        String closedStatus = (String) activity.get(TPMActivityFields.CLOSED_STATUS);
        if (TPMActivityFields.CLOSE_STATUS__CLOSED.equals(closedStatus)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_ENDED_CAN_NOT_CREATE_AGREEMENT));
        }

        boolean needAgreement = Boolean.TRUE.equals(activity.get(TPMActivityFields.IS_AGREEMENT_REQUIRED));
        if (!needAgreement) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_WHICH_IS_NOT_AGREEMENT_ACTIVITY_CAN_NOT_CREATE_AGREEMENT));
        }

        long activityBegin = (long) activity.get(TPMActivityFields.BEGIN_DATE);
        long activityEnd = (long) activity.get(TPMActivityFields.END_DATE);
        long agreementBegin = (long) arg.getObjectData().get(TPMActivityAgreementFields.BEGIN_DATE);
        long agreementEnd = (long) arg.getObjectData().get(TPMActivityAgreementFields.END_DATE);

        log.info("activity - begin time : {}, end time : {}, agreement - begin time : {}, end time : {}", activityBegin, activityEnd, agreementBegin, agreementEnd);

        if (agreementBegin < activityBegin || agreementEnd > activityEnd) {
            throw new ValidateException(I18N.text(I18NKeys.AGREEMENT_TIME_OUT_OF_RANGE_ERROR));
        }

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter activityFilter = new Filter();
        activityFilter.setFieldName(TPMActivityAgreementFields.ACTIVITY_ID);
        activityFilter.setOperator(Operator.EQ);
        activityFilter.setFieldValues(Lists.newArrayList(activityId));

        Filter accountFilter = new Filter();
        accountFilter.setFieldName(TPMActivityAgreementFields.STORE_ID);
        accountFilter.setOperator(Operator.EQ);
        accountFilter.setFieldValues(Lists.newArrayList((String) arg.getObjectData().get(TPMActivityAgreementFields.STORE_ID)));

        query.setFilters(Lists.newArrayList(activityFilter, accountFilter));

        List<IObjectData> agreements = CommonUtils.queryData(serviceFacade, User.systemUser(actionContext.getTenantId()), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, query);

        log.info("query store under the same activity have agreement : {}, query : {}", agreements, query);

        for (IObjectData agreement : agreements) {
            if (agreement.getId().equals(arg.getObjectData().getId())) {
                continue;
            }

            long begin = (long) agreement.get(TPMActivityAgreementFields.BEGIN_DATE);
            long end = (long) agreement.get(TPMActivityAgreementFields.END_DATE);

            log.info("start compare - begin : {}, end : {}, agreement begin : {}, agreement end : {}", begin, end, agreementBegin, agreementEnd);

            if (TimeUtils.isIntervalOverlap(begin, end, agreementBegin, agreementEnd)) {
                throw new ValidateException(I18N.text(I18NKeys.TIME_OVERLAP_IN_ACTIVITY_AND_STORE_ERROR));
            }
        }
    }

    private void validateEndDate(Arg arg) {
        long begin = (long) arg.getObjectData().get(TPMActivityAgreementFields.BEGIN_DATE);
        long end = TimeUtils.convertToDayEndIfTimeWasDayBegin((long) arg.getObjectData().get(TPMActivityAgreementFields.END_DATE));
        arg.getObjectData().put(TPMActivityAgreementFields.END_DATE, end);

        if (end <= begin) {
            throw new ValidateException(I18N.text(I18NKeys.ADD_AGREEMENT_DATE_ERROR));
        }
    }
}

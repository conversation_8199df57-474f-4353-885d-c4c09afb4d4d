package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.tpm.apiname.*;
import com.facishare.crm.fmcg.tpm.common.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.service.BuryModule;
import com.facishare.crm.fmcg.tpm.service.BuryOperation;
import com.facishare.crm.fmcg.tpm.service.BuryService;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.utils.TimeUtils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.common.release.GrayRelease;
import com.fxiaoke.crmrestapi.common.contants.LifeStatusEnum;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2020/11/10 3:46 PM
 */

@SuppressWarnings("Duplicates")
public class TPMActivityAgreementObjAddAction extends StandardAddAction {

    public static final Logger log = LoggerFactory.getLogger(TPMActivityAgreementObjAddAction.class);

    @Override
    protected void before(Arg arg) {
        log.info("action arg : {}", arg);

        validateEndDate(arg);
        stopWatch.lap("validateEndDate");

        validateActivity(serviceFacade, actionContext, arg);
        stopWatch.lap("validateActivity");

        validateAgreementStatus(arg);
        stopWatch.lap("validateAgreementStatus");

        log.info("after validate : {}", arg);

        super.before(arg);
    }

    private void validateAgreementStatus(Arg arg) {
        long begin = (long) arg.getObjectData().get(TPMActivityAgreementFields.BEGIN_DATE);
        long end = (long) arg.getObjectData().get(TPMActivityAgreementFields.END_DATE);

        long now = System.currentTimeMillis();
        String status;
        if (now < begin) {
            status = TPMActivityAgreementFields.AGREEMENT_STATUS__SCHEDULE;
        } else if (now < end) {
            status = TPMActivityAgreementFields.AGREEMENT_STATUS__IN_PROGRESS;
        } else {
            status = TPMActivityAgreementFields.AGREEMENT_STATUS__END;
        }
        arg.getObjectData().put(TPMActivityAgreementFields.AGREEMENT_STATUS, status);
    }

    private void validateActivity(ServiceFacade serviceFacade, ActionContext actionContext, Arg arg) {
        String activityId = (String) arg.getObjectData().get(TPMActivityAgreementFields.ACTIVITY_ID);

        if (TPMGrayUtils.agreementNotRelatedToActivity(actionContext.getTenantId()) && Strings.isNullOrEmpty(activityId)) {
            return;
        }

        String storeId = (String) arg.getObjectData().get(TPMActivityAgreementFields.STORE_ID);
        IObjectData activity = serviceFacade.findObjectDataIgnoreAll(User.systemUser(actionContext.getTenantId()), activityId, ApiNames.TPM_ACTIVITY_OBJ);

        String closedStatus = (String) activity.get(TPMActivityFields.CLOSED_STATUS);
        if (TPMActivityFields.CLOSE_STATUS__CLOSED.equals(closedStatus)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_ENDED_CAN_NOT_CREATE_AGREEMENT));
        }

        boolean needAgreement = Boolean.TRUE.equals(activity.get(TPMActivityFields.IS_AGREEMENT_REQUIRED));
        if (!needAgreement) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_WHICH_IS_NOT_AGREEMENT_ACTIVITY_CAN_NOT_CREATE_AGREEMENT));
        }

        String storeRangeJson = (String) activity.get(TPMActivityFields.STORE_RANGE);
        if (!Strings.isNullOrEmpty(storeRangeJson)) {
            JSONObject storeRange = JSON.parseObject(storeRangeJson);
            String type = storeRange.getString("type");

            switch (type) {
                case "FIXED":
                    SearchTemplateQuery fixedStoreQuery = new SearchTemplateQuery();

                    fixedStoreQuery.setLimit(1);
                    fixedStoreQuery.setOffset(0);
                    fixedStoreQuery.setNeedReturnCountNum(false);
                    fixedStoreQuery.setNeedReturnQuote(false);
                    fixedStoreQuery.setSearchSource("db");

                    Filter activityFilter = new Filter();
                    activityFilter.setFieldName(TPMActivityStoreFields.ACTIVITY_ID);
                    activityFilter.setOperator(Operator.EQ);
                    activityFilter.setFieldValues(Lists.newArrayList(activityId));

                    Filter storeIdFilter = new Filter();
                    storeIdFilter.setFieldName(TPMActivityStoreFields.STORE_ID);
                    storeIdFilter.setOperator(Operator.EQ);
                    storeIdFilter.setFieldValues(Lists.newArrayList(storeId));

                    fixedStoreQuery.setFilters(Lists.newArrayList(activityFilter, storeIdFilter));

                    List<IObjectData> stores = serviceFacade.findBySearchQuery(User.systemUser(actionContext.getTenantId()), ApiNames.TPM_ACTIVITY_STORE_OBJ, fixedStoreQuery).getData();

                    if (CollectionUtils.isEmpty(stores)) {
                        throw new ValidateException(I18N.text(I18NKeys.THIS_STORE_WHICH_IS_NOT_IN_THE_RANGE_CAN_NOT_CREATE_AGREEMENT));
                    }
                    break;
                case "CONDITION":
                    SearchTemplateQuery conditionStoreQuery = new SearchTemplateQuery();

                    conditionStoreQuery.setLimit(1);
                    conditionStoreQuery.setOffset(0);
                    conditionStoreQuery.setNeedReturnCountNum(false);
                    conditionStoreQuery.setNeedReturnQuote(false);
                    conditionStoreQuery.setSearchSource("db");

                    List<Wheres> conditionWheres = new ArrayList<>();

                    String valueJson = storeRange.getString("value");
                    JSONArray wheres = JSON.parseArray(valueJson);
                    for (int whereIndex = 0; whereIndex < wheres.size(); whereIndex++) {
                        JSONObject where = wheres.getJSONObject(0);

                        Wheres conditionWhere = new Wheres();
                        conditionWhere.setConnector(where.getString("connector"));
                        conditionWhere.setFilters(Lists.newArrayList());

                        JSONArray filters = where.getJSONArray("filters");
                        for (int filterIndex = 0; filterIndex < filters.size(); filterIndex++) {

                            JSONObject filter = filters.getJSONObject(filterIndex);
                            Filter conditionFilter = new Filter();

                            conditionFilter.setFieldName(filter.getString("field_name"));
                            conditionFilter.setIndexName(filter.getString("index_name"));
                            conditionFilter.setFieldValueType(filter.getString("field_value_type"));
                            conditionFilter.setOperator(Operator.valueOf(filter.getString("operator")));
                            conditionFilter.setFieldValues(filter.getJSONArray("field_values").toJavaList(String.class));
                            conditionFilter.setConnector(filter.getString("connector"));
                            conditionFilter.setValueType(filter.getInteger("value_type"));
                            conditionFilter.setRefDescribeApiName(filter.getString("ref_describe_api_name"));
                            conditionFilter.setRefFieldApiName(filter.getString("ref_field_api_name"));
                            conditionFilter.setIsCascade(filter.getBoolean("is_cascade"));
                            conditionFilter.setIsMasterField(filter.getBoolean("is_master_field"));
                            conditionFilter.setFilterGroup(filter.getString("filterGroup"));
                            conditionWhere.getFilters().add(conditionFilter);
                        }

                        Filter idFilter = new Filter();
                        idFilter.setFieldName("_id");
                        idFilter.setOperator(Operator.EQ);
                        idFilter.setFieldValues(Lists.newArrayList(storeId));
                        idFilter.setConnector("AND");
                        conditionWhere.getFilters().add(idFilter);

                        conditionWheres.add(conditionWhere);
                    }

                    conditionStoreQuery.setWheres(conditionWheres);

                    log.info("find store by condition query : {}", conditionStoreQuery.toJsonString());

                    List<IObjectData> conditionStores = serviceFacade.findBySearchQuery(User.systemUser(actionContext.getTenantId()), ApiNames.ACCOUNT_OBJ, conditionStoreQuery).getData();

                    if (CollectionUtils.isEmpty(conditionStores)) {
                        throw new ValidateException(I18N.text(I18NKeys.THIS_STORE_WHICH_IS_NOT_IN_THE_RANGE_CAN_NOT_CREATE_AGREEMENT));
                    }
                    break;
                default:
                case "ALL":
                    break;
            }
        }

        long activityBegin = (long) activity.get(TPMActivityFields.BEGIN_DATE);
        long activityEnd = (long) activity.get(TPMActivityFields.END_DATE);
        long agreementBegin = (long) arg.getObjectData().get(TPMActivityAgreementFields.BEGIN_DATE);
        long agreementEnd = (long) arg.getObjectData().get(TPMActivityAgreementFields.END_DATE);

        if (GrayRelease.isAllow("fmcg", "YINLU_TPM", actionContext.getTenantId())) {
            long now = System.currentTimeMillis();

            String activityTimeSpanPlanId = activity.get("activity_time_span_plan_id__c", String.class);
            if (!Strings.isNullOrEmpty(activityTimeSpanPlanId)) {

                IObjectData activityTimeSpanPlan = serviceFacade.findObjectData(
                        User.systemUser(actionContext.getTenantId()),
                        activityTimeSpanPlanId,
                        "tpm_activity_time_span_plan__c");

                long activityAgreementBegin = (long) activityTimeSpanPlan.get(TPMActivityFields.YINLU_AGREEMENT_BEGIN_DATE);
                long activityAgreementEnd = TimeUtils.convertToDayEndIfTimeWasDayBegin((long) activityTimeSpanPlan.get(TPMActivityFields.YINLU_AGREEMENT_END_DATE));

                if (now > activityAgreementEnd || now < activityAgreementBegin) {
                    throw new ValidateException(I18N.text(I18NKeys.CURRENT_TIME_IS_NOT_FIT_THE_AGREEMENT_TIME));
                }
                long begin = Math.min(activityAgreementBegin, activityBegin);
                long end = Math.max(activityAgreementEnd, activityEnd);

                if (agreementBegin < begin || agreementEnd > end) {
                    throw new ValidateException(I18N.text(I18NKeys.AGREEMENT_TIME_OUT_OF_RANGE_ERROR));
                }
            }
        } else {
            log.info("activity - begin time : {}, end time : {}", activityBegin, activityEnd);

            long now = System.currentTimeMillis();

            if (now > activityEnd || now < activityBegin) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_WHICH_HAS_NOT_STARTED_CAN_NOT_CREATE_AGREEMENT));
            }

            if (agreementBegin < activityBegin || agreementEnd > activityEnd) {
                throw new ValidateException(I18N.text(I18NKeys.AGREEMENT_TIME_OUT_OF_RANGE_ERROR));
            }
        }

        SearchTemplateQuery agreementQuery = new SearchTemplateQuery();

        agreementQuery.setLimit(200);
        agreementQuery.setOffset(0);
        agreementQuery.setNeedReturnCountNum(false);
        agreementQuery.setNeedReturnQuote(false);
        agreementQuery.setSearchSource("db");

        Filter activityFilter = new Filter();
        activityFilter.setFieldName(TPMActivityAgreementFields.ACTIVITY_ID);
        activityFilter.setOperator(Operator.EQ);
        activityFilter.setFieldValues(Lists.newArrayList(activityId));

        Filter accountFilter = new Filter();
        accountFilter.setFieldName(TPMActivityAgreementFields.STORE_ID);
        accountFilter.setOperator(Operator.EQ);
        accountFilter.setFieldValues(Lists.newArrayList(storeId));

        agreementQuery.setFilters(Lists.newArrayList(activityFilter, accountFilter));

        List<IObjectData> agreements = serviceFacade.findBySearchQuery(User.systemUser(actionContext.getTenantId()), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, agreementQuery).getData();

        log.info("query store under the same activity have agreement : {}, query : {}", agreements, agreementQuery);

        if (!CollectionUtils.isEmpty(agreements)) {
            agreements.forEach(agreement -> {
                long begin = (long) agreement.get(TPMActivityAgreementFields.BEGIN_DATE);
                long end = (long) agreement.get(TPMActivityAgreementFields.END_DATE);
                if (TimeUtils.isIntervalOverlap(begin, end, agreementBegin, agreementEnd)) {
                    throw new ValidateException(I18N.text(I18NKeys.TIME_OVERLAP_IN_ACTIVITY_AND_STORE_ERROR));
                }
            });
        }
    }

    private void validateEndDate(Arg arg) {
        long begin = (long) arg.getObjectData().get(TPMActivityAgreementFields.BEGIN_DATE);
        if (Objects.isNull(arg.getObjectData().get(TPMActivityAgreementFields.END_DATE))) {
            throw new ValidateException("协议结束时间不能为空。");
        }
        long end = TimeUtils.convertToDayEndIfTimeWasDayBegin((long) arg.getObjectData().get(TPMActivityAgreementFields.END_DATE));
        arg.getObjectData().put(TPMActivityAgreementFields.END_DATE, end);

        if (end <= begin) {
            throw new ValidateException(I18N.text(I18NKeys.ADD_AGREEMENT_DATE_ERROR));
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        BuryService.asyncTpmLog(Integer.valueOf(actionContext.getTenantId()), Integer.valueOf(actionContext.getUser().getUserIdOrOutUserIdIfOutUser()), BuryModule.TPM.TPM_ACTIVITY_AGREEMENT, BuryOperation.CREATE);
        Result resultData = super.after(arg, result);

        log.info("add agreement result data : {}", resultData);

        String lifeStatus = (String) resultData.getObjectData().get(CommonFields.LIFE_STATUS);
        String agreementStatus = (String) resultData.getObjectData().get(TPMActivityAgreementFields.AGREEMENT_STATUS);

        if (!LifeStatusEnum.Normal.getValue().equals(lifeStatus) && TPMActivityAgreementFields.AGREEMENT_STATUS__IN_PROGRESS.equals(agreementStatus)) {
            Map<String, Object> updater = new HashMap<>();
            updater.put(TPMActivityAgreementFields.AGREEMENT_STATUS, TPMActivityAgreementFields.AGREEMENT_STATUS__SCHEDULE);
            serviceFacade.updateWithMap(User.systemUser(actionContext.getTenantId()), resultData.getObjectData().toObjectData(), updater);
            resultData.getObjectData().put(TPMActivityAgreementFields.AGREEMENT_STATUS, TPMActivityAgreementFields.AGREEMENT_STATUS__SCHEDULE);
        }
        return resultData;
    }

    @Override
    protected void finallyDo() {
        super.finallyDo();
        this.stopWatch.logSlow(500);
    }
}

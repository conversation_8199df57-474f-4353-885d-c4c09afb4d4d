package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.tpm.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/7/21 下午3:15
 */
public class TPMActivityBudgetAdjustObjBulkInvalidAction extends StandardBulkInvalidAction {

    private static final Logger log = LoggerFactory.getLogger(TPMActivityBudgetAdjustObjBulkInvalidAction.class);

    @Override
    protected void before(Arg arg) {
        List<IObjectData> objects = serviceFacade.findObjectDataByIds(actionContext.getTenantId(),arg.getDataIds(), ApiNames.TPM_ACTIVITY_BUDGET_ADJUST_OBJ);
        List<String> names = new ArrayList<>();
        for(IObjectData data: objects){
            String status = data.get("life_status",String.class);
            if("normal".equals(status)){
                names.add(data.getName());
            }
        }
        if(!CollectionUtils.isEmpty(names)){
            throw new ValidateException(I18N.text(I18NKeys.COMPLETED_ADJUST_CAN_NOT_BE_INVALID) +names);
        }
        super.before(arg);
    }
}

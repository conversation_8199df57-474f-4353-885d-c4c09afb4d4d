package com.facishare.crm.fmcg.tpm.api.activity;

import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/3/16 2:36 PM
 */
public interface ActivityClose {

    @Data
    @ToString
    class Arg extends BaseObjectSaveAction.Arg implements Serializable {

        private String objectDataId;

        private Boolean skipDataValidate;
    }

    @Data
    @ToString
    class Result implements Serializable {

    }
}

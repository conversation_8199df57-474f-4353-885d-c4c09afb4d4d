package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.apiname.CommonFields;
import com.facishare.crm.fmcg.tpm.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.tpm.business.TPM2Service;
import com.facishare.crm.fmcg.tpm.business.abstraction.ITPM2Service;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardFlowCompletedAction;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/7/30 11:25
 */
@Slf4j
public class TPMActivityObjFlowCompletedAction extends StandardFlowCompletedAction {

    private ITPM2Service tpm2Service = SpringUtil.getContext().getBean(TPM2Service.class);

    @Override
    protected Result after(Arg arg, Result result) {
        log.info("TPMActivityObj flow complete call back arg : {}", JSON.toJSONString(arg));
        if (tpm2Service.isTPM2Tenant(Integer.valueOf(actionContext.getTenantId()))) {
            IObjectData activity = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), arg.getDataId(), arg.getDescribeApiName());
            String lifeStatus = activity.get(CommonFields.LIFE_STATUS, String.class);
            long startTime = activity.get(TPMActivityFields.BEGIN_DATE, Long.class);
            long endTime = activity.get(TPMActivityFields.END_DATE, Long.class);
            Map<String, Object> updateMap = new HashMap<>();
            String status;
            long now = System.currentTimeMillis();
            if (startTime > now) {
                status = TPMActivityFields.ACTIVITY_STATUS__SCHEDULE;
            } else if (now <= endTime) {
                status = TPMActivityFields.ACTIVITY_STATUS__IN_PROGRESS;
            } else {
                status = TPMActivityFields.ACTIVITY_STATUS__END;
            }
            if ("reject".equals(arg.getStatus()) || "cancel".equals(arg.getStatus())) {
                if (ObjectLifeStatus.INEFFECTIVE.getCode().equals(lifeStatus)) {
                    status = TPMActivityFields.ACTIVITY_STATUS__INEFFECTIVE;
                }
            }
            updateMap.put(TPMActivityFields.ACTIVITY_STATUS, status);
            serviceFacade.updateWithMap(User.systemUser(actionContext.getTenantId()), activity, updateMap);
        }
        return super.after(arg, result);
    }
}

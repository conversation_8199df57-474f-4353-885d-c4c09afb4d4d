package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.tpm.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.apiname.CommonFields;
import com.facishare.crm.fmcg.tpm.apiname.TPMActivityProofAuditDetailFields;
import com.facishare.crm.fmcg.tpm.apiname.TPMActivityProofDetailFields;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/4/21 下午6:23
 */
public class TPMActivityItemCostStandardObjBulkInvalidAction extends StandardBulkInvalidAction {
    @Override
    protected void before(Arg arg) {
        validateRelated(arg);
        super.before(arg);
    }

    private void validateRelated(Arg arg) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter idListFilter = new Filter();
        idListFilter.setFieldName(TPMActivityProofAuditDetailFields.ACTIVITY_ITEM_COST_STANDARD_ID);
        idListFilter.setOperator(Operator.IN);
        idListFilter.setFieldValues(arg.getDataIds());

        Filter deletedFilter = new Filter();
        deletedFilter.setFieldName(CommonFields.IS_DELETED);
        deletedFilter.setOperator(Operator.EQ);
        deletedFilter.setFieldValues(Lists.newArrayList("false"));

        query.setFilters(Lists.newArrayList(idListFilter, deletedFilter));

        List<IObjectData> auditDetails = CommonUtils.queryData(serviceFacade, User.systemUser(actionContext.getTenantId()), ApiNames.TPM_ACTIVITY_PROOF_AUDIT_DETAIL_OBJ, query);

        if (!auditDetails.isEmpty()) {

            List<String> costStandardIds = auditDetails.stream().map(record -> (String) record.get(TPMActivityProofAuditDetailFields.ACTIVITY_ITEM_COST_STANDARD_ID)).distinct().collect(Collectors.toList());
            List<IObjectData> data = serviceFacade.findObjectDataByIds(actionContext.getUser().getTenantId(), costStandardIds, ApiNames.TPM_ACTIVITY_ITEM_COST_STANDARD);

            String names = data.stream().map(IObjectData::getName).collect(Collectors.joining(","));
            throw new ValidateException(String.format(I18N.text(I18NKeys.THOSE_COST_STANDARD_RELATED_BY_AUDIT_DETAIL), names));
        }


        query.getFilters().clear();
        Filter idListFilter2 = new Filter();
        idListFilter2.setFieldName(TPMActivityProofDetailFields.ACTIVITY_ITEM_COST_STANDARD_ID);
        idListFilter2.setOperator(Operator.IN);
        idListFilter2.setFieldValues(arg.getDataIds());


        query.setFilters(Lists.newArrayList(idListFilter2, deletedFilter));

        List<IObjectData> proofDetails = CommonUtils.queryData(serviceFacade, User.systemUser(actionContext.getTenantId()), ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ, query);

        if (!proofDetails.isEmpty()) {

            List<String> costStandardIds = proofDetails.stream().map(record -> (String) record.get(TPMActivityProofDetailFields.ACTIVITY_ITEM_COST_STANDARD_ID)).distinct().collect(Collectors.toList());
            List<IObjectData> data = serviceFacade.findObjectDataByIds(actionContext.getUser().getTenantId(), costStandardIds, ApiNames.TPM_ACTIVITY_ITEM_COST_STANDARD);

            String names = data.stream().map(IObjectData::getName).collect(Collectors.joining(","));
            throw new ValidateException(String.format(I18N.text(I18NKeys.THOSE_COST_STANDARD_RELATED_BY_PROOF_DETAIL), names));
        }
    }
}

package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.api.visit.ActivityProofDTO;
import com.facishare.crm.fmcg.tpm.api.visit.ActivityProofDetailDTO;
import com.facishare.crm.fmcg.tpm.api.visit.ActivityProofImageDTO;
import com.facishare.crm.fmcg.tpm.api.visit.VisitActionDataDTO;
import com.facishare.crm.fmcg.tpm.apiname.*;
import com.facishare.crm.fmcg.tpm.service.BuryModule;
import com.facishare.crm.fmcg.tpm.service.BuryOperation;
import com.facishare.crm.fmcg.tpm.service.BuryService;
import com.facishare.crm.fmcg.tpm.service.CheckinService;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fmcg.framework.http.PaasDataProxy;
import com.fmcg.framework.http.contract.paas.data.PaasAddTeamMember;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2020/11/12 2:47 PM
 */
@SuppressWarnings("Duplicates")
public class TPMActivityProofAuditObjEditAction extends StandardEditAction {

    private static final CheckinService checkinService = SpringUtil.getContext().getBean(CheckinService.class);

    private static final PaasDataProxy paasDataProxy = SpringUtil.getContext().getBean(PaasDataProxy.class);

    @Override
    protected void before(Arg arg) {
        String auditStatus = (String) arg.getObjectData().get(TPMActivityProofAuditFields.AUDIT_STATUS);
        if (!auditStatus.equals(TPMActivityProofAuditFields.AUDIT_STATUS__PASS) &&
                !auditStatus.equals(TPMActivityProofAuditFields.AUDIT_STATUS__REJECT)) {
            throw new ValidateException("audit status can not be null");
        }

        /*if (arg.getObjectData().get(TPMActivityProofAuditFields.AUDITOR) == null ) {
            arg.getObjectData().put(TPMActivityProofAuditFields.AUDITOR, Lists.newArrayList(actionContext.getUser().getUserId()));
        }*/
        if (arg.getObjectData().get(TPMActivityProofAuditFields.DEALER_ACTIVITY_COST_ID) != null) {
            throw new ValidateException(I18N.text(I18NKeys.AUDIT_CAN_NOT_EDIT_DUE_TO_RELATE_COST));
        }
        super.before(arg);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        String activityProofId = (String) result.getObjectData().get(TPMActivityProofAuditFields.ACTIVITY_PROOF_ID);
        IObjectData activityProofData = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), activityProofId, ApiNames.TPM_ACTIVITY_PROOF_OBJ);
        activityProofData.set(TPMActivityProofFields.AUDIT_STATUS, result.getObjectData().get(TPMActivityProofAuditFields.AUDIT_STATUS));
        if(!Strings.isNullOrEmpty((String)result.getObjectData().get(TPMActivityProofAuditFields.RANDOM_AUDIT_STATUS))){
            activityProofData.set(TPMActivityProofFields.RANDOM_AUDIT_STATUS, result.getObjectData().get(TPMActivityProofAuditFields.RANDOM_AUDIT_STATUS));
        }
        serviceFacade.updateObjectData(User.systemUser(actionContext.getTenantId()), activityProofData);

        String visitId = (String) result.getObjectData().get(TPMActivityProofAuditFields.VISIT_ID);
        String actionId = (String) result.getObjectData().get(TPMActivityProofAuditFields.ACTION_ID);
        String storeId = (String) result.getObjectData().get(TPMActivityProofAuditFields.STORE_ID);

        if (!Strings.isNullOrEmpty(visitId) && !Strings.isNullOrEmpty(actionId)) {
            VisitActionDataDTO data = new VisitActionDataDTO();
            List<IObjectData> masterList = queryProof(actionContext, storeId, visitId, actionId);
            List<String> masterIds = masterList.stream().map(DBRecord::getId).collect(Collectors.toList());
            List<String> activityIds = masterList.stream().map(master -> (String) master.get(TPMActivityProofAuditFields.ACTIVITY_ID)).collect(Collectors.toList());
            Map<String, IObjectData> activityMap = queryActivity(actionContext, activityIds).stream().collect(Collectors.toMap(DBRecord::getId, v -> v));
            Map<String, List<IObjectData>> detailsMap = queryActivityProofDetails(actionContext, masterIds).stream().collect(Collectors.groupingBy(detail -> (String) detail.get(TPMActivityProofAuditDetailFields.ACTIVITY_PROOF_AUDIT_ID)));
            Map<String, String> itemNameMap = queryActivityItem(actionContext).stream().collect(Collectors.toMap(DBRecord::getId, IObjectData::getName));
            data.setActivityProofList(Lists.newArrayList());
            for (IObjectData master : masterList) {
                ActivityProofDTO datum = new ActivityProofDTO();
                datum.setProofId(master.getId());
                datum.setRemark((String) master.get(TPMActivityProofAuditFields.OPINION));
                datum.setAuditStatus((String) master.get(TPMActivityProofAuditFields.AUDIT_STATUS));
                datum.setOpinion((String) master.get(TPMActivityProofAuditFields.OPINION));
                if (!Strings.isNullOrEmpty((String) master.get(TPMActivityProofAuditFields.RANDOM_AUDIT_STATUS))) {
                    datum.setRandomAuditStatus((String) master.get(TPMActivityProofAuditFields.RANDOM_AUDIT_STATUS));
                }
                String activityId = (String) master.get(TPMActivityProofAuditFields.ACTIVITY_ID);
                if (activityMap.containsKey(activityId)) {
                    datum.setActivityName(activityMap.get(activityId).getName());
                }
                datum.setImages(JSON.parseArray(JSON.toJSONString(master.get(TPMActivityProofAuditFields.PROOF_IMAGES)), ActivityProofImageDTO.class));
                datum.setImagesTotalCount(datum.getImages().size());
                datum.setDetails(Lists.newArrayList());
                if (detailsMap.containsKey(master.getId())) {
                    List<IObjectData> details = detailsMap.get(master.getId());
                    for (IObjectData detail : details) {
                        ActivityProofDetailDTO detailDatum = new ActivityProofDetailDTO();
                        String activityItemId = (String) detail.get(TPMActivityProofAuditDetailFields.ACTIVITY_ITEM_ID);
                        detailDatum.setName(itemNameMap.getOrDefault(activityItemId, "--"));
                        detailDatum.setAmount((String) detail.get(TPMActivityProofAuditDetailFields.AMOUNT));
                        detailDatum.setSubtotal((String) detail.get(TPMActivityProofAuditDetailFields.AUDIT_SUBTOTAL));
                        datum.getDetails().add(detailDatum);
                    }
                }
                data.getActivityProofList().add(datum);
            }
            data.setActivityProofListSize(data.getActivityProofList().size());
            String updateActionResult = checkinService.updateProofAuditAction(actionContext.getUser(), visitId, actionId, data);
            result.getObjectData().put("__update_action_result", updateActionResult);
        }
        BuryService.asyncTpmLog(Integer.valueOf(actionContext.getTenantId()), Integer.valueOf(actionContext.getUser().getUserIdOrOutUserIdIfOutUser()), BuryModule.TPM.TPM_ACTIVITY_PROOF_AUDIT, BuryOperation.UPDATE);

        List<String> teamMemberIds = Lists.newArrayList();
        if (!CollectionUtils.isEmpty((List) result.getObjectData().get(TPMActivityProofAuditFields.AUDITOR))) {
            ((List) result.getObjectData().get(TPMActivityProofAuditFields.AUDITOR)).forEach(v -> {
                if (!v.toString().equals("-10000")) teamMemberIds.add(v.toString());
            });
        }
        if (result.getObjectData().get(TPMActivityProofAuditFields.INSPECTOR) != null &&
                !CollectionUtils.isEmpty((List) result.getObjectData().get(TPMActivityProofAuditFields.INSPECTOR))) {
            ((List) result.getObjectData().get(TPMActivityProofAuditFields.INSPECTOR)).forEach(v -> teamMemberIds.add(v.toString()));
        }
        if (!CollectionUtils.isEmpty(teamMemberIds))
            addTeamMember(ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ, Lists.newArrayList(result.getObjectData().getId()), teamMemberIds);
        return super.after(arg, result);
    }

    private void addTeamMember(String apiName, List<String> dataIds, List<String> userIds) {
        PaasAddTeamMember.Arg addTeamMemberArg = new PaasAddTeamMember.Arg();
        addTeamMemberArg.setDataIds(dataIds);
        addTeamMemberArg.setOutTeamMemberEmployee(Lists.newArrayList());
        addTeamMemberArg.setOtherObjects(Lists.newArrayList());
        addTeamMemberArg.setTemMemberRole("");
        addTeamMemberArg.setTeamMemberRoleList(Lists.newArrayList("4"));
        addTeamMemberArg.setTeamMemberPermissionType("2");
        addTeamMemberArg.setTeamMemberEmployee(userIds);
        PaasAddTeamMember.Result result = paasDataProxy.addTeamMember(Integer.parseInt(actionContext.getTenantId()), -10000, apiName, addTeamMemberArg);
    }

    private List<IObjectData> queryActivityItem(ActionContext context) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        //todo: 原先limit 0
        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");
        query.setFilters(Lists.newArrayList());
        return CommonUtils.queryData(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_ITEM_OBJ, query);
    }

    private List<IObjectData> queryActivityProofDetails(ActionContext context, List<String> masterIds) {

        SearchTemplateQuery query = new SearchTemplateQuery();
        //todo: 原先limit 0
        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter masterFilter = new Filter();
        masterFilter.setFieldName(TPMActivityProofAuditDetailFields.ACTIVITY_PROOF_AUDIT_ID);
        masterFilter.setOperator(Operator.IN);
        masterFilter.setFieldValues(masterIds);

        query.setFilters(Lists.newArrayList(masterFilter));
        return CommonUtils.queryData(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_PROOF_AUDIT_DETAIL_OBJ, query);
    }

    private List<IObjectData> queryActivity(ActionContext context, List<String> ids) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        //todo: 原先limit 0
        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter idFilter = new Filter();
        idFilter.setFieldName(CommonFields.ID);
        idFilter.setOperator(Operator.IN);
        idFilter.setFieldValues(ids);

        query.setFilters(Lists.newArrayList(idFilter));
        return CommonUtils.queryData(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_OBJ, query);
    }

    private List<IObjectData> queryProof(ActionContext context, String storeId, String visitId, String actionId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        //todo: 原先limit 0
        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter storeIdFilter = new Filter();
        storeIdFilter.setFieldName(TPMActivityProofAuditFields.STORE_ID);
        storeIdFilter.setOperator(Operator.EQ);
        storeIdFilter.setFieldValues(Lists.newArrayList(storeId));

        Filter visitIdFilter = new Filter();
        visitIdFilter.setFieldName(TPMActivityProofAuditFields.VISIT_ID);
        visitIdFilter.setOperator(Operator.EQ);
        visitIdFilter.setFieldValues(Lists.newArrayList(visitId));

        Filter actionIdFilter = new Filter();
        actionIdFilter.setFieldName(TPMActivityProofAuditFields.ACTION_ID);
        actionIdFilter.setOperator(Operator.EQ);
        actionIdFilter.setFieldValues(Lists.newArrayList(actionId));

        query.setFilters(Lists.newArrayList(storeIdFilter, actionIdFilter, visitIdFilter));

        return CommonUtils.queryData(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ, query);
    }
}

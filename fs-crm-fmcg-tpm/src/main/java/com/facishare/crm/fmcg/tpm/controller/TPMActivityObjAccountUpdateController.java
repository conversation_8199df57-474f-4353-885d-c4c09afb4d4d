package com.facishare.crm.fmcg.tpm.controller;

import com.beust.jcommander.internal.Lists;
import com.facishare.crm.fmcg.tpm.api.activity.AccountUpdate;
import com.facishare.crm.fmcg.tpm.apiname.ApiNames;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.metadata.exception.MetaDataException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.ActionContextUtil;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fmcg.framework.http.PaasDataProxy;
import com.fmcg.framework.http.contract.paas.data.PaasDataEdit;
import com.fxiaoke.common.concurrent.DynamicExecutors;
import com.github.autoconf.ConfigFactory;
import com.github.trace.executor.MonitorTaskWrapper;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import de.lab4inf.math.util.Strings;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.util.ResourceUtils;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadPoolExecutor;

@SuppressWarnings("Duplicates")
public class TPMActivityObjAccountUpdateController extends PreDefineController<AccountUpdate.Arg, AccountUpdate.Result> {

    private final PaasDataProxy paasDataProxy = SpringUtil.getContext().getBean(PaasDataProxy.class);

    public static final int DEFAULT_CORE_POOL_SIZE = 5;
    public static final int DEFAULT_MAXIMUM_POOL_SIZE = 10;
    public static final int DEFAULT_KEEP_ALIVE_TIME = 2000;

    private static final ThreadPoolExecutor executor = DynamicExecutors.newThreadPool(
            DEFAULT_CORE_POOL_SIZE,
            DEFAULT_MAXIMUM_POOL_SIZE,
            DEFAULT_KEEP_ALIVE_TIME, new ThreadFactoryBuilder()
                    .setDaemon(true)
                    .setNameFormat("fmcg-yq-data-update-thread-%s")
                    .build());

    static {
        ConfigFactory.getConfig("fs-fmcg-framework-config",
                conf -> {
                    executor.setCorePoolSize(conf.getInt("fmcg.yq.data_update.thread.core", DEFAULT_CORE_POOL_SIZE));
                    executor.setMaximumPoolSize(conf.getInt("fmcg.yq.data_update.thread.max", DEFAULT_MAXIMUM_POOL_SIZE));
                });
    }

    @Override
    protected AccountUpdate.Result doService(AccountUpdate.Arg arg) {
        List<String> data = loadFileData(arg.getName());
        for (String datum : data) {
            executor.submit(MonitorTaskWrapper.wrap(() -> doUpdate(datum)));
        }
        return new AccountUpdate.Result();
    }

    private void doUpdate(String datum) {
        String[] datumArr = datum.split(",");
        if (datumArr.length != 8) {
            log.info("[YQUAL] edit account error - {}", datum);
            return;
        }

        String accountNo = datumArr[0];
        try {
            String id = findAccountId(accountNo);
            if (Strings.isNullOrEmpty(id)) {
                log.info("[YQUAL] edit account error, can not find account - {}", datum);
                return;
            }

            String environmental = datumArr[1];
            String environmental2 = datumArr[2];
            String businessType = datumArr[3];
            String businessType2 = datumArr[4];
            String isNotQualified = datumArr[5];
            String notQualifiedIssueDescription = datumArr[6];
            String ifForbid = datumArr[7];

            PaasDataEdit.Arg editArg = new PaasDataEdit.Arg();
            Map<String, Object> accountData = new HashMap<>();
            accountData.put("_id", id);

            if (!Strings.isNullOrEmpty(environmental)) {
                accountData.put("environmental__c", environmental);
            }

            if (!Strings.isNullOrEmpty(environmental2)) {
                accountData.put("environmental2__c", environmental2);
            }

            if (!Strings.isNullOrEmpty(businessType)) {
                accountData.put("business_type__c", businessType);
            }

            if (!Strings.isNullOrEmpty(businessType2)) {
                accountData.put("business_type2__c", businessType2);
            }

            if (!Strings.isNullOrEmpty(isNotQualified)) {
                accountData.put("is_not_qualified__c", isNotQualified);
            }

            if (!Strings.isNullOrEmpty(notQualifiedIssueDescription)) {
                accountData.put("not_qualified_issue_description__c", notQualifiedIssueDescription);
            }

            if (!Strings.isNullOrEmpty(ifForbid)) {
                accountData.put("if_forbid__c", ifForbid);
            }

            editArg.setData(accountData);

            PaasDataEdit.Result editResult = paasDataProxy.edit(controllerContext.getUser().getTenantIdInt(), -10000, ApiNames.ACCOUNT_OBJ, editArg);

            if (editResult.getErrCode() != null && editResult.getErrCode() != 0) {
                log.info("[YQUAL] edit account error - datum : {}, code : {}, message : {} ", datum, editResult.getErrCode(), editResult.getErrMessage());
            } else {
                log.info("[YQUAL] update success - {} ", datum);
            }
        } catch (Exception ex) {
            log.info("[YQUAL] update account error - {} - unknown exception - {} ", datum, ex.getMessage());
        }
    }

    private List<String> loadFileData(String name) {
        try {
            File file = ResourceUtils.getFile(String.format("classpath:yq/%s.csv", name));
            return Files.readAllLines(file.toPath());
        } catch (IOException e) {
            throw new MetaDataException("read data from file cause io exception.");
        }
    }

    private String findAccountId(String accountNo) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setOffset(0);
        query.setLimit(1);
        query.setSearchSource("db");

        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);

        Filter accountNoFilter = new Filter();
        accountNoFilter.setFieldName("account_no");
        accountNoFilter.setOperator(Operator.EQ);
        accountNoFilter.setFieldValues(Lists.newArrayList(accountNo));

        query.setFilters(Lists.newArrayList(accountNoFilter));

        IActionContext context = ActionContextUtil.getNewContext(controllerContext.getTenantId());
        context.setUserId("-10000");
        context.setPrivilegeCheck(false);
        QueryResult<IObjectData> data = serviceFacade.findBySearchTemplateQueryWithFields(
                context,
                ApiNames.ACCOUNT_OBJ,
                query,
                Lists.newArrayList("_id")
        );

        if (!CollectionUtils.isEmpty(data.getData())) {
            return data.getData().get(0).get("_id", String.class);
        }
        return null;
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Collections.emptyList();
    }
}

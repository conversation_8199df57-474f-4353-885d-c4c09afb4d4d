package com.facishare.crm.fmcg.tpm.business;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.paas.metadata.api.IObjectData;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Strings;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/7/28 10:49
 */
@Component
public class StoreBusiness {

    private JSONObject dealerIdConfigMap = new JSONObject();
    private JSONObject dealerRecordTypeMap = new JSONObject();

    @PostConstruct
    void init() {
        ConfigFactory.getConfig("gray-rel-fmcg", conf -> {
            String value = conf.get("TPM_DEALER_ID_FIELD");
            if (!Strings.isNullOrEmpty(value)) {
                dealerIdConfigMap = JSON.parseObject(value);
            }
            value = conf.get("TPM_DEALER_RECORD_TYPE");
            if (!Strings.isNullOrEmpty(value)) {
                dealerRecordTypeMap = JSON.parseObject(value);
            }
        });
    }

    public String findDealerId(String tenantId, IObjectData store) {
        String specialField = dealerIdConfigMap.getString(tenantId);
        String dealerId;
        if (Strings.isNullOrEmpty(specialField)) {
            dealerId = (String) store.get("dealer_id__c");
            if (Strings.isNullOrEmpty(dealerId)) {
                dealerId = (String) store.get("dealer_id");
            }
        } else {
            dealerId = (String) store.get(specialField);
        }
        return dealerId;
    }

    public String findDealerRecordType(String tenantId) {
        return (String)dealerRecordTypeMap.getOrDefault(tenantId,"dealer__c");
    }
}

package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.api.enumeration.LogType;
import com.facishare.crm.fmcg.tpm.api.log.LogData;
import com.facishare.crm.fmcg.tpm.api.method.IdempotentArgBase;
import com.facishare.crm.fmcg.tpm.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.apiname.TPMActivityBudgetFields;
import com.facishare.crm.fmcg.tpm.business.BudgetService;
import com.facishare.crm.fmcg.tpm.business.OperateInfoService;
import com.facishare.crm.fmcg.tpm.service.*;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.metadata.util.SpringUtil;
import com.github.trace.TraceContext;


/**
 * <AUTHOR>
 * @date 2021/3/25 上午11:03
 */
public class TPMActivityBudgetObjAddAction extends StandardAddAction implements TransactionService<StandardAddAction.Arg, StandardAddAction.Result> {

    private final PackTransactionProxy packTransactionProxy = SpringUtil.getContext().getBean(PackTransactionProxyImpl.class);
    private final BudgetService budgetService = SpringUtil.getContext().getBean(BudgetService.class);
    private final OperateInfoService operateInfoService = SpringUtil.getContext().getBean(OperateInfoService.class);

    @Override
    protected Result doAct(Arg arg) {
        try {
            return packTransactionProxy.packAct(this, arg);
        } catch (Exception e) {
            budgetService.rmSaveIdempotent(actionContext);
            throw e;
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        BuryService.asyncTpmLog(Integer.parseInt(actionContext.getTenantId()), actionContext.getUser().getUserIdInt(), BuryModule.TPM.TPM_ACTIVITY_BUDGET, BuryOperation.CREATE);
        return super.after(arg, result);
    }

    @Override
    public Result doActTransaction(Arg arg) {
        Result result = super.doAct(arg);
        ObjectDataDocument budget = result.getObjectData();
        LogData logData = LogData.builder().data(JSON.toJSONString(budget)).build();
        String logId = operateInfoService.log(actionContext.getTenantId(), LogType.ADD.value(), JSON.toJSONString(logData), actionContext.getUser().getUserId(), ApiNames.TPM_ACTIVITY_BUDGET, result.getObjectData().getId(), this.needTriggerApprovalFlow());
        budgetService.addBudgetDetail(actionContext.getTenantId(),
                actionContext.getUser().getUserId(),
                "0",
                budget.getId(),
                String.format("初期预算表新建：「%s」新建", budget.get("name")),
                Double.parseDouble(budget.getOrDefault(TPMActivityBudgetFields.AMOUNT, "0.0").toString()),
                0.0,
                Double.parseDouble(budget.getOrDefault(TPMActivityBudgetFields.AMOUNT, "0.0").toString()),
                System.currentTimeMillis(),
                String.format("LogId:%s-traceId:%s", logId, TraceContext.get().getTraceId()),
                null,
                TraceContext.get().getTraceId(),
                IdempotentArgBase.builder().idempotentKey(actionContext.getPostId() + ":" + result.getObjectData().getId()).build()
        );
        return result;
    }
}

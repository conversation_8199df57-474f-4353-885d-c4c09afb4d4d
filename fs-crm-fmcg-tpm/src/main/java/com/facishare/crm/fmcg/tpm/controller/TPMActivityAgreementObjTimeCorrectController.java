package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Maps;
import com.facishare.crm.fmcg.tpm.api.agreement.Transfer;
import com.facishare.crm.fmcg.tpm.apiname.ApiNames;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.DateUtil;
import com.google.common.collect.Lists;
import de.lab4inf.math.util.Strings;
import groovy.util.logging.Slf4j;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * create by liguangtian
 */
@Slf4j
@SuppressWarnings("all")
public class TPMActivityAgreementObjTimeCorrectController extends PreDefineController<Transfer.Arg, Transfer.Result> {

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList();
    }

    private static final int DEFAULT_LIMIT = 100;

    /**
     * transfer agreement data for ZHG
     * 1. load all data from tenant A
     * TPMActivityEnableListController#queryProof
     * 2. convert data to tenant B object
     * 3. save tenant B object data
     * serviceFacade.saveObjectData(User.systemUser(actionContext.getTenantId()), targetObjects);
     */
    @Override
    protected Transfer.Result doService(Transfer.Arg arg) {

        String tenantId = "722872";
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setOffset(0);
        query.setLimit(DEFAULT_LIMIT);
        query.setSearchSource("db");

        if (Strings.isNullOrEmpty(arg.getId())) {
            return new Transfer.Result();
        } else if ("all".equals(arg.getId())) {

            Filter transferFilter = new Filter();
            transferFilter.setFieldName("is_transfer_data__c");
            transferFilter.setOperator(Operator.EQ);
            transferFilter.setFieldValues(Lists.newArrayList(Boolean.TRUE.toString()));


            query.setFilters(Lists.newArrayList(transferFilter));
        } else {

            Filter idFilter = new Filter();
            idFilter.setFieldName("_id");
            idFilter.setOperator(Operator.EQ);
            idFilter.setFieldValues(Lists.newArrayList(arg.getId()));

            Filter transferFilter = new Filter();
            transferFilter.setFieldName("is_transfer_data__c");
            transferFilter.setOperator(Operator.EQ);
            transferFilter.setFieldValues(Lists.newArrayList(Boolean.TRUE.toString()));

            query.setFilters(Lists.newArrayList(idFilter, transferFilter));
        }

        OrderBy order = new OrderBy();
        order.setFieldName("create_time");
        order.setIsAsc(false);
        order.setIsNullLast(false);

        query.setOrders(Lists.newArrayList(order));

        List<IObjectData> data;
        while (!((data = serviceFacade.findBySearchQuery(User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, query).getData()).isEmpty())) {
            log.info("[AT] query arg : {}, query result : {}", JSON.toJSONString(query), JSON.toJSONString(data));

            if (data.isEmpty()) {
                break;
            }

            for (IObjectData datum : data) {
                try {
                    Long end = datum.get("end_date", Long.class);
                    if (Objects.isNull(end)) {
                        continue;
                    }
                    long startTimeStampOfEndDate = DateUtil.getDayStartTime(new Date(end)).getTime();
                    if (end != startTimeStampOfEndDate) {
                        continue;
                    }
                    long correctEnd = end + 86400000L - 1;

                    Map<String, Object> updater = Maps.newHashMap();
                    updater.put("end_date", correctEnd);
                    serviceFacade.updateWithMap(User.systemUser(tenantId), datum, updater);

                } catch (Exception ex) {
                    log.error("Correct end date cause unknown exception : ", ex);
                }
            }
            query.setOffset(query.getOffset() + DEFAULT_LIMIT);
        }
        return new Transfer.Result();
    }
}

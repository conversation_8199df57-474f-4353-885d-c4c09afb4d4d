package com.facishare.crm.fmcg.tpm.common;

import com.fxiaoke.common.release.GrayRelease;
import lombok.experimental.UtilityClass;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/7/31 14:11
 */
@UtilityClass
public class TPMGrayUtils {

    public boolean isYinLu(String tenantId) {
        return GrayRelease.isAllow("fmcg", "YINLU_TPM", tenantId);
    }

    public boolean isHaoLiYou(String tenantId) {
        return GrayRelease.isAllow("fmcg", "HAOLIYOU_TPM", tenantId);
    }

    public boolean newActivityEnableCheck(String tenantId) {
        return GrayRelease.isAllow("fmcg", "TPM_NEW_ACTIVITY_ENABLE_CHECK", tenantId);
    }

    public boolean agreementNotRelatedToActivity(String tenantId) {
        return Boolean.TRUE.equals(GrayRelease.isAllow("fmcg", "TPM_AGREEMENT_NOT_RELATED_TO_ACTIVITY", tenantId));
    }

    public boolean allowAddDealerActivityCostOnWeb(String tenantId) {
        return Boolean.TRUE.equals(GrayRelease.isAllow("fmcg", "TPM_ALLOW_ADD_DEALER_ACTIVITY_COST_ON_WEB", tenantId));
    }

    public boolean proofDataTypeAllUseExistOrNot(String tenantId) {
        return Boolean.TRUE.equals(GrayRelease.isAllow("fmcg", "TPM_PROOF_DATA_TYPE_ALL_USE_EXIST_OR_NOT", tenantId));
    }

    public boolean customProofFrequencyValidate(String tenantId) {
        return Boolean.TRUE.equals(GrayRelease.isAllow("fmcg", "TPM_CUSTOM_PROOF_FREQUENCY_VALIDATE", tenantId));
    }

    public boolean customCostValidate(String tenantId) {
        return Boolean.TRUE.equals(GrayRelease.isAllow("fmcg", "TPM_CUSTOM_COST_VALIDATE", tenantId));
    }

    public boolean customAllowStoreCountValidate(String tenantId) {
        return Boolean.TRUE.equals(GrayRelease.isAllow("fmcg", "TPM_CUSTOM_STORE_COUNT_VALIDATE", tenantId));
    }

    public boolean dealerProofEnable(String tenantId) {
        return Boolean.TRUE.equals(GrayRelease.isAllow("fmcg", "TPM_DEALER_PROOF_ENABLE", tenantId));
    }

    public boolean denyDepartmentFilterOnActivity(String tenantId) {
        return Boolean.TRUE.equals(GrayRelease.isAllow("fmcg", "TPM_DENY_DEPARTMENT_FILTER_ON_ACTIVITY", tenantId));
    }

    public boolean skipEnableCheck(String tenantId) {
        return Boolean.TRUE.equals(GrayRelease.isAllow("fmcg", "TPM_SKIP_ENABLE_CHECK", tenantId));
    }

    public boolean skiProofCountCheckOnWriteOffAction(String tenantId) {
        return Boolean.TRUE.equals(GrayRelease.isAllow("fmcg", "TPM_SKIP_PROOF_COUNT_CHECK_ON_WRITE_OFF_ACTION", tenantId));
    }

    public boolean skipProofAddTransaction(String tenantId) {
        return Boolean.TRUE.equals(GrayRelease.isAllow("fmcg", "SKIP_PROOF_ADD_TRANSACTION", tenantId));
    }

    public boolean skipProofAddAgreementTimeCheck(String tenantId) {
        return Boolean.TRUE.equals(GrayRelease.isAllow("fmcg", "SKIP_PROOF_ADD_AGREEMENT_TIME_CHECK", tenantId));
    }

    public boolean excessDeductionForCost(String tenantId) {
        return Boolean.TRUE.equals(GrayRelease.isAllow("fmcg", "EXCESS_DEDUCTION_FOR_COST", tenantId));
    }

    public boolean skipDeletedBudgetWhenCalculating(String tenantId) {
        return Boolean.TRUE.equals(GrayRelease.isAllow("fmcg", "SKIP_DELETED_BUDGET_WHEN_CALCULATING", tenantId));
    }

    public boolean budgetTransferInSupportNegative(String tenantId) {
        return Boolean.TRUE.equals(GrayRelease.isAllow("fmcg", "BUDGET_TRANSFER_IN_SUPPORT_NEGATIVE", tenantId));
    }

    public boolean isSupportOverrideAdjustTime(String tenantId) {
        return Boolean.TRUE.equals(GrayRelease.isAllow("fmcg", "IS_SUPPORT_OVERRIDE_ADJUST_TIME", tenantId));
    }

    public boolean isYuanQi(String tenantId) {
        return Boolean.TRUE.equals(GrayRelease.isAllow("fmcg", "YUANQI", tenantId));
    }

    public boolean isSupportCloseActivityPrivileges(String tenantId) {
        return Boolean.TRUE.equals(GrayRelease.isAllow("fmcg", "IS_SUPPORT_CLOSE_ACTIVITY_PRIVILEGES", tenantId));
    }

    public boolean isAllowProcessActivityClose(String tenantId) {
        return Boolean.TRUE.equals(GrayRelease.isAllow("fmcg", "IS_ALLOW_PROCESS_ACTIVITY_CLOSE", tenantId));
    }

    public boolean disableBudgetAmountJudge(String tenantId) {
        return Boolean.TRUE.equals(GrayRelease.isAllow("fmcg", "DISABLE_BUDGET_AMOUNT_JUDGE", tenantId));
    }

    public boolean isAsyncSaveCheckinProofData(String tenantId) {
        return Boolean.TRUE.equals(GrayRelease.isAllow("fmcg", "IS_ASYNC_SAVE_CHECKIN_PROOF_DATA", tenantId));
    }

    public boolean isYinLuEnableList(String tenantId){
        return Boolean.TRUE.equals(GrayRelease.isAllow("fmcg", "IS_YINLU_ENABLE_LIST", tenantId));
    }

    public static boolean TPMUseEs(String tenantId) {
        return Boolean.TRUE.equals(GrayRelease.isAllow("fmcg", "TPM_USE_ES_QUERY", tenantId));
    }
    public static boolean dealerCostSupportFlowLayout(String tenantId) {
        return Boolean.TRUE.equals(GrayRelease.isAllow("fmcg", "DEALER_COST_SUPPORT_FLOW_LAYOUT", tenantId));
    }
}

package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.excel.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 12/23/20 8:42 PM
 */
@Slf4j
@SuppressWarnings("Duplicates")
public class TPMActivityItemCostStandardObjListHeaderController extends StandardListHeaderController {

    @Override
    protected List<String> getAuthorizedFields() {
        List<String> list = super.getAuthorizedFields();
        list.remove("store_range");
        return list;
    }

}
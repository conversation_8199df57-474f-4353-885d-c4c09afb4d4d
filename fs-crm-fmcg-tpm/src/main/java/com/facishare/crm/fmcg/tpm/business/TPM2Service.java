package com.facishare.crm.fmcg.tpm.business;

import com.facishare.crm.fmcg.tpm.business.abstraction.ITPM2Service;
import com.fmcg.framework.http.FmcgServiceProxy;
import com.fmcg.framework.http.contract.fmcgservice.GetLicense;
import com.github.jedis.support.MergeJedisCmd;
import org.springframework.stereotype.Component;
import redis.clients.jedis.params.SetParams;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2021/12/15 下午6:11
 */
@Component
public class TPM2Service implements ITPM2Service {


    @Resource
    private FmcgServiceProxy fmcgServiceProxy;

    @Resource(name = "redisCmd")
    private MergeJedisCmd redisCmd;

    private static final String TPM_2_REDIS_KEY = "TPM2:%s";

    @Override
    public boolean isTPM2Tenant(Integer tenantId) {
        String key = String.format(TPM_2_REDIS_KEY, tenantId);
        String value = redisCmd.get(key);
        if (value == null) {
            GetLicense.Arg arg = new GetLicense.Arg();
            arg.setAppCode("FMCG.TPM.2");
            GetLicense.Result result = fmcgServiceProxy.getLicense(tenantId, -10000, arg);
            value = String.valueOf(result.getLicense() != null);
            redisCmd.set(key,value, SetParams.setParams().nx().ex(60L));
        }
        return Boolean.TRUE.equals(Boolean.valueOf(value));
    }
}

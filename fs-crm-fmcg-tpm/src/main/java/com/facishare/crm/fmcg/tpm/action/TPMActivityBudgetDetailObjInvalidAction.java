package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.tpm.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.apiname.CommonFields;
import com.facishare.crm.fmcg.tpm.apiname.TPMActivityBudgetDetailFields;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;

@SuppressWarnings("Duplicates")
public class TPMActivityBudgetDetailObjInvalidAction extends StandardInvalidAction {

    @Override
    protected void before(Arg arg) {
        IObjectData data = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), arg.getObjectDataId(), ApiNames.TPM_ACTIVITY_BUDGET_DETAIL_OBJ);
        String budgetId = data.get(TPMActivityBudgetDetailFields.BUDGET_TABLE_ID, String.class);
        if (!isBudgetDeleted(budgetId)) {
            throw new ValidateException("预算表未作废，无法手动作废明细。");
        }
        super.before(arg);
    }

    private boolean isBudgetDeleted(String budgetId) {

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter idFilter = new Filter();
        idFilter.setFieldName(CommonFields.ID);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(budgetId));

        Filter isDeletedFilter = new Filter();
        isDeletedFilter.setFieldName("is_deleted");
        isDeletedFilter.setOperator(Operator.EQ);
        isDeletedFilter.setFieldValues(Lists.newArrayList("false"));

        query.setFilters(Lists.newArrayList(idFilter, isDeletedFilter));

        return serviceFacade.findBySearchQuery(User.systemUser(actionContext.getTenantId()), ApiNames.TPM_ACTIVITY_BUDGET, query).getTotalNumber() == 0;

    }
}

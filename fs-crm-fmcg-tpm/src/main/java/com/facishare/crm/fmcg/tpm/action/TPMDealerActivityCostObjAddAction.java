package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.api.enumeration.LogType;
import com.facishare.crm.fmcg.tpm.api.log.LogData;
import com.facishare.crm.fmcg.tpm.api.method.IdempotentArgBase;
import com.facishare.crm.fmcg.tpm.apiname.*;
import com.facishare.crm.fmcg.tpm.business.BudgetService;
import com.facishare.crm.fmcg.tpm.business.OperateInfoService;
import com.facishare.crm.fmcg.tpm.common.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.service.*;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.utils.TimeUtils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/12/16 下午4:01
 */
@SuppressWarnings("Duplicates")
public class TPMDealerActivityCostObjAddAction extends StandardAddAction implements TransactionService<StandardAddAction.Arg, StandardAddAction.Result> {

    public static final Logger log = LoggerFactory.getLogger(TPMDealerActivityCostObjAddAction.class);

    private static final BudgetService budgetService = SpringUtil.getContext().getBean(BudgetService.class);

    private final PackTransactionProxy packTransactionProxy = SpringUtil.getContext().getBean(PackTransactionProxyImpl.class);

    private final OperateInfoService operateInfoService = SpringUtil.getContext().getBean(OperateInfoService.class);

    private static final String BUDGET_KEY = "BUDGET_KEY:%s";

    private static final String ACTIVITY_KEY = "ACTIVITY_KEY:%s";

    @Override
    protected void before(Arg arg) {
        validateEndDate(arg);
        validateActivity(arg);

        if (!TPMGrayUtils.skiProofCountCheckOnWriteOffAction(actionContext.getTenantId())) {
            validateHasAnyProofOrNot(arg);
            validateHasAnyProofAuditedOrNot(arg);
        } else {
            String dealerActivityId = (String) arg.getObjectData().get(TPMDealerActivityCostFields.DEALER_ACTIVITY_ID);
            if (Strings.isNullOrEmpty(dealerActivityId)) {
                String activityId = (String) arg.getObjectData().get(TPMDealerActivityCostFields.ACTIVITY_ID);
                String dealerId = (String) arg.getObjectData().get(TPMDealerActivityCostFields.DEALER_ID);
                IObjectData dealerActivity = findOrCreateDealerActivity(activityId, dealerId);
                arg.getObjectData().put(TPMDealerActivityCostFields.DEALER_ACTIVITY_ID, dealerActivity.getId());
            }
        }
        super.before(arg);
    }

    private IObjectData findOrCreateDealerActivity(String activityId, String dealerId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter activityFilter = new Filter();
        activityFilter.setFieldName(TPMDealerActivityFields.ACTIVITY_ID);
        activityFilter.setOperator(Operator.EQ);
        activityFilter.setFieldValues(Lists.newArrayList(activityId));


        Filter dealerFilter = new Filter();
        dealerFilter.setFieldName(TPMDealerActivityFields.DEALER_ID);
        dealerFilter.setOperator(Operator.EQ);
        dealerFilter.setFieldValues(Lists.newArrayList(dealerId));
        query.setFilters(Lists.newArrayList(activityFilter, dealerFilter));

        List<IObjectData> dealerActivities = serviceFacade.findBySearchQuery(User.systemUser(actionContext.getTenantId()), ApiNames.TPM_DEALER_ACTIVITY_OBJ, query).getData();
        if (dealerActivities.isEmpty()) {
            IObjectData dealerActivity = new ObjectData();
            dealerActivity.setDescribeApiName(ApiNames.TPM_DEALER_ACTIVITY_OBJ);
            dealerActivity.setTenantId(actionContext.getTenantId());
            dealerActivity.setOwner(Lists.newArrayList("-10000"));
            dealerActivity.set(TPMDealerActivityFields.DEALER_ID, dealerId);
            dealerActivity.set(TPMDealerActivityFields.ACTIVITY_ID, activityId);
            return serviceFacade.saveObjectData(User.systemUser(actionContext.getTenantId()), dealerActivity);
        } else {
            return dealerActivities.get(0);
        }
    }

    @Override
    protected Result doAct(Arg arg) {
        if (TPMGrayUtils.excessDeductionForCost(actionContext.getTenantId())) {
            try {
                tryLock();
                actionContext.setAttribute("triggerFlow", true);
                return packTransactionProxy.packAct(this, arg);
            } catch (Exception e) {
                budgetService.rmSaveIdempotent(actionContext);
                throw e;
            }
        }
        return super.doAct(arg);
    }

    private void validateHasAnyProofAuditedOrNot(Arg arg) {
        String dealerActivityId = (String) arg.getObjectData().get(TPMDealerActivityCostFields.DEALER_ACTIVITY_ID);
        String activityId = (String) arg.getObjectData().get(TPMDealerActivityCostFields.ACTIVITY_ID);
        String dealerId = (String) arg.getObjectData().get(TPMDealerActivityCostFields.DEALER_ID);
        Long startTime = (Long) arg.getObjectData().get(TPMDealerActivityCostFields.BEGIN_DATE);
        Long endTime = (Long) arg.getObjectData().get(TPMDealerActivityCostFields.END_DATE);

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(1);
        query.setOffset(0);
        query.setSearchSource("db");
        query.setNeedReturnCountNum(false);

        Filter createTimeFilter = new Filter();
        createTimeFilter.setFieldName(TPMActivityProofFields.CREATE_TIME);
        createTimeFilter.setFieldValues(Lists.newArrayList(startTime.toString(), endTime.toString()));
        createTimeFilter.setOperator(Operator.BETWEEN);

        Filter activityFilter = new Filter();
        activityFilter.setFieldName(TPMActivityProofFields.ACTIVITY_ID);
        activityFilter.setFieldValues(Lists.newArrayList(activityId));
        activityFilter.setOperator(Operator.EQ);

        Filter dealerActivityFilter = new Filter();
        dealerActivityFilter.setFieldName(TPMActivityProofFields.DEALER_ACTIVITY);
        dealerActivityFilter.setFieldValues(Lists.newArrayList(dealerActivityId));
        dealerActivityFilter.setOperator(Operator.EQ);

        Filter dealerFilter = new Filter();
        dealerFilter.setFieldName(TPMActivityProofFields.DEALER_ID);
        dealerFilter.setFieldValues(Lists.newArrayList(dealerId));
        dealerFilter.setOperator(Operator.EQ);

        Filter proofCostFilter = new Filter();
        proofCostFilter.setFieldName(TPMActivityProofFields.DEALER_ACTIVITY_COST_ID);
        proofCostFilter.setOperator(Operator.IS);
        proofCostFilter.setFieldValues(Lists.newArrayList());

        Filter proofStatusFilter = new Filter();
        proofStatusFilter.setFieldName(TPMActivityProofFields.AUDIT_STATUS);
        proofStatusFilter.setOperator(Operator.IN);
        proofStatusFilter.setFieldValues(Lists.newArrayList(Lists.newArrayList(TPMActivityProofFields.AUDIT_STATUS__REJECT, TPMActivityProofFields.AUDIT_STATUS__PASS)));

        query.setFilters(Lists.newArrayList(createTimeFilter, activityFilter, dealerActivityFilter, dealerFilter, proofCostFilter, proofStatusFilter));

        OrderBy createTimeOrder = new OrderBy();
        createTimeOrder.setFieldName(CommonFields.CREATE_TIME);
        createTimeOrder.setIsAsc(true);
        query.setOrders(Lists.newArrayList(createTimeOrder));

        List<IObjectData> proofs = serviceFacade.findBySearchQuery(User.systemUser(actionContext.getTenantId()), ApiNames.TPM_ACTIVITY_PROOF_OBJ, query).getData();

        if (0 == proofs.size()) {
            throw new ValidateException(I18N.text(I18NKeys.DO_NOT_SUPPORT_CREATE_COST_DUE_TO_NONE_OF_PROOF_HAS_BEEN_AUDIT_IN_TIME_RANGE));
        }
    }

    private void validateHasAnyProofOrNot(Arg arg) {
        String dealerActivityId = (String) arg.getObjectData().get(TPMDealerActivityCostFields.DEALER_ACTIVITY_ID);
        String activityId = (String) arg.getObjectData().get(TPMDealerActivityCostFields.ACTIVITY_ID);
        String dealerId = (String) arg.getObjectData().get(TPMDealerActivityCostFields.DEALER_ID);
        Long startTime = (Long) arg.getObjectData().get(TPMDealerActivityCostFields.BEGIN_DATE);
        Long endTime = (Long) arg.getObjectData().get(TPMDealerActivityCostFields.END_DATE);

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(1);
        query.setOffset(0);
        query.setSearchSource("db");
        query.setNeedReturnCountNum(false);

        Filter createTimeFilter = new Filter();
        createTimeFilter.setFieldName(TPMActivityProofFields.CREATE_TIME);
        createTimeFilter.setFieldValues(Lists.newArrayList(startTime.toString(), endTime.toString()));
        createTimeFilter.setOperator(Operator.BETWEEN);

        Filter activityFilter = new Filter();
        activityFilter.setFieldName(TPMActivityProofFields.ACTIVITY_ID);
        activityFilter.setFieldValues(Lists.newArrayList(activityId));
        activityFilter.setOperator(Operator.EQ);

        Filter dealerActivityFilter = new Filter();
        dealerActivityFilter.setFieldName(TPMActivityProofFields.DEALER_ACTIVITY);
        dealerActivityFilter.setFieldValues(Lists.newArrayList(dealerActivityId));
        dealerActivityFilter.setOperator(Operator.EQ);

        Filter dealerFilter = new Filter();
        dealerFilter.setFieldName(TPMActivityProofFields.DEALER_ID);
        dealerFilter.setFieldValues(Lists.newArrayList(dealerId));
        dealerFilter.setOperator(Operator.EQ);

        Filter proofCostFilter = new Filter();
        proofCostFilter.setFieldName(TPMActivityProofFields.DEALER_ACTIVITY_COST_ID);
        proofCostFilter.setOperator(Operator.IS);
        proofCostFilter.setFieldValues(Lists.newArrayList());

        query.setFilters(Lists.newArrayList(createTimeFilter, activityFilter, dealerActivityFilter, dealerFilter, proofCostFilter));

        OrderBy createTimeOrder = new OrderBy();
        createTimeOrder.setFieldName(CommonFields.CREATE_TIME);
        createTimeOrder.setIsAsc(true);
        query.setOrders(Lists.newArrayList(createTimeOrder));

        List<IObjectData> proofs = serviceFacade.findBySearchQuery(User.systemUser(actionContext.getTenantId()), ApiNames.TPM_ACTIVITY_PROOF_OBJ, query).getData();

        if (0 == proofs.size()) {
            throw new ValidateException(I18N.text(I18NKeys.NO_DATA_IN_THIS_TIME_RANGE_WHEN_CREATE_COST));
        }
    }

    private void validateActivity(Arg arg) {
        double confirmAmount = Double.parseDouble(arg.getObjectData().get(TPMDealerActivityCostFields.CONFIRMED_AMOUNT) == null || Strings.isNullOrEmpty(arg.getObjectData().get(TPMDealerActivityCostFields.CONFIRMED_AMOUNT).toString()) ? "0.0" : arg.getObjectData().get(TPMDealerActivityCostFields.CONFIRMED_AMOUNT).toString());
        if (!TPMGrayUtils.excessDeductionForCost(actionContext.getTenantId()) && confirmAmount != 0) {
            throw new ValidateException(I18N.text(I18NKeys.CAN_NOT_FILL_AUDIT_AMOUNT_WHEN_CREATING_COST));
        }
        double amount = Double.parseDouble(arg.getObjectData().getOrDefault(TPMDealerActivityCostFields.AUDITED_AMOUNT, 0.0).toString());

        String activityId = (String) arg.getObjectData().get(TPMDealerActivityCostFields.ACTIVITY_ID);
        IObjectData activity = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), activityId, ApiNames.TPM_ACTIVITY_OBJ);

        String activityBudgetTableId = activity.get(TPMActivityFields.BUDGET_TABLE, String.class);
        Double activityAmount = activity.get(TPMActivityFields.ACTIVITY_AMOUNT, Double.class);

        String closedStatus = (String) activity.get(TPMActivityFields.CLOSED_STATUS);
        if (TPMActivityFields.CLOSE_STATUS__CLOSED.equals(closedStatus)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_HAS_CLOSED_CAN_NOT_CREATE_COST));
        }

        if (!TPMGrayUtils.excessDeductionForCost(actionContext.getTenantId()) && !Strings.isNullOrEmpty(activityBudgetTableId) &&
                activityAmount != null &&
                amount > activityAmount) {
            throw new ValidateException(I18N.text(I18NKeys.AMOUNT_SHOULD_LESS_THAN_AUDIT_AMOUNT));
        }

        Long startTime = (Long) arg.getObjectData().get(TPMDealerActivityCostFields.BEGIN_DATE);
        Long endTime = (Long) arg.getObjectData().get(TPMDealerActivityCostFields.END_DATE);
        if (startTime == null || endTime == null) {
            throw new ValidateException("begin time or end time could not be empty.");
        }

        Long activityStartTime = (Long) activity.get(TPMActivityFields.BEGIN_DATE);
        Long activityEndTime = (Long) activity.get(TPMActivityFields.END_DATE);
        if (activityStartTime > endTime || activityEndTime < startTime) {
            throw new ValidateException(I18N.text(I18NKeys.NO_DATA_IN_THIS_TIME_RANGE_WHEN_CREATE_COST));
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result rst = super.after(arg, result);
        BuryService.asyncTpmLog(Integer.valueOf(actionContext.getTenantId()), actionContext.getUser().getUserIdInt(), BuryModule.TPM.TPM_DEALER_ACTIVITY_COST, BuryOperation.CREATE);
        String budgetId = actionContext.getAttribute(String.format(BUDGET_KEY, actionContext.getPostId()));
        String activityId = actionContext.getAttribute(String.format(ACTIVITY_KEY, actionContext.getPostId()));
        log.info("actionContext:{},budgetId:{},activityId:{}", actionContext, budgetId, activityId);
        if (!Strings.isNullOrEmpty(activityId)) {
            budgetService.calculateActivity(actionContext.getTenantId(), activityId);
            budgetService.calculateBudget(actionContext.getTenantId(), budgetId);
        }
        deal1(arg, result);
        return rst;

    }

    private void deal1(Arg arg, Result result) {
        Long startTime = (Long) result.getObjectData().get(TPMDealerActivityCostFields.BEGIN_DATE);
        Long endTime = (Long) result.getObjectData().get(TPMDealerActivityCostFields.END_DATE);

        String dealerId = (String) result.getObjectData().get(TPMDealerActivityCostFields.DEALER_ID);
        String activityId = (String) result.getObjectData().get(TPMDealerActivityCostFields.ACTIVITY_ID);
        String dealerActivityId = (String) arg.getObjectData().get(TPMDealerActivityCostFields.DEALER_ACTIVITY_ID);

        SearchTemplateQuery query = new SearchTemplateQuery();
        int offset = 0;
        query.setLimit(500);
        query.setOffset(offset);
        query.setSearchSource("db");

        Filter proofCreateTimeFilter = new Filter();
        proofCreateTimeFilter.setFieldName(TPMActivityProofFields.CREATE_TIME);
        proofCreateTimeFilter.setOperator(Operator.BETWEEN);
        proofCreateTimeFilter.setFieldValues(Lists.newArrayList(startTime.toString(), endTime.toString()));

        Filter proofCostFilter = new Filter();
        proofCostFilter.setFieldName(TPMActivityProofFields.DEALER_ACTIVITY_COST_ID);
        proofCostFilter.setOperator(Operator.IS);
        proofCostFilter.setFieldValues(Lists.newArrayList());

        Filter dealerActivityFilter = new Filter();
        dealerActivityFilter.setFieldName(TPMActivityProofFields.DEALER_ACTIVITY);
        dealerActivityFilter.setFieldValues(Lists.newArrayList(dealerActivityId));
        dealerActivityFilter.setOperator(Operator.EQ);


        Filter dealerIdFilter = new Filter();
        dealerIdFilter.setFieldName(TPMActivityProofFields.DEALER_ID);
        dealerIdFilter.setOperator(Operator.EQ);
        dealerIdFilter.setFieldValues(Lists.newArrayList(dealerId));

        Filter activityFilter = new Filter();
        activityFilter.setFieldName(TPMActivityProofFields.ACTIVITY_ID);
        activityFilter.setOperator(Operator.EQ);
        activityFilter.setFieldValues(Lists.newArrayList(activityId));


        Filter auditStatusFilter = new Filter();
        auditStatusFilter.setFieldName(TPMActivityProofFields.AUDIT_STATUS);
        auditStatusFilter.setFieldValues(Lists.newArrayList(TPMActivityProofFields.AUDIT_STATUS__PASS, TPMActivityProofFields.AUDIT_STATUS__REJECT));
        auditStatusFilter.setOperator(Operator.IN);

        query.setFilters(Lists.newArrayList(proofCostFilter, proofCreateTimeFilter, dealerIdFilter, activityFilter, auditStatusFilter, dealerActivityFilter));

        OrderBy createTimeOrder = new OrderBy();
        createTimeOrder.setFieldName(CommonFields.CREATE_TIME);
        createTimeOrder.setIsAsc(true);
        query.setOrders(Lists.newArrayList(createTimeOrder));
        List<IObjectData> proofs = Lists.newArrayList();
        List<IObjectData> tmpList;
        while (!(tmpList = serviceFacade.findBySearchQuery(User.systemUser(actionContext.getTenantId()), ApiNames.TPM_ACTIVITY_PROOF_OBJ, query).getData()).isEmpty()) {
            proofs.addAll(tmpList);
            offset += tmpList.size();
            query.setOffset(offset);
        }
        if (!proofs.isEmpty()) {
            query.setLimit(-1);
            query.setOffset(0);
            query.getFilters().clear();
            Filter proofIdFilter = new Filter();
            proofIdFilter.setFieldName(TPMActivityProofAuditFields.ACTIVITY_PROOF_ID);
            proofIdFilter.setOperator(Operator.IN);
            proofIdFilter.setFieldValues(proofs.stream().map(DBRecord::getId).collect(Collectors.toList()));
            query.setFilters(Lists.newArrayList(proofIdFilter));
            List<IObjectData> proofAuditList = CommonUtils.queryData(serviceFacade, User.systemUser(actionContext.getTenantId()), ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ, query);

            List<List<IObjectData>> listArr = Lists.partition(proofAuditList, 50);
            List<String> updateField = Lists.newArrayList(TPMActivityProofAuditFields.DEALER_ACTIVITY_COST_ID);
            for (List<IObjectData> list : listArr) {
                for (IObjectData datum : list) {
                    datum.set(TPMActivityProofAuditFields.DEALER_ACTIVITY_COST_ID, result.getObjectData().getId());
                }
                serviceFacade.batchUpdateByFields(User.systemUser(actionContext.getTenantId()), list, updateField);
            }

            updateField = Lists.newArrayList(TPMActivityProofFields.DEALER_ACTIVITY_COST_ID);
            listArr = Lists.partition(proofs, 50);
            for (List<IObjectData> list : listArr) {
                for (IObjectData datum : list) {
                    datum.set(TPMActivityProofFields.DEALER_ACTIVITY_COST_ID, result.getObjectData().getId());
                }
                serviceFacade.batchUpdateByFields(User.systemUser(actionContext.getTenantId()), list, updateField);
            }
        }
    }

    private void validateEndDate(Arg arg) {
        long begin = (long) arg.getObjectData().get(TPMDealerActivityCostFields.BEGIN_DATE);
        long end = TimeUtils.convertToDayEndIfTimeWasDayBegin((long) arg.getObjectData().get(TPMDealerActivityCostFields.END_DATE));
        arg.getObjectData().put(TPMDealerActivityCostFields.END_DATE, end);

        if (end <= begin) {
            throw new ValidateException(I18N.text(I18NKeys.COST_TIME_RANGE_IS_ERROR));
        }
    }

    @Override
    protected void finallyDo() {
        try {
            super.finallyDo();
        } finally {
            budgetService.unLockBudget(actionContext);
        }
    }

    @Override
    public Result doActTransaction(Arg arg) {
        Result result = super.doAct(arg);
        String tenantId = actionContext.getTenantId();

        IObjectData activity = serviceFacade.findObjectData(User.systemUser(tenantId), (String) result.getObjectData().get(TPMDealerActivityCostFields.ACTIVITY_ID), ApiNames.TPM_ACTIVITY_OBJ);
        String budgetId = activity.get(TPMActivityFields.BUDGET_TABLE, String.class);
        if (!Strings.isNullOrEmpty(budgetId)) {
            //budgetService.tryLockBudget(actionContext, budgetId);

            String userId = actionContext.getUser().getUserId();
            if (TPMGrayUtils.excessDeductionForCost(tenantId)) {
                Map<String, Double> amountMap = budgetService.calculateActivity(tenantId, activity.getId());
                double activityAmount = Double.parseDouble(activity.get(TPMActivityFields.ACTIVITY_AMOUNT, String.class, "0"));
                double actualAmount = Double.parseDouble(activity.get(TPMActivityFields.ACTIVITY_ACTUAL_AMOUNT, String.class, "0"));
                arg.getObjectData().putIfAbsent(TPMDealerActivityCostFields.CONFIRMED_AMOUNT, 0D);
                double calActivityAmount = amountMap.getOrDefault(TPMActivityFields.ACTIVITY_ACTUAL_AMOUNT, 0D) + Double.parseDouble(arg.getObjectData().getOrDefault(TPMDealerActivityCostFields.CONFIRMED_AMOUNT, "0").toString());

                if (CommonUtils.keepNDecimal(actualAmount - calActivityAmount, 3) != 0 && CommonUtils.keepNDecimal(activityAmount - calActivityAmount, 3) < 0) {
                    double incrementAmount;
                    if (activityAmount >= actualAmount) {
                        incrementAmount = calActivityAmount - activityAmount;
                    } else {
                        incrementAmount = calActivityAmount - actualAmount;
                    }

                    IObjectData lastDetail = budgetService.getLastBudgetDetail(tenantId, budgetId);
                    double beforeAmount = lastDetail.get(TPMActivityBudgetDetailFields.AMOUNT_AFTER_OPERATION, Double.class, 0D);
                    double afterAmount = beforeAmount - incrementAmount;

                    LogData logData = LogData.builder().data(JSON.toJSONString(arg)).build();
                    String logId = operateInfoService.log(tenantId, LogType.ADD.value(), JSON.toJSONString(logData), userId, ApiNames.TPM_DEALER_ACTIVITY_COST, result.getObjectData().getId(), false);


                    budgetService.addBudgetDetail(tenantId, userId,
                            "1",
                            budgetId,
                            String.format("「%s」活动方案超额核销", activity.getName()),
                            -incrementAmount,
                            beforeAmount,
                            afterAmount,
                            System.currentTimeMillis(),
                            String.format("LogId:%s-traceId:%s", logId, TraceContext.get().getTraceId()),
                            activity.getId(),
                            TraceContext.get().getTraceId(),
                            IdempotentArgBase.builder().idempotentKey(actionContext.getPostId()).build());
                }
                actionContext.setAttribute(String.format(BUDGET_KEY, actionContext.getPostId()), budgetId);
                actionContext.setAttribute(String.format(ACTIVITY_KEY, actionContext.getPostId()), activity.getId());
            }
        }

        return result;
    }

    private void tryLock(){
        IObjectData activity = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), (String) arg.getObjectData().get(TPMDealerActivityCostFields.ACTIVITY_ID), ApiNames.TPM_ACTIVITY_OBJ);
        String budgetId = activity.get(TPMActivityFields.BUDGET_TABLE, String.class);
        if (!Strings.isNullOrEmpty(budgetId)) {
            budgetService.tryLockBudget(actionContext, budgetId);
        }
    }
}

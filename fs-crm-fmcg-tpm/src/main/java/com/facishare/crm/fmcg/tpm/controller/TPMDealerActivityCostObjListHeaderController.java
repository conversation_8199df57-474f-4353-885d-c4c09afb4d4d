package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.tpm.common.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2020/11/10 4:39 PM
 */
@SuppressWarnings("Duplicates")
public class TPMDealerActivityCostObjListHeaderController extends StandardListHeaderController {

    @Override
    protected Result after(Arg arg, Result result) {
        List<JSONObject> buttons = CommonUtils.cast(result.getLayout().get("buttons"), JSONObject.class);
        if (TPMGrayUtils.allowAddDealerActivityCostOnWeb(controllerContext.getTenantId())) {
            result.getLayout().put("buttons", buttons.stream()
                    .filter(button -> !button.getString("action").equals("Import"))
                    .collect(Collectors.toList()));
        } else {
            result.getLayout().put("buttons", buttons.stream()
                    .filter(button -> !button.getString("action").equals("Add") && !button.getString("action").equals("Import"))
                    .collect(Collectors.toList()));
        }
        return super.after(arg, result);
    }
}

package com.facishare.crm.fmcg.tpm.api.proof;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

public interface Enable {

    @Data
    @ToString
    class Arg implements Serializable {

        @SerializedName("store_id")
        @JSONField(name = "store_id")
        @JsonProperty("store_id")
        private String storeId;
    }

    @Data
    @ToString
    class Result implements Serializable {

        private boolean enable = false;
    }
}
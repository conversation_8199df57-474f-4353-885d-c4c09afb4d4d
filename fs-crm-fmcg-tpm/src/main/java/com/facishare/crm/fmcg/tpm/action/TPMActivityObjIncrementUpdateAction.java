package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.tpm.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.tpm.business.BudgetService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardIncrementUpdateAction;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import de.lab4inf.math.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2021/3/9 下午7:28
 */
@SuppressWarnings("Duplicates")
public class TPMActivityObjIncrementUpdateAction extends StandardIncrementUpdateAction {

    public static Logger log = LoggerFactory.getLogger(TPMActivityObjIncrementUpdateAction.class);

    private static final List<String> ALLOW_LIST = Lists.newArrayList(TPMActivityFields.ACTIVITY_AMOUNT, "_id", "isValidate", TPMActivityFields.ACTIVITY_ACTUAL_AMOUNT);

    private static final BudgetService budgetService = SpringUtil.getContext().getBean(BudgetService.class);

    @Override
    protected void before(Arg arg) {
        if (actionContext.isFromFunction() || actionContext.isFromOpenAPI()) {
            arg.getData().keySet().forEach(key -> {
                if (!Strings.isNullOrEmpty(key) && !key.endsWith("__c") && !key.equals("_id"))
                    throw new ValidateException("不允许函数(OpenApi)更新活动方案的预置字段。");
            });
        }
        super.before(arg);
    }

}

package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.common.parallel.ParallelUtils;
import com.facishare.crm.fmcg.tpm.api.visit.ActivityProofDTO;
import com.facishare.crm.fmcg.tpm.api.visit.ActivityProofDetailDTO;
import com.facishare.crm.fmcg.tpm.api.visit.ActivityProofImageDTO;
import com.facishare.crm.fmcg.tpm.api.visit.VisitActionDataDTO;
import com.facishare.crm.fmcg.tpm.apiname.*;
import com.facishare.crm.fmcg.tpm.business.BudgetService;
import com.facishare.crm.fmcg.tpm.business.StoreBusiness;
import com.facishare.crm.fmcg.tpm.cache.ActivityItemCache;
import com.facishare.crm.fmcg.tpm.common.EasyValidator;
import com.facishare.crm.fmcg.tpm.common.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.service.*;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.utils.QueryDataUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.dto.SaveMasterAndDetailData;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.AggFunctionArg;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.ActionContextUtil;
import com.facishare.paas.metadata.util.DateUtil;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fmcg.framework.http.FmcgServiceProxy;
import com.fmcg.framework.http.contract.fmcgservice.GetTenantConfig;
import com.fxiaoke.common.release.GrayRelease;
import com.github.trace.executor.MonitorTaskWrapper;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2020/11/12 2:47 PM
 */
@SuppressWarnings("Duplicates")
public class TPMActivityProofObjAddAction extends StandardAddAction implements TransactionService<BaseObjectSaveAction.Arg, BaseObjectSaveAction.Result> {

    private static final Logger LOGGER = LoggerFactory.getLogger(TPMActivityProofObjAddAction.class);

    private static final OrganizationService organizationService = SpringUtil.getContext().getBean(OrganizationService.class);
    private static final CheckinService checkinService = SpringUtil.getContext().getBean(CheckinService.class);
    private static final FmcgServiceProxy fmcgServiceProxy = SpringUtil.getContext().getBean(FmcgServiceProxy.class);
    private static final StoreBusiness storeBusiness = SpringUtil.getContext().getBean(StoreBusiness.class);
    private static final BudgetService budgetService = SpringUtil.getContext().getBean(BudgetService.class);
    private static final ActivityItemCache activityItemCache = SpringUtil.getContext().getBean(ActivityItemCache.class);

    @Resource
    private final PackTransactionProxy packTransactionProxy = SpringUtil.getContext().getBean(PackTransactionProxyImpl.class);

    private String auditMode = "0";

    @Override
    protected void before(Arg arg) {

        // 校验外勤信息
        visitInformationValidate();
        stopWatch.lap("visitInformationValidate");

        // 获取客户信息，忽略计算以及引用字段
        IObjectData store = findStore();
        stopWatch.lap("findStore");

        // 获取活动方案信息，忽略计算以及引用字段
        IObjectData activity = findActivity();
        stopWatch.lap("findActivity");

        // 客开：校验举证频率设置
        validateCustomProofFrequency(store.getId(), activity);
        stopWatch.lap("validateCustomProofFrequency");

        // 客开：举证门店数校验
        validateCustomAllowStoreCount(store.getId(), activity);
        stopWatch.lap("validateCustomAllowStoreCount");

        // 客开：费用校验
        validateCustomCost(activity);
        stopWatch.lap("validateCustomCost");

        // 校验活动方案
        activityValidate(store, activity);
        stopWatch.lap("activityValidate");

        // 获取协议信息，忽略计算以及引用字段
        IObjectData agreement = findAgreement(activity);
        stopWatch.lap("findAgreement");

        // 校验举证日期
        dateValidate(store, activity, agreement);
        stopWatch.lap("dateValidate");

        // 定位经销商并绑定经销商活动信息
        IObjectData dealer = findDealer(store, agreement);
        stopWatch.lap("findDealer");

        IObjectData dealerActivity = findOrCreateDealerActivity(activity, dealer);
        stopWatch.lap("findOrCreateDealerActivity");

        arg.getObjectData().put(TPMActivityProofFields.DEALER_ID, dealer.getId());
        arg.getObjectData().put(TPMActivityProofFields.DEALER_ACTIVITY, dealerActivity.getId());
        arg.getObjectData().put(TPMActivityProofFields.AUDIT_STATUS, TPMActivityProofFields.AUDIT_STATUS__SCHEDULE);

        this.auditMode = getAuditMode();
        stopWatch.lap("getAuditMode");

        if ("1".equals(this.auditMode)) {
            arg.getObjectData().put(TPMActivityProofFields.AUDIT_STATUS, TPMActivityProofFields.AUDIT_STATUS__PASS);
            arg.getObjectData().put(TPMActivityProofFields.RANDOM_AUDIT_STATUS, "unchecked");
        }
        stopWatch.lap("ProofObjAddAction-before");

        super.before(arg);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        stopWatch.lap("start after");

        Result finalRst = super.after(arg, result);
        stopWatch.lap("start save checkins");

        if (TPMGrayUtils.isAsyncSaveCheckinProofData(actionContext.getTenantId())) {
            ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
            parallelTask.submit(MonitorTaskWrapper.wrap(() -> saveCheckinsAction(result)));
            parallelTask.run();
        } else {
            saveCheckinsAction(result);
        }
        stopWatch.lap("finish save checkins");

        BuryService.asyncTpmLog(Integer.valueOf(actionContext.getTenantId()), Integer.parseInt(actionContext.getUser().getUserIdOrOutUserIdIfOutUser()), BuryModule.TPM.TPM_ACTIVITY_PROOF, BuryOperation.CREATE);
        return finalRst;
    }

    @Override
    protected Result doAct(Arg arg) {
        stopWatch.lap("start doAct");
        try {
            //执行事务
            if (TPMGrayUtils.skipProofAddTransaction(actionContext.getTenantId())) {
                return doActTransaction(arg);
            } else {
                return packTransactionProxy.packAct(this, arg);
            }
        } catch (Exception e) {
            budgetService.rmSaveIdempotent(actionContext);
            throw e;
        } finally {
            stopWatch.lap("finish doAct");
        }
    }

    @Override
    public Result doActTransaction(Arg arg) {

        Result result = super.doAct(arg);
        // 自动检核
        autoAuditAction(result);
        return result;
    }

    @Override
    protected void finallyDo() {
        super.finallyDo();
        this.stopWatch.logSlow(500L);
    }

    private void saveCheckinsAction(Result result) {
        String visitId = (String) result.getObjectData().get(TPMActivityProofFields.VISIT_ID);
        String actionId = (String) result.getObjectData().get(TPMActivityProofFields.ACTION_ID);

        if (!Strings.isNullOrEmpty(visitId) && !Strings.isNullOrEmpty(actionId)) {

            String storeId = (String) result.getObjectData().get(TPMActivityProofFields.STORE_ID);
            VisitActionDataDTO data = new VisitActionDataDTO();

            List<IObjectData> masterList = queryProof(storeId, visitId, actionId);

            List<String> masterIds = masterList.stream().map(DBRecord::getId).collect(Collectors.toList());
            List<String> activityIds = masterList.stream().map(master -> (String) master.get(TPMActivityProofFields.ACTIVITY_ID)).distinct().collect(Collectors.toList());

            Map<String, IObjectData> activityMap = queryActivity(activityIds);
            Map<String, List<IObjectData>> detailsMap = queryActivityProofDetails(masterIds);
            Map<String, String> itemNameMap = queryActivityItem();

            data.setActivityProofList(Lists.newArrayList());

            for (IObjectData master : masterList) {
                ActivityProofDTO proof = new ActivityProofDTO();

                String activityId = master.get(TPMActivityProofFields.ACTIVITY_ID, String.class);
                if (activityMap.containsKey(activityId)) {
                    proof.setActivityName(activityMap.get(activityId).getName());
                }

                proof.setProofId(master.getId());
                proof.setRemark(master.get(TPMActivityProofFields.REMARK, String.class));

                List<ActivityProofImageDTO> images = JSON.parseArray(JSON.toJSONString(master.get(TPMActivityProofFields.PROOF_IMAGES)), ActivityProofImageDTO.class);

                proof.setImages(CollectionUtils.isEmpty(images) ? Lists.newArrayList() : images);
                proof.setImagesTotalCount(proof.getImages().size());

                proof.setDetails(Lists.newArrayList());
                if (detailsMap.containsKey(master.getId())) {
                    List<IObjectData> details = detailsMap.get(master.getId());
                    for (IObjectData detail : details) {
                        ActivityProofDetailDTO proofDetail = new ActivityProofDetailDTO();
                        proofDetail.setAmount((String) detail.get(TPMActivityProofDetailFields.AMOUNT));

                        String activityItemId = (String) detail.get(TPMActivityProofDetailFields.ACTIVITY_ITEM_ID);
                        proofDetail.setName(itemNameMap.getOrDefault(activityItemId, "--"));

                        proof.getDetails().add(proofDetail);
                    }
                }
                data.getActivityProofList().add(proof);
            }

            data.setActivityProofListSize(data.getActivityProofList().size());
            String updateActionResult = checkinService.updateProofAction(actionContext.getUser(), visitId, actionId, data);
            result.getObjectData().put("__update_action_result", updateActionResult);
        }
    }

    private List<IObjectData> queryProof(String storeId, String visitId, String actionId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);

        Filter storeIdFilter = new Filter();
        storeIdFilter.setFieldName(TPMActivityProofFields.STORE_ID);
        storeIdFilter.setOperator(Operator.EQ);
        storeIdFilter.setFieldValues(Lists.newArrayList(storeId));

        Filter visitIdFilter = new Filter();
        visitIdFilter.setFieldName(TPMActivityProofFields.VISIT_ID);
        visitIdFilter.setOperator(Operator.EQ);
        visitIdFilter.setFieldValues(Lists.newArrayList(visitId));

        Filter actionIdFilter = new Filter();
        actionIdFilter.setFieldName(TPMActivityProofFields.ACTION_ID);
        actionIdFilter.setOperator(Operator.EQ);
        actionIdFilter.setFieldValues(Lists.newArrayList(actionId));

        query.setFilters(Lists.newArrayList(storeIdFilter, actionIdFilter, visitIdFilter));

        return QueryDataUtil.find(
                serviceFacade,
                actionContext,
                ApiNames.TPM_ACTIVITY_PROOF_OBJ,
                query,
                Lists.newArrayList("_id", "name", TPMActivityProofFields.REMARK, TPMActivityProofFields.ACTIVITY_ID, TPMActivityProofFields.PROOF_IMAGES));
    }

    private Map<String, IObjectData> queryActivity(List<String> ids) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(200);
        query.setOffset(0);
        query.setSearchSource("db");
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);

        Filter idFilter = new Filter();
        idFilter.setFieldName(CommonFields.ID);
        idFilter.setOperator(Operator.IN);
        idFilter.setFieldValues(ids);

        query.setFilters(Lists.newArrayList(idFilter));

        return serviceFacade.findBySearchTemplateQueryWithFields(
                        ActionContextExt.of(User.systemUser(this.actionContext.getTenantId()), this.actionContext.getRequestContext()).getContext(),
                        ApiNames.TPM_ACTIVITY_OBJ,
                        query,
                        Lists.newArrayList("_id", "name")).getData().stream()
                .collect(Collectors.toMap(DBRecord::getId, v -> v, (oldOne, newOne) -> oldOne));
    }

    private Map<String, List<IObjectData>> queryActivityProofDetails(List<String> masterIds) {

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");
        query.setNeedReturnCountNum(false);

        Filter masterFilter = new Filter();
        masterFilter.setFieldName(TPMActivityProofDetailFields.ACTIVITY_PROOF_ID);
        masterFilter.setOperator(Operator.IN);
        masterFilter.setFieldValues(masterIds);

        query.setFilters(Lists.newArrayList(masterFilter));

        return QueryDataUtil.find(
                        serviceFacade,
                        actionContext,
                        ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ,
                        query,
                        Lists.newArrayList("_id", "name", TPMActivityProofDetailFields.ACTIVITY_ITEM_ID, TPMActivityProofDetailFields.AMOUNT, TPMActivityProofDetailFields.ACTIVITY_PROOF_ID)).stream()
                .collect(Collectors.groupingBy(detail -> (String) detail.get(TPMActivityProofDetailFields.ACTIVITY_PROOF_ID)));
    }

    private Map<String, String> queryActivityItem() {
        return activityItemCache.get(actionContext.getTenantId());
    }

    private void visitInformationValidate() {
        String visitId = (String) arg.getObjectData().get(TPMActivityProofFields.VISIT_ID);
        String actionId = (String) arg.getObjectData().get(TPMActivityProofFields.ACTION_ID);

        if (!GrayRelease.isAllow("fmcg", "TPM_WEB_PROOF", actionContext.getTenantId())) {
            EasyValidator.notNullOrEmpty(visitId, String.format(I18N.text(I18NKeys.CHECKINS_INFO_IS_LOST_CAN_NOT_SUPPORT_PROOF), "visit_id"));
            EasyValidator.notNullOrEmpty(actionId, String.format(I18N.text(I18NKeys.CHECKINS_INFO_IS_LOST_CAN_NOT_SUPPORT_PROOF), "action_id"));
        }

        String activityId = (String) arg.getObjectData().get(TPMActivityProofFields.ACTIVITY_ID);
        if (!Strings.isNullOrEmpty(visitId) && !Strings.isNullOrEmpty(actionId)) {
            SearchTemplateQuery query = new SearchTemplateQuery();

            query.setNeedReturnCountNum(false);
            query.setNeedReturnQuote(false);
            query.setLimit(1);
            query.setOffset(0);
            query.setSearchSource("db");

            Filter activityFilter = new Filter();
            activityFilter.setFieldName(TPMActivityProofFields.ACTIVITY_ID);
            activityFilter.setOperator(Operator.EQ);
            activityFilter.setFieldValues(Lists.newArrayList(activityId));

            Filter visitIdFilter = new Filter();
            visitIdFilter.setFieldName(TPMActivityProofFields.VISIT_ID);
            visitIdFilter.setOperator(Operator.EQ);
            visitIdFilter.setFieldValues(Lists.newArrayList(visitId));

            Filter actionIdFilter = new Filter();
            actionIdFilter.setFieldName(TPMActivityProofFields.ACTION_ID);
            actionIdFilter.setOperator(Operator.EQ);
            actionIdFilter.setFieldValues(Lists.newArrayList(actionId));

            query.setFilters(Lists.newArrayList(activityFilter, visitIdFilter, actionIdFilter));

            List<IObjectData> proofs = serviceFacade.findBySearchTemplateQueryWithFields(
                    ActionContextUtil.getNewContext(actionContext.getTenantId()),
                    ApiNames.TPM_ACTIVITY_PROOF_OBJ,
                    query,
                    Lists.newArrayList(TPMActivityProofFields.ACTIVITY_ID)).getData();

            if (!CollectionUtils.isEmpty(proofs)) {
                throw new ValidateException("举证信息已存在，请勿重复提交。");
            }
        }
    }

    private IObjectData findStore() {
        IObjectData store = serviceFacade.findObjectDataIgnoreAll(User.systemUser(actionContext.getTenantId()), (String) arg.getObjectData().get(TPMActivityProofFields.STORE_ID), ApiNames.ACCOUNT_OBJ);

        // todo : i18n support
        EasyValidator.notNull(store, "门店信息不存在");
        return store;
    }

    private IObjectData findActivity() {
        IObjectData activity = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), (String) arg.getObjectData().get(TPMActivityProofFields.ACTIVITY_ID), ApiNames.TPM_ACTIVITY_OBJ);

        // todo : i18n support
        EasyValidator.notNull(activity, "活动方案信息不存在");
        return activity;
    }

    private void validateCustomProofFrequency(String storeId, IObjectData activity) {
        if (!TPMGrayUtils.customProofFrequencyValidate(actionContext.getTenantId())) {
            return;
        }

        String frequencyType = activity.get("proof_frequency__c", String.class);
        String proofFrequencyLimitStr = activity.get("proof_frequency_limit__c", String.class);
        if (Strings.isNullOrEmpty(frequencyType) || Strings.isNullOrEmpty(proofFrequencyLimitStr)) {
            return;
        }

        BigDecimal frequencyLimit = new BigDecimal(proofFrequencyLimitStr);
        String activityId = activity.getId();

        long now = System.currentTimeMillis();

        BigDecimal count;
        long start;

        switch (frequencyType) {
            default:
            case "unlimited":
                // ignore
                break;
            case "0": // 半天

                // calculate time in millis value of today
                long dayStart = getStartTimeStampOfDay(now);

                // increase activity frequency limit to today time stamp
                long morningProofBeginTime = dayStart + activity.get("morning_proof_begin_time__c", Long.class);
                long morningProofEndTime = dayStart + activity.get("morning_proof_end_time__c", Long.class);
                long afternoonProofBeginTime = dayStart + activity.get("afternoon_proof_begin_time__c", Long.class);
                long afternoonProofEndTime = dayStart + activity.get("afternoon_proof_end_time__c", Long.class);

                // time check
                if (now >= morningProofBeginTime && now <= morningProofEndTime) {
                    start = morningProofBeginTime;
                } else if (now >= afternoonProofBeginTime && now <= afternoonProofEndTime) {
                    start = afternoonProofBeginTime;
                } else {
                    throw new ValidateException(String.format(I18N.text(I18NKeys.PROOF_FREQUENT_VALIDATE_TIME_OUT_OF_RANGE), activity.getName()));
                }

                count = countProof(storeId, activityId, start, now);
                if (count.compareTo(frequencyLimit) > -1) {
                    throw new ValidateException(String.format(I18N.text(I18NKeys.PROOF_FREQUENT_VALIDATE_HALF_DAY),
                            activity.getName(),
                            frequencyLimit));
                }
                break;
            case "1": // 一天
                start = DateUtil.getDayStartTime(new Date(now)).getTime();
                count = countProof(storeId, activityId, start, now);
                if (count.compareTo(frequencyLimit) > -1) {
                    throw new ValidateException(String.format(I18N.text(I18NKeys.PROOF_FREQUENT_VALIDATE_ONE_DAY),
                            activity.getName(),
                            frequencyLimit));
                }
                break;
            case "2": // 两天
                start = DateUtil.getDayStartTime(new Date(now - 86400000L)).getTime();
                count = countProof(storeId, activityId, start, now);
                if (count.compareTo(frequencyLimit) > -1) {
                    throw new ValidateException(String.format(I18N.text(I18NKeys.PROOF_FREQUENT_VALIDATE_TWO_DAY),
                            activity.getName(),
                            frequencyLimit));
                }
                break;
            case "3": // 一月
                start = DateUtil.getTimesThisMonthStartTime().getTime();
                count = countProof(storeId, activityId, start, now);
                if (count.compareTo(frequencyLimit) > -1) {
                    throw new ValidateException(String.format(I18N.text(I18NKeys.PROOF_FREQUENT_VALIDATE_ONE_MONTH),
                            activity.getName(),
                            frequencyLimit));
                }
                break;
            case "4": // 两月
                start = DateUtil.getLastMonthStartTime().getTime();
                count = countProof(storeId, activityId, start, now);
                if (count.compareTo(frequencyLimit) > -1) {
                    throw new ValidateException(String.format(I18N.text(I18NKeys.PROOF_FREQUENT_VALIDATE_TWO_MONTH),
                            activity.getName(),
                            frequencyLimit));
                }
                break;
            case "5": // 活动
                count = countProof(storeId, activityId);
                if (count.compareTo(frequencyLimit) > -1) {
                    throw new ValidateException(String.format(I18N.text(I18NKeys.PROOF_FREQUENT_VALIDATE_ONE_ACTIVITY),
                            activity.getName(),
                            frequencyLimit));
                }
                break;
        }
    }

    private void validateCustomAllowStoreCount(String storeId, IObjectData activity) {
        if (!TPMGrayUtils.customAllowStoreCountValidate(actionContext.getTenantId())) {
            return;
        }

        String allowStoreCountStr = activity.get("allow_store_count__c", String.class);
        if (Strings.isNullOrEmpty(allowStoreCountStr)) {
            return;
        }

        BigDecimal allowStoreCount = new BigDecimal(allowStoreCountStr);

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setSearchSource("db");

        Filter activityFilter = new Filter();
        activityFilter.setFieldName(TPMActivityProofFields.ACTIVITY_ID);
        activityFilter.setOperator(Operator.EQ);
        activityFilter.setFieldValues(Lists.newArrayList(activity.getId()));

        Filter storeFilter = new Filter();
        storeFilter.setFieldName(TPMActivityProofFields.STORE_ID);
        storeFilter.setOperator(Operator.NEQ);
        storeFilter.setFieldValues(Lists.newArrayList(storeId));

        query.setFilters(Lists.newArrayList(activityFilter, storeFilter));

        AggFunctionArg functionArg = AggFunctionArg.builder().aggFunction("count").aggField("id").build();

        List<IObjectData> aggregateResult = serviceFacade.aggregateFindBySearchQueryWithGroupFields(
                User.systemUser(actionContext.getTenantId()),
                query,
                ApiNames.TPM_ACTIVITY_PROOF_OBJ,
                Lists.newArrayList(TPMActivityProofFields.STORE_ID),
                Lists.newArrayList(functionArg)
        );

        BigDecimal totalStoreCount = BigDecimal.valueOf(aggregateResult.size());

        if (totalStoreCount.compareTo(allowStoreCount) > -1) {
            throw new ValidateException(String.format(I18N.text(I18NKeys.ACTIVITY_MAX_STORE_NUMBER_VALIDATE),
                    activity.getName(),
                    allowStoreCount.toString()));
        }
    }

    private void validateCustomCost(IObjectData activity) {
        if (!TPMGrayUtils.customCostValidate(actionContext.getTenantId())) {
            return;
        }

        BigDecimal activityTotal = activity.get(TPMActivityFields.ACTIVITY_AMOUNT, BigDecimal.class, new BigDecimal("0"));
        BigDecimal currentAmount = arg.getObjectData().toObjectData().get(TPMActivityProofFields.ACTUAL_TOTAL, BigDecimal.class, new BigDecimal("0"));

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter activityIdFilter = new Filter();
        activityIdFilter.setFieldName(TPMActivityProofFields.ACTIVITY_ID);
        activityIdFilter.setOperator(Operator.EQ);
        activityIdFilter.setFieldValues(Lists.newArrayList(activity.getId()));

        Filter deletedFilter = new Filter();
        deletedFilter.setFieldName(CommonFields.IS_DELETED);
        deletedFilter.setOperator(Operator.EQ);
        deletedFilter.setFieldValues(Lists.newArrayList("false"));

        query.setFilters(Lists.newArrayList(activityIdFilter, deletedFilter));

        List<String> aggList = Lists.newArrayList(TPMActivityProofFields.ACTIVITY_ID);

        List<IObjectData> data = serviceFacade.aggregateFindBySearchQueryWithGroupFields(
                User.systemUser(actionContext.getTenantId()),
                query,
                ApiNames.TPM_ACTIVITY_PROOF_OBJ,
                aggList,
                "sum",
                TPMActivityProofFields.ACTUAL_TOTAL);

        BigDecimal total;
        if (!CollectionUtils.isEmpty(data)) {
            String key = String.format("sum_%s", TPMActivityProofFields.ACTUAL_TOTAL);
            total = data.get(0).get(key, BigDecimal.class);
        } else {
            total = new BigDecimal("0");
        }

        LOGGER.info("validate custom cost : total - {}, current amount - {}, activity total : {}", total, currentAmount, activityTotal);

        total = total.add(currentAmount);
        if (total.compareTo(activityTotal) > 0) {
            throw new ValidateException("当前活动方案下的总举证申报费用已超过活动申请费用，无法举证。");
        }
    }

    private void activityValidate(IObjectData store, IObjectData activity) {
        String closedStatus = (String) activity.get(TPMActivityFields.CLOSED_STATUS);
        if (TPMActivityFields.CLOSE_STATUS__CLOSED.equals(closedStatus)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_HAS_CLOSED_CAN_NOT_CREATE_PROOF));
        }

        String dealerId = activity.get(TPMActivityFields.DEALER_ID, String.class);

        LOGGER.info("store id : {}, dealer id : {}", store.getId(), dealerId);

        if (!Strings.isNullOrEmpty(dealerId) && !dealerId.equals(store.getId()) && !TPMGrayUtils.isYuanQi(actionContext.getTenantId())) {
            String storeDealerId = storeBusiness.findDealerId(actionContext.getTenantId(), store);
            if (!dealerId.equals(storeDealerId)) {
                throw new ValidateException(I18N.text(I18NKeys.STORE_DEALER_CAN_NOT_MATCH_ACTIVITY_DEALER));
            }
        }

        List<String> departmentRange = CommonUtils.cast(activity.get(TPMActivityFields.DEPARTMENT_RANGE), String.class);

        //小程序 无需也没有办法校验部门
        if (!"-10000".equals(actionContext.getUser().getUserId())
                && !TPMGrayUtils.denyDepartmentFilterOnActivity(actionContext.getTenantId())
                && !actionContext.getUser().isOutUser()
                && !organizationService.employeeInRange(
                actionContext.getUser().getTenantIdInt(),
                actionContext.getUser().getUserIdInt(),
                departmentRange.stream().map(Integer::valueOf).collect(Collectors.toList()))) {
            throw new ValidateException(I18N.text(I18NKeys.EMPLOYEE_IS_NOT_WITHIN_THE_SCOPE_OF_ACTIVITY));
        }

        if (!Strings.isNullOrEmpty(dealerId) && dealerId.equals(store.getId())) {
            return;
        }

        String storeRangeJson = (String) activity.get(TPMActivityFields.STORE_RANGE);
        if (!Strings.isNullOrEmpty(storeRangeJson)) {
            JSONObject storeRange = JSON.parseObject(storeRangeJson);
            String type = storeRange.getString("type");

            switch (type) {
                case "FIXED":
                    SearchTemplateQuery fixedStoreQuery = new SearchTemplateQuery();

                    fixedStoreQuery.setLimit(1);
                    fixedStoreQuery.setOffset(0);
                    fixedStoreQuery.setNeedReturnCountNum(false);
                    fixedStoreQuery.setNeedReturnQuote(false);
                    fixedStoreQuery.setSearchSource("db");

                    Filter activityFilter = new Filter();
                    activityFilter.setFieldName(TPMActivityStoreFields.ACTIVITY_ID);
                    activityFilter.setOperator(Operator.EQ);
                    activityFilter.setFieldValues(Lists.newArrayList(activity.getId()));

                    Filter storeIdFilter = new Filter();
                    storeIdFilter.setFieldName(TPMActivityStoreFields.STORE_ID);
                    storeIdFilter.setOperator(Operator.EQ);
                    storeIdFilter.setFieldValues(Lists.newArrayList(store.getId()));

                    fixedStoreQuery.setFilters(Lists.newArrayList(activityFilter, storeIdFilter));

                    List<IObjectData> stores = serviceFacade.findBySearchTemplateQueryWithFields(
                            ActionContextExt.of(User.systemUser(this.actionContext.getTenantId()), this.actionContext.getRequestContext()).getContext(),
                            ApiNames.TPM_ACTIVITY_STORE_OBJ,
                            fixedStoreQuery,
                            Lists.newArrayList("_id")).getData();

                    if (CollectionUtils.isEmpty(stores)) {
                        throw new ValidateException(I18N.text(I18NKeys.THIS_STORE_WHICH_IS_NOT_IN_THE_RANGE_CAN_NOT_CREATE_AGREEMENT));
                    }
                    break;
                case "CONDITION":
                    SearchTemplateQuery conditionStoreQuery = new SearchTemplateQuery();

                    conditionStoreQuery.setLimit(1);
                    conditionStoreQuery.setOffset(0);
                    conditionStoreQuery.setNeedReturnCountNum(false);
                    conditionStoreQuery.setNeedReturnQuote(false);
                    conditionStoreQuery.setSearchSource("db");

                    List<Wheres> conditionWheres = new ArrayList<>();

                    String valueJson = storeRange.getString("value");
                    JSONArray wheres = JSON.parseArray(valueJson);
                    for (int whereIndex = 0; whereIndex < wheres.size(); whereIndex++) {
                        JSONObject where = wheres.getJSONObject(0);

                        Wheres conditionWhere = new Wheres();
                        conditionWhere.setConnector(where.getString("connector"));
                        conditionWhere.setFilters(Lists.newArrayList());

                        JSONArray filters = where.getJSONArray("filters");
                        for (int filterIndex = 0; filterIndex < filters.size(); filterIndex++) {
                            JSONObject filter = filters.getJSONObject(filterIndex);
                            Filter conditionFilter = new Filter();
                            conditionFilter.setFieldName(filter.getString("field_name"));
                            conditionFilter.setIndexName(filter.getString("index_name"));
                            conditionFilter.setFieldValueType(filter.getString("field_value_type"));
                            conditionFilter.setOperator(Operator.valueOf(filter.getString("operator")));
                            conditionFilter.setFieldValues(filter.getJSONArray("field_values").toJavaList(String.class));
                            conditionFilter.setConnector(filter.getString("connector"));
                            conditionFilter.setValueType(filter.getInteger("value_type"));
                            conditionFilter.setRefDescribeApiName(filter.getString("ref_describe_api_name"));
                            conditionFilter.setRefFieldApiName(filter.getString("ref_field_api_name"));
                            conditionFilter.setIsCascade(filter.getBoolean("is_cascade"));
                            conditionFilter.setIsMasterField(filter.getBoolean("is_master_field"));
                            conditionFilter.setFilterGroup(filter.getString("filterGroup"));
                            conditionWhere.getFilters().add(conditionFilter);
                        }

                        Filter idFilter = new Filter();
                        idFilter.setFieldName("_id");
                        idFilter.setOperator(Operator.EQ);
                        idFilter.setFieldValues(Lists.newArrayList(store.getId()));
                        idFilter.setConnector("AND");

                        conditionWhere.getFilters().add(idFilter);

                        conditionWheres.add(conditionWhere);
                    }
                    conditionStoreQuery.setWheres(conditionWheres);

                    List<IObjectData> conditionStores = serviceFacade.findBySearchTemplateQueryWithFields(
                            ActionContextExt.of(User.systemUser(this.actionContext.getTenantId()), this.actionContext.getRequestContext()).getContext(),
                            ApiNames.ACCOUNT_OBJ,
                            conditionStoreQuery,
                            Lists.newArrayList("_id")).getData();

                    if (CollectionUtils.isEmpty(conditionStores)) {
                        throw new ValidateException(I18N.text(I18NKeys.THIS_STORE_WHICH_IS_NOT_IN_THE_RANGE_CAN_NOT_CREATE_AGREEMENT));
                    }
                    break;
                default:
                case "ALL":
                    break;
            }
        }
    }

    private IObjectData findAgreement(IObjectData activity) {
        String agreementId = (String) arg.getObjectData().get(TPMActivityProofFields.ACTIVITY_AGREEMENT_ID);
        if (Boolean.TRUE.equals(activity.get(TPMActivityFields.IS_AGREEMENT_REQUIRED))) {
            if (Strings.isNullOrEmpty(agreementId)) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_WITCH_IS_PROTOCOL_SHOULD_CHECK_UP_AGREEMENT_OPTION));
            }
            IObjectData agreementData = serviceFacade.findObjectDataIgnoreAll(User.systemUser(actionContext.getTenantId()), agreementId, ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);
            if (agreementData == null) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_WITCH_IS_PROTOCOL_SHOULD_CHECK_UP_AGREEMENT_OPTION));
            }
            return agreementData;
        }
        return null;
    }

    private void dateValidate(IObjectData store, IObjectData activity, IObjectData agreement) {
        if (agreement != null) {
            String lifeStatus = agreement.get(CommonFields.LIFE_STATUS, String.class);
            if (!CommonFields.LIFE_STATUS__NORMAL.equals(lifeStatus)) {
                throw new ValidateException(I18N.text(I18NKeys.AGREEMENT_STATUS_IS_ABNORMAL));
            }

            if (!TPMGrayUtils.skipProofAddAgreementTimeCheck(actionContext.getTenantId())) {
                long begin = agreement.get(TPMActivityAgreementFields.BEGIN_DATE, Long.class);
                long end = agreement.get(TPMActivityAgreementFields.END_DATE, Long.class);

                long now = System.currentTimeMillis();
                if (now < begin || now > end) {
                    throw new ValidateException(I18N.text(I18NKeys.AGREEMENT_HAS_NOT_TAKEN_EFFECT));
                }
            }

            if (!store.getId().equals(agreement.get(TPMActivityAgreementFields.STORE_ID, String.class))) {
                throw new ValidateException(I18N.text(I18NKeys.AGREEMENT_IS_NOT_FIT_THIS_STORE));
            }
            if (arg.getDetails().containsKey(ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ)) {
                for (ObjectDataDocument proofDetail : arg.getDetails().get(ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ)) {
                    if (Strings.isNullOrEmpty((String) proofDetail.get(TPMActivityProofDetailFields.ACTIVITY_AGREEMENT_DETAIL_ID))) {
                        throw new ValidateException(I18N.text(I18NKeys.THIS_IS_A_PROTOCOL_ACTIVITY_SO_PROOF_ITEM_SHOULD_RELATED_WITH_AGREEMENT_ITEM));
                    }
                }
            }
        } else {
            if (TPMGrayUtils.isHaoLiYou(actionContext.getTenantId())) {
                return;
            }
            long begin = activity.get(TPMActivityFields.BEGIN_DATE, Long.class);
            long end = activity.get(TPMActivityFields.END_DATE, Long.class);

            long now = System.currentTimeMillis();
            if (now < begin || now > end) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_IS_NOT_ACTIVE_OR_HAS_EXPIRED));
            }
        }
    }

    private IObjectData findDealer(IObjectData store, IObjectData agreement) {
        String dealerId;
        if (GrayRelease.isAllow("fmcg", "YINLU_TPM", actionContext.getTenantId()) && agreement != null) {
            dealerId = agreement.get(TPMActivityAgreementFields.DEALER_ID, String.class);
        } else if (TPMGrayUtils.dealerProofEnable(actionContext.getTenantId())) {
            dealerId = storeBusiness.findDealerId(actionContext.getTenantId(), store);
            if (Strings.isNullOrEmpty(dealerId)) {
                dealerId = store.getId();
            }
        } else {
            dealerId = storeBusiness.findDealerId(actionContext.getTenantId(), store);
        }
        EasyValidator.notNullOrEmpty(dealerId, I18N.text(I18NKeys.STORE_DEALER_IS_EMPTY_CAN_NOT_PROOF));

        IObjectData dealer = serviceFacade.findObjectDataIgnoreAll(User.systemUser(actionContext.getTenantId()), dealerId, ApiNames.ACCOUNT_OBJ);
        EasyValidator.notNull(dealer, "经销商信息不存在");

        return dealer;
    }

    private IObjectData findOrCreateDealerActivity(IObjectData activity, IObjectData dealer) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);
        query.setLimit(1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter activityFilter = new Filter();
        activityFilter.setFieldName(TPMDealerActivityFields.ACTIVITY_ID);
        activityFilter.setOperator(Operator.EQ);
        activityFilter.setFieldValues(Lists.newArrayList(activity.getId()));

        Filter dealerFilter = new Filter();
        dealerFilter.setFieldName(TPMDealerActivityFields.DEALER_ID);
        dealerFilter.setOperator(Operator.EQ);
        dealerFilter.setFieldValues(Lists.newArrayList(dealer.getId()));
        query.setFilters(Lists.newArrayList(activityFilter, dealerFilter));

        List<IObjectData> dealerActivities = serviceFacade.findBySearchTemplateQueryWithFields(
                ActionContextExt.of(User.systemUser(this.actionContext.getTenantId()), this.actionContext.getRequestContext()).getContext(),
                ApiNames.TPM_DEALER_ACTIVITY_OBJ,
                query,
                Lists.newArrayList("_id")).getData();

        if (dealerActivities.isEmpty()) {
            IObjectData dealerActivity = new ObjectData();
            dealerActivity.setDescribeApiName(ApiNames.TPM_DEALER_ACTIVITY_OBJ);
            dealerActivity.setTenantId(actionContext.getTenantId());
            dealerActivity.setOwner(Lists.newArrayList("-10000"));
            dealerActivity.set(TPMDealerActivityFields.DEALER_ID, dealer.getId());
            dealerActivity.set(TPMDealerActivityFields.ACTIVITY_ID, activity.getId());
            return serviceFacade.saveObjectData(User.systemUser(actionContext.getTenantId()), dealerActivity);
        } else {
            return dealerActivities.get(0);
        }
    }

    private String getAuditMode() {
        GetTenantConfig.Arg getConfigArg = new GetTenantConfig.Arg();
        getConfigArg.setKey("TPM_AUDIT_MODE");
        GetTenantConfig.Result getConfigResult = fmcgServiceProxy.getTenantConfig(Integer.parseInt(actionContext.getTenantId()), -10000, getConfigArg);

        EasyValidator.notNull(getConfigResult, "'TPM_AUDIT_MODE' config is null");
        LOGGER.info("tenantId : {}, 'TPM_AUDIT_MODE' config value : {}", actionContext.getTenantId(), getConfigResult.getValue());

        return getConfigResult.getValue();
    }

    private long getStartTimeStampOfDay(long now) {
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(now), ZoneId.systemDefault()).withHour(8).withMinute(0).withSecond(0).withNano(0);
        return localDateTime.toInstant(ZoneOffset.of("+8")).toEpochMilli();
    }

    private BigDecimal countProof(String storeId, String activityId, long begin, long end) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(1);
        query.setOffset(0);
        query.setNeedReturnCountNum(true);
        query.setFindExplicitTotalNum(true);
        query.setSearchSource("db");

        Filter activityFilter = new Filter();
        activityFilter.setFieldName(TPMActivityProofFields.ACTIVITY_ID);
        activityFilter.setOperator(Operator.EQ);
        activityFilter.setFieldValues(Lists.newArrayList(activityId));

        Filter storeFilter = new Filter();
        storeFilter.setFieldName(TPMActivityProofFields.STORE_ID);
        storeFilter.setOperator(Operator.EQ);
        storeFilter.setFieldValues(Lists.newArrayList(storeId));

        Filter createTimeFilter = new Filter();
        createTimeFilter.setFieldName(CommonFields.CREATE_TIME);
        createTimeFilter.setOperator(Operator.BETWEEN);
        createTimeFilter.setFieldValues(Lists.newArrayList(String.valueOf(begin), String.valueOf(end)));

        query.setFilters(Lists.newArrayList(storeFilter, activityFilter, createTimeFilter));

        return BigDecimal.valueOf(serviceFacade.findBySearchQuery(User.systemUser(actionContext.getTenantId()), ApiNames.TPM_ACTIVITY_PROOF_OBJ, query).getTotalNumber());
    }

    private BigDecimal countProof(String storeId, String activityId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(1);
        query.setOffset(0);
        query.setNeedReturnCountNum(true);
        query.setFindExplicitTotalNum(true);
        query.setSearchSource("db");

        Filter activityFilter = new Filter();
        activityFilter.setFieldName(TPMActivityProofFields.ACTIVITY_ID);
        activityFilter.setOperator(Operator.EQ);
        activityFilter.setFieldValues(Lists.newArrayList(activityId));

        Filter storeFilter = new Filter();
        storeFilter.setFieldName(TPMActivityProofFields.STORE_ID);
        storeFilter.setOperator(Operator.EQ);
        storeFilter.setFieldValues(Lists.newArrayList(storeId));

        query.setFilters(Lists.newArrayList(storeFilter, activityFilter));

        return BigDecimal.valueOf(serviceFacade.findBySearchQuery(User.systemUser(actionContext.getTenantId()), ApiNames.TPM_ACTIVITY_PROOF_OBJ, query).getTotalNumber());
    }

    private void autoAuditAction(Result result) {
        long now = System.currentTimeMillis();
        if ("1".equals(this.auditMode)) {
            IObjectData masterData = new ObjectData();
            masterData.setDescribeApiName(ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ);
            masterData.setTenantId(actionContext.getTenantId());
            masterData.setOwner(Lists.newArrayList("-10000"));
            masterData.set(TPMActivityProofAuditFields.ACTIVITY_PROOF_ID, result.getObjectData().getId());
            masterData.set(TPMActivityProofAuditFields.PROOF_IMAGES, result.getObjectData().get(TPMActivityProofFields.PROOF_IMAGES));
            masterData.set(TPMActivityProofAuditFields.ACTIVITY_AGREEMENT_ID, result.getObjectData().get(TPMActivityProofFields.ACTIVITY_AGREEMENT_ID));
            masterData.set(TPMActivityProofAuditFields.DEALER_ACTIVITY, result.getObjectData().get(TPMActivityProofFields.DEALER_ACTIVITY));
            masterData.set(TPMActivityProofAuditFields.STORE_ID, result.getObjectData().get(TPMActivityProofFields.STORE_ID));
            masterData.set(TPMActivityProofAuditFields.DEALER_ID, result.getObjectData().get(TPMActivityProofFields.DEALER_ID));
            masterData.set(TPMActivityProofAuditFields.ACTIVITY_ID, result.getObjectData().get(TPMActivityProofFields.ACTIVITY_ID));
            masterData.set(TPMActivityProofAuditFields.AUDIT_TIME, now);
            masterData.set(TPMActivityProofAuditFields.AUDIT_TOTAL, result.getObjectData().get(TPMActivityProofFields.ACTUAL_TOTAL) == null ? result.getObjectData().get(TPMActivityProofFields.TOTAL) : result.getObjectData().get(TPMActivityProofFields.ACTUAL_TOTAL));
            masterData.set(TPMActivityProofAuditFields.OPINION, "系统检核：合格");
            masterData.set(TPMActivityProofAuditFields.AUDITOR, Lists.newArrayList("-10000"));
            masterData.set(TPMActivityProofAuditFields.AUDIT_STATUS, TPMActivityProofFields.AUDIT_STATUS__PASS);
            masterData.set(TPMActivityProofAuditFields.RANDOM_AUDIT_STATUS, "unchecked");
            masterData.setRecordType("default__c");
            masterData.set(CommonFields.LOCK_STATUS, CommonFields.LOCK_STATUS__LOCK);

            Map<String, List<IObjectData>> detailsData = new HashMap<>();
            List<IObjectData> auditDetails = Lists.newArrayList();
            for (ObjectDataDocument proofDetail : result.getDetails().get(ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ)) {
                IObjectData auditDetail = new ObjectData();
                auditDetail.set(TPMActivityProofAuditDetailFields.ACTIVITY_PROOF_DETAIL_ID, proofDetail.getId());
                auditDetail.set(TPMActivityProofAuditDetailFields.ACTIVITY_ITEM_ID, proofDetail.get(TPMActivityProofDetailFields.ACTIVITY_ITEM_ID));
                auditDetail.set(TPMActivityProofAuditDetailFields.ACTIVITY_DETAIL_ID, proofDetail.get(TPMActivityProofDetailFields.ACTIVITY_DETAIL_ID));
                auditDetail.set(TPMActivityProofAuditDetailFields.ACTIVITY_AGREEMENT_DETAIL_ID, proofDetail.get(TPMActivityProofDetailFields.ACTIVITY_AGREEMENT_DETAIL_ID));
                auditDetail.set(TPMActivityProofAuditDetailFields.ACTIVITY_ITEM_COST_STANDARD_ID, proofDetail.get(TPMActivityProofDetailFields.ACTIVITY_ITEM_COST_STANDARD_ID));
                auditDetail.set(TPMActivityProofAuditDetailFields.AUDIT_SUBTOTAL, proofDetail.get(TPMActivityProofDetailFields.SUBTOTAL));
                auditDetail.set(TPMActivityProofAuditDetailFields.AUDIT_AMOUNT, proofDetail.get(TPMActivityProofDetailFields.AMOUNT));
                auditDetail.set(CommonFields.LOCK_STATUS, CommonFields.LOCK_STATUS__LOCK);
                auditDetail.setTenantId(actionContext.getTenantId());
                auditDetail.setOwner(Lists.newArrayList("-10000"));
                auditDetail.setDescribeApiName(ApiNames.TPM_ACTIVITY_PROOF_AUDIT_DETAIL_OBJ);
                auditDetail.setRecordType("default__c");
                auditDetails.add(auditDetail);
            }
            detailsData.put(ApiNames.TPM_ACTIVITY_PROOF_AUDIT_DETAIL_OBJ, auditDetails);

            Map<String, IObjectDescribe> describeMap = new HashMap<>();
            describeMap.put(ApiNames.TPM_ACTIVITY_PROOF_AUDIT_DETAIL_OBJ, serviceFacade.findObject(actionContext.getTenantId(), ApiNames.TPM_ACTIVITY_PROOF_AUDIT_DETAIL_OBJ));

            SaveMasterAndDetailData.Arg saveArg = SaveMasterAndDetailData.Arg.builder()
                    .masterObjectData(masterData)
                    .detailObjectData(detailsData)
                    .objectDescribes(describeMap)
                    .build();

            try {
                serviceFacade.saveMasterAndDetailData(User.systemUser(actionContext.getTenantId()), saveArg);
                BuryService.asyncTpmLog(Integer.valueOf(actionContext.getTenantId()), Integer.valueOf(actionContext.getUser().getUserIdOrOutUserIdIfOutUser()), BuryModule.TPM.TPM_ACTIVITY_PROOF_AUDIT, BuryOperation.AUTO_CREATE);
            } catch (Exception ex) {
                LOGGER.error("auto create proof audit data error : ", ex);
                throw new ValidateException(ex.getMessage());
            }
        }
    }
}
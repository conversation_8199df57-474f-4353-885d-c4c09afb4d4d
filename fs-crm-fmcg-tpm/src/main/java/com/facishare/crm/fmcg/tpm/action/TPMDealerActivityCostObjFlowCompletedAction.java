package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.tpm.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.apiname.TPMActivityAgreementFields;
import com.facishare.crm.fmcg.tpm.apiname.TPMDealerActivityCostFields;
import com.facishare.crm.fmcg.tpm.business.TPM2Service;
import com.facishare.crm.fmcg.tpm.business.abstraction.ITPM2Service;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardFlowCompletedAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/12/31 下午3:39
 */

public class TPMDealerActivityCostObjFlowCompletedAction extends StandardFlowCompletedAction {

    private static final Logger log = LoggerFactory.getLogger(TPMDealerActivityCostObjFlowCompletedAction.class);

    private ITPM2Service itpm2Service = SpringUtil.getContext().getBean(ITPM2Service.class);

    @Override
    protected void before(Arg arg) {
        if (itpm2Service.isTPM2Tenant(Integer.valueOf(actionContext.getTenantId()))) {
            IObjectData cost = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), arg.getDataId(), ApiNames.TPM_DEALER_ACTIVITY_COST);
            if ("pass".equals(arg.getStatus())) {
                Map<String, Object> updateField = new HashMap<>();
                updateField.put(TPMDealerActivityCostFields.WRITE_OFF_STATUS, TPMDealerActivityCostFields.WriteOffStatus.PASS);
                log.info("TPMDealerActivityCost flow complete update cost id:{} ,map : {}", arg.getDataId(), updateField);
                serviceFacade.updateWithMap(User.systemUser(actionContext.getTenantId()), cost, updateField);
            } else if ("reject".equals(arg.getStatus())) {
                log.info("flow may be reject.");
                Map<String, Object> updateField = new HashMap<>();
                updateField.put(TPMDealerActivityCostFields.WRITE_OFF_STATUS, TPMDealerActivityCostFields.WriteOffStatus.REJECT);
                log.info("TPMDealerActivityCost flow complete update cost id:{} ,map : {}", arg.getDataId(), updateField);
                serviceFacade.updateWithMap(User.systemUser(actionContext.getTenantId()), cost, updateField);
            }
        }
        super.before(arg);
    }

}

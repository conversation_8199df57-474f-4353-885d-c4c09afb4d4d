package com.facishare.crm.fmcg.tpm.controller;

import com.beust.jcommander.internal.Lists;
import com.facishare.crm.fmcg.tpm.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.apiname.TPMActivityFields;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.IRecordTypeOption;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.SelectOne;
import com.facishare.paas.metadata.impl.describe.SelectOption;
import lombok.extern.slf4j.Slf4j;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 12/23/20 8:42 PM
 */
@Slf4j
@SuppressWarnings("Duplicates")
public class TPMActivityObjListHeaderController extends StandardListHeaderController {

    @Override
    protected Result after(Arg arg, Result result) {

        if ("list".equals(arg.getLayoutType()) && Boolean.TRUE.equals(arg.getIncludeLayout())) {

            ObjectDescribeDocument activityDescribeDocument = ObjectDescribeDocument.of(result.getObjectDescribe().toObjectDescribe().copy());
            ObjectDescribeExt activityDescribe = ObjectDescribeExt.of(activityDescribeDocument);

            // proof record type support
            for (SelectOne selectOneField : activityDescribe.getSelectOneFields()) {
                if (selectOneField.getApiName().equals(TPMActivityFields.PROOF_RECORD_TYPE)) {
                    List<ISelectOption> options = selectOneField.getSelectOptions();
                    List<IRecordTypeOption> recordTypeOptions = serviceFacade.findRecordTypeOptionList(controllerContext.getTenantId(), ApiNames.TPM_ACTIVITY_PROOF_OBJ, false);

                    String optionsIdentity = toSelectOptionsIdentity(options);
                    String recordTypesIdentity = toRecordTypesIdentity(recordTypeOptions);

                    log.info("options identity : {}, record types identity : {}", optionsIdentity, recordTypesIdentity);

                    if (!optionsIdentity.equals(recordTypesIdentity)) {
                        options.clear();
                        for (IRecordTypeOption recordTypeOption : recordTypeOptions) {
                            ISelectOption newOption = new SelectOption();
                            newOption.setLabel(recordTypeOption.getLabel());
                            newOption.setValue(recordTypeOption.getApiName());
                            options.add(newOption);
                        }
                        selectOneField.setSelectOptions(options);
                        IObjectDescribe updateResult = serviceFacade.updateFieldDescribe(activityDescribe, Lists.newArrayList(selectOneField));

                        log.info("update field : {}, update result : {}", selectOneField.toJsonString(), updateResult.toJsonString());
                    }
                }
            }

        }
        return super.after(arg, result);
    }

    private String toSelectOptionsIdentity(List<ISelectOption> selectOptions) {
        return selectOptions
                .stream()
                .sorted(Comparator.comparing(ISelectOption::getValue))
                .map(m -> String.format("%s.%s", m.getValue(), m.getLabel()))
                .collect(Collectors.joining(","));
    }

    private String toRecordTypesIdentity(List<IRecordTypeOption> selectOptions) {
        return selectOptions
                .stream()
                .sorted(Comparator.comparing(IRecordTypeOption::getApiName))
                .map(m -> String.format("%s.%s", m.getApiName(), m.getLabel()))
                .collect(Collectors.joining(","));
    }

    @Override
    protected List<String> getAuthorizedFields() {
        List<String> list = super.getAuthorizedFields();
        list.remove("store_range");
        list.remove("cashing_product_range");
        list.remove("product_range");
        return list;
    }
}
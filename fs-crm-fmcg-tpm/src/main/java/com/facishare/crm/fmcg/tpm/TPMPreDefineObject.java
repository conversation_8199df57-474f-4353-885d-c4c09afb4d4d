package com.facishare.crm.fmcg.tpm;

import com.facishare.paas.appframework.core.model.ActionClassInfo;
import com.facishare.paas.appframework.core.model.ControllerClassInfo;
import com.facishare.paas.appframework.core.model.PreDefineObject;
import com.facishare.paas.appframework.core.model.PreDefineObjectRegistry;

import java.util.Arrays;
import java.util.List;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2020/11/4 5:33 PM
 */
@SuppressWarnings("all")
public enum TPMPreDefineObject implements PreDefineObject {
    TPMActivityProofObj("TPMActivityProofObj"),
    TPMActivityProofAuditObj("TPMActivityProofAuditObj"),
    TPMDealerActivityObj("TPMDealerActivityObj"),
    TPMActivityObj("TPMActivityObj"),
    TPMActivityItemObj("TPMActivityItemObj"),
    TPMDealerActivityCostObj("TPMDealerActivityCostObj"),
    TPMActivityAgreementObj("TPMActivityAgreementObj"),
    TPMActivityBudgetObj("TPMActivityBudgetObj"),
    TPMActivityItemCostStandardObj("TPMActivityItemCostStandardObj"),
    TPMActivityBudgetDetailObj("TPMActivityBudgetDetailObj"),
    TPMActivityBudgetAdjustObj("TPMActivityBudgetAdjustObj"),
    TPMCustomActivityPlanObj("tpm_activity_plan__c");

    private final String apiName;
    private static final String PACKAGE_NAME = TPMPreDefineObject.class.getPackage().getName();

    TPMPreDefineObject(String apiName) {
        this.apiName = apiName;
    }

    public static TPMPreDefineObject getEnum(String apiName) {
        List<TPMPreDefineObject> list = Arrays.asList(TPMPreDefineObject.values());
        return list.stream().filter(m -> m.getApiName().equalsIgnoreCase(apiName)).findAny().orElse(null);
    }

    public static void init() {
        for (TPMPreDefineObject object : TPMPreDefineObject.values()) {
            PreDefineObjectRegistry.register(object);
        }
    }

    @Override
    public String getApiName() {
        return this.apiName;
    }

    @Override
    public String getPackageName() {
        return PACKAGE_NAME;
    }

    @Override
    public ActionClassInfo getDefaultActionClassInfo(String actionCode) {
        String className = PACKAGE_NAME + ".action." + this + actionCode + "Action";
        return new ActionClassInfo(className);
    }

    @Override
    public ControllerClassInfo getControllerClassInfo(String methodName) {
        String className = PACKAGE_NAME + ".controller." + this + methodName + "Controller";
        return new ControllerClassInfo(className);
    }
}

package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.api.enumeration.LogType;
import com.facishare.crm.fmcg.tpm.api.log.LogData;
import com.facishare.crm.fmcg.tpm.api.method.IdempotentArgBase;
import com.facishare.crm.fmcg.tpm.apiname.*;
import com.facishare.crm.fmcg.tpm.business.BudgetService;
import com.facishare.crm.fmcg.tpm.business.OperateInfoService;
import com.facishare.crm.fmcg.tpm.common.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.service.*;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.dao.pg.mapper.metadata.SpecialTableMapper;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/11/11 下午5:03
 */
@SuppressWarnings("Duplicates")
public class TPMActivityObjInvalidAction extends StandardInvalidAction implements TransactionService<StandardInvalidAction.Arg, StandardInvalidAction.Result> {

    private static final BudgetService budgetService = SpringUtil.getContext().getBean(BudgetService.class);

    private final PackTransactionProxy packTransactionProxy = SpringUtil.getContext().getBean(PackTransactionProxyImpl.class);

    private final OperateInfoService operateInfoService = SpringUtil.getContext().getBean(OperateInfoService.class);

    private SpecialTableMapper specialTableMapper = SpringUtil.getContext().getBean(SpecialTableMapper.class);

    @Override
    protected void before(Arg arg) {

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter idFilter = new Filter();
        idFilter.setFieldName(TPMActivityProofFields.ACTIVITY_ID);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(arg.getObjectDataId()));

        Filter deletedFilter = new Filter();
        deletedFilter.setFieldName(CommonFields.IS_DELETED);
        deletedFilter.setOperator(Operator.EQ);
        deletedFilter.setFieldValues(Lists.newArrayList("false"));

        query.setFilters(Lists.newArrayList(idFilter, deletedFilter));

        List<IObjectData> data = serviceFacade.findBySearchQuery(User.systemUser(actionContext.getTenantId()), ApiNames.TPM_ACTIVITY_PROOF_OBJ, query).getData();

        if (!data.isEmpty()) {
            throw new ValidateException(I18N.text(I18NKeys.INVALID_ACTIVITY_ERROR_ITEM_USED_BY_PROOF));
        }

        query.setLimit(1);
        query.setOffset(0);

        idFilter = new Filter();
        idFilter.setFieldName(TPMActivityAgreementFields.ACTIVITY_ID);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(arg.getObjectDataId()));

        deletedFilter = new Filter();
        deletedFilter.setFieldName(CommonFields.IS_DELETED);
        deletedFilter.setOperator(Operator.EQ);
        deletedFilter.setFieldValues(Lists.newArrayList("false"));

        query.setFilters(Lists.newArrayList(idFilter, deletedFilter));

        data = serviceFacade.findBySearchQuery(User.systemUser(actionContext.getTenantId()), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, query).getData();

        if (!data.isEmpty()) {
            throw new ValidateException(I18N.text(I18NKeys.INVALID_ACTIVITY_ERROR_ITEM_USED_BY_AGREEMENT));
        }

        query.setLimit(1);
        query.setOffset(0);

        idFilter = new Filter();
        idFilter.setFieldName(TPMDealerActivityCostFields.ACTIVITY_ID);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(arg.getObjectDataId()));

        deletedFilter = new Filter();
        deletedFilter.setFieldName(CommonFields.IS_DELETED);
        deletedFilter.setOperator(Operator.EQ);
        deletedFilter.setFieldValues(Lists.newArrayList("false"));

        query.setFilters(Lists.newArrayList(idFilter, deletedFilter));

        data = serviceFacade.findBySearchQuery(User.systemUser(actionContext.getTenantId()), ApiNames.TPM_DEALER_ACTIVITY_COST, query).getData();

        if (!data.isEmpty()) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_RELATED_DEALER_ACTIVITY_CAN_NOT_BE_INVALID));
        }

        super.before(arg);
    }

    @Override
    protected Result doAct(Arg arg) {
        return packTransactionProxy.packAct(this, arg);
    }

    @Override
    protected void finallyDo() {
        try {
            super.finallyDo();
        } finally {
            budgetService.unLockBudget(actionContext);
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result rst = super.after(arg, result);

        String type = (String) result.getObjectData().get("record_type");
        if ("default__c".equals(type)) {
            BuryService.asyncTpmLog(Integer.valueOf(actionContext.getTenantId()), actionContext.getUser().getUserIdInt(), BuryModule.TPM.TPM_ACTIVITY, BuryOperation.DELETE_CUSTOM);
        }
        if ("dealer_activity__c".equals(type)) {
            BuryService.asyncTpmLog(Integer.valueOf(actionContext.getTenantId()), actionContext.getUser().getUserIdInt(), BuryModule.TPM.TPM_ACTIVITY, BuryOperation.DELETE_PERSONAL);
        }
        BuryService.asyncTpmLog(Integer.valueOf(actionContext.getTenantId()), actionContext.getUser().getUserIdInt(), BuryModule.TPM.TPM_ACTIVITY, BuryOperation.DELETE);

        //作废重新计算

        return rst;
    }

    @Override
    public Result doActTransaction(Arg arg) {
        IObjectData activity = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), arg.getObjectDataId(), ApiNames.TPM_ACTIVITY_OBJ);
        Result result = super.doAct(arg);

        if (!"ineffective".equals(activity.get(CommonFields.LIFE_STATUS)) && budgetService.isOpenBudge(Integer.parseInt(actionContext.getTenantId()))) {
            String budgetId = (String) result.getObjectData().getOrDefault(TPMActivityFields.BUDGET_TABLE, "");
            if (TPMGrayUtils.skipDeletedBudgetWhenCalculating(actionContext.getTenantId())) {
                List<Map> data = (specialTableMapper.setTenantId(actionContext.getTenantId())).findBySql(String.format("select id,is_deleted from fmcg_tpm_activity_budget where id = '%s' and tenant_id = '%s'", budgetId, actionContext.getTenantId()));
                if (!CollectionUtils.isEmpty(data)) {
                    Map budget = data.get(0);
                    if (!"0".equals(budget.get("is_deleted").toString())) {
                        return result;
                    }
                }
            }
            String closeStatus = (String) activity.get(TPMActivityFields.CLOSE_STATUS);

            if (!Strings.isNullOrEmpty(budgetId) && !TPMActivityFields.CLOSE_STATUS__CLOSED.equals(closeStatus)) {
                budgetService.tryLockBudget(actionContext, budgetId);
                double activityAmount = Double.parseDouble((String) result.getObjectData().getOrDefault(TPMActivityFields.ACTIVITY_AMOUNT, "0.0"));
                double actualAmount = Double.parseDouble((String) result.getObjectData().getOrDefault(TPMActivityFields.ACTIVITY_ACTUAL_AMOUNT, "0.0"));
                IObjectData budget = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), budgetId, ApiNames.TPM_ACTIVITY_BUDGET);
                double availableAmount = Double.parseDouble((String) budget.get(TPMActivityBudgetFields.AVAILABLE_AMOUNT));

                LogData logData = LogData.builder().data(JSON.toJSONString(result.getObjectData())).build();
                logData.setAttribute("budget", budget);
                String logId = operateInfoService.log(actionContext.getTenantId(), LogType.INVALID.value(), JSON.toJSONString(logData), actionContext.getUser().getUserId(), ApiNames.TPM_ACTIVITY_OBJ, arg.getObjectDataId(), this.needTriggerApprovalFlow());

                budgetService.addBudgetDetail(actionContext.getTenantId(), actionContext.getUser().getUserId(),
                        "2",
                        budgetId,
                        String.format("个案活动作废：「%s」作废", result.getObjectData().get("name")),
                        activityAmount - actualAmount,
                        availableAmount,
                        availableAmount + activityAmount,
                        System.currentTimeMillis(),
                        String.format("LogId:%s-traceId:%s", logId, TraceContext.get().getTraceId()),
                        arg.getObjectDataId(),
                        TraceContext.get().getTraceId(),
                        IdempotentArgBase.builder().idempotentKey(actionContext.getPostId() + ":" + budgetId).build());
                budgetService.calculateBudget(actionContext.getTenantId(), (String) result.getObjectData().getOrDefault(TPMActivityFields.BUDGET_TABLE, ""));
            }
        }
        return result;
    }
}

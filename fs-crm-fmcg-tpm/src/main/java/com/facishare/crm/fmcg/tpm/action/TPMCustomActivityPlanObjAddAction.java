package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import lombok.extern.slf4j.Slf4j;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/7/21 11:43
 */
@Slf4j
@SuppressWarnings("Duplicates")
public class TPMCustomActivityPlanObjAddAction extends StandardAddAction {

    @Override
    protected void before(Arg arg) {
        log.info("activity plan add start : {}", JSON.toJSONString(arg));
        super.before(arg);
    }
}
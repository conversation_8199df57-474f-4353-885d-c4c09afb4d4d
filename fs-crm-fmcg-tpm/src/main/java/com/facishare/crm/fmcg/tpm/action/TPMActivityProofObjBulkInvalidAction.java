package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.tpm.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.apiname.TPMActivityProofFields;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/8/4 下午3:35
 */
public class TPMActivityProofObjBulkInvalidAction extends StandardBulkInvalidAction {

    @Override
    protected void before(Arg arg) {

        List<IObjectData> proofs = serviceFacade.findObjectDataByIds(actionContext.getTenantId(),arg.getDataIds(), ApiNames.TPM_ACTIVITY_PROOF_OBJ);
        List<String> names1 = Lists.newArrayList();
        List<String> names2 = Lists.newArrayList();
        proofs.forEach(proof->{
            String status = proof.get(TPMActivityProofFields.AUDIT_STATUS,String.class);
            if(!"schedule".equals(status)){
                names1.add(proof.getName());
            }
            if(!Strings.isNullOrEmpty(proof.get(TPMActivityProofFields.DEALER_ACTIVITY_COST_ID, String.class))){
                names2.add(proof.getName());
            }
        });
        if(!CollectionUtils.isEmpty(names1)){
            throw new ValidateException(I18N.text(I18NKeys.BATCH_AUDITED_PROOF_CAN_NOT_INVALID_NAMES) +names1);
        }
        if(!CollectionUtils.isEmpty(names2)){
            throw new ValidateException(I18N.text(I18NKeys.BATCH_PROOF_RELATED_COST_CAN_NOT_INVALID_NAMES) +names2);
        }
        super.before(arg);
    }
}

package com.facishare.crm.fmcg.tpm.apiname;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2020/11/6 2:10 PM
 */
public interface TPMActivityFields {

    String BEGIN_DATE = "begin_date";
    String YINLU_AGREEMENT_BEGIN_DATE = "agreement_begin_date__c";
    String YINLU_AGREEMENT_END_DATE = "agreement_end_date__c";
    String END_DATE = "end_date";
    String ACTIVITY_TYPE = "activity_type";
    String ACTIVITY_STATUS = "activity_status";
    String DEALER_ID = "dealer_id";
    String PROOF_RECORD_TYPE = "proof_record_type";
    String PROOF_RECORD_TYPE__C = "proof_record_type__c";

    String ACTIVITY_STATUS__SCHEDULE = "schedule";
    String ACTIVITY_STATUS__IN_PROGRESS = "in_progress";
    String ACTIVITY_STATUS__END = "end";
    String ACTIVITY_STATUS__APPROVAL = "approval";
    String ACTIVITY_STATUS__INEFFECTIVE = "ineffective";

    String RECORD_TYPE__DEALER_ACTIVITY = "dealer_activity__c";

    String STORE_RANGE = "store_range";

    String SUBJECT = "subject";

    String IS_AGREEMENT_REQUIRED = "is_agreement_required";

    String DESCRIPTION = "description";

    String DEPARTMENT_RANGE = "department_range";

    String BUDGET_TABLE = "budget_table";
    String ACTIVITY_AMOUNT = "activity_amount";
    String CLOSED_STATUS = "closed_status";
    String AVAILABLE_AMOUNT = "available_amount";
    String ACTIVITY_ACTUAL_AMOUNT = "activity_actual_amount";
    String ACTIVITY_FROZEN_AMOUNT = "activity_frozen_amount";

    String CLOSE_STATUS = "closed_status";
    String CLOSE_STATUS__CLOSED = "closed";
    String CLOSE_STATUS__UNCLOSED = "unclosed";

    String MULTI_DEPARTMENT_RANGE = "multi_department_range";
}

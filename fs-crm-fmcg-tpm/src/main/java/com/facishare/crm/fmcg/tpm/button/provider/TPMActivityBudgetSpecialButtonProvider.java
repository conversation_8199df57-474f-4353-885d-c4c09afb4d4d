package com.facishare.crm.fmcg.tpm.button.provider;

import com.facishare.crm.fmcg.tpm.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.button.abs.AbstractTPMSpecialButtonProvider;
import com.facishare.crm.fmcg.tpm.utils.ButtonUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.metadata.ui.layout.IButton;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/9/8 下午3:04
 */
//@Component
public class TPMActivityBudgetSpecialButtonProvider extends AbstractTPMSpecialButtonProvider {

    @Override
    public String getApiName() {
        return ApiNames.TPM_ACTIVITY_BUDGET;
    }

    @Override
    public List<IButton> getSpecialButtons() {
        List<IButton> buttons = super.getSpecialButtons();
        /*buttons.add(ButtonUtils.buildButton(ObjectAction.BUDGET_TRANSFER));
        buttons.add(ButtonUtils.buildButton(ObjectAction.BUDGET_TRANSFER_IN));
        buttons.add(ButtonUtils.buildButton(ObjectAction.BUDGET_TRANSFER_OUT));*/
        return buttons;
    }
}

package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.tpm.apiname.*;
import com.facishare.crm.fmcg.tpm.business.BudgetService;
import com.facishare.crm.fmcg.tpm.common.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/11/11 下午5:03
 */

@SuppressWarnings("Duplicates")
public class TPMDealerActivityCostObjBulkInvalidAction extends StandardBulkInvalidAction {

    private static final BudgetService budgetService = SpringUtil.getContext().getBean(BudgetService.class);

    private static final Logger LOGGER = LoggerFactory.getLogger(TPMDealerActivityCostObjBulkInvalidAction.class);

    @Override
    protected void before(Arg arg) {

        List<IObjectData> costs = serviceFacade.findObjectDataByIds(actionContext.getTenantId(), arg.getDataIds(), ApiNames.TPM_DEALER_ACTIVITY_COST);
        List<String> names = Lists.newArrayList();
        costs.forEach(cost -> {
            if (cost.get("enter_into_account", Boolean.class, false))
                names.add(cost.getName());
        });
        if (!CollectionUtils.isEmpty(names)) {
            throw new ValidateException(I18N.text(I18NKeys.THIS_COST_HAS_TRANSFER_IN_ACCOUNT_DO_NOT_SUPPORT_INVALID) + names);
        }
        if (TPMGrayUtils.excessDeductionForCost(actionContext.getTenantId())) {
            throw new ValidateException("支持核销超额扣减的企业暂不支持批量作废。");
        }
        super.before(arg);
    }

    @Override
    protected Result after(Arg arg, Result result) {

        //作废重新计算
        if (budgetService.isOpenBudge(Integer.parseInt(actionContext.getTenantId()))) {
            Set<String> activityIds = new HashSet<>();
            Map<String, List<String>> budgetToActivity = new HashMap<>();
            result.getObjectDataList().forEach(v -> {
                if (!Strings.isNullOrEmpty((String) v.getOrDefault(TPMDealerActivityCostFields.ACTIVITY_ID, "")))
                    activityIds.add((String) v.get(TPMDealerActivityCostFields.ACTIVITY_ID));
            });
            List<IObjectData> activities = serviceFacade.findObjectDataByIds(actionContext.getTenantId(), new ArrayList<>(activityIds), ApiNames.TPM_ACTIVITY_OBJ);
            activities.forEach(activity -> {
                String budgetId = activity.get(TPMActivityFields.BUDGET_TABLE, String.class, "");
                if (!Strings.isNullOrEmpty(budgetId)) {
                    if (budgetToActivity.containsKey(budgetId)) {
                        budgetToActivity.get(budgetId).add(activity.getId());
                    } else {
                        budgetToActivity.put(budgetId, Lists.newArrayList(activity.getId()));
                    }

                }
            });
            budgetToActivity.forEach((budgetId, activityList) -> {
                budgetService.tryLockBudget(actionContext, budgetId);
                activityList.forEach(acId -> budgetService.calculateActivity(actionContext.getTenantId(), acId));
                budgetService.calculateBudget(actionContext.getTenantId(), budgetId);
                budgetService.unLockBudget(actionContext);
            });
        } else {
            Set<String> activityIds = new HashSet<>();
            result.getObjectDataList().forEach(v -> {
                if (!Strings.isNullOrEmpty((String) v.getOrDefault(TPMDealerActivityCostFields.ACTIVITY_ID, "")))
                    activityIds.add((String) v.get(TPMDealerActivityCostFields.ACTIVITY_ID));
            });
            activityIds.forEach(acId -> budgetService.calculateActivity(actionContext.getTenantId(), acId));
        }
        rmRelated(result);
        return super.after(arg, result);
    }

    public void rmRelated(Result result) {

        List<ObjectDataDocument> objectDataDocuments = new ArrayList<>(result.getObjectDataList());
        objectDataDocuments.removeAll(result.getFailureObjectDataList());
        List<String> costIds = objectDataDocuments.stream().map(ObjectDataDocument::getId).collect(Collectors.toList());
        for (String costId : costIds) {
            SearchTemplateQuery proofQuery = new SearchTemplateQuery();
            proofQuery.setLimit(-1);
            proofQuery.setOffset(0);
            proofQuery.setSearchSource("db");
            IFilter costFilter = new Filter();
            costFilter.setFieldName(TPMActivityProofFields.DEALER_ACTIVITY_COST_ID);
            costFilter.setOperator(Operator.EQ);
            costFilter.setFieldValues(Lists.newArrayList(costId));
            proofQuery.setFilters(Lists.newArrayList(costFilter));

            List<IObjectData> proofs = CommonUtils.queryData(serviceFacade, User.systemUser(actionContext.getTenantId()), ApiNames.TPM_ACTIVITY_PROOF_OBJ, proofQuery);
            LOGGER.info("proofs size:{}", proofs.size());
            List<String> proofUpdateFields = Lists.newArrayList(TPMActivityProofFields.DEALER_ACTIVITY_COST_ID);
            for (List<IObjectData> proofObjs : Lists.partition(proofs, 50)) {
                proofObjs.forEach(v -> v.set(TPMActivityProofFields.DEALER_ACTIVITY_COST_ID, ""));
                List<IObjectData> r = serviceFacade.batchUpdateByFields(User.systemUser(actionContext.getTenantId()), proofObjs, proofUpdateFields);
                LOGGER.info("proofs update size:{}", r.stream().filter(v -> Strings.isNullOrEmpty(v.get(TPMActivityProofFields.DEALER_ACTIVITY_COST_ID, String.class))).count());
            }


            SearchTemplateQuery proofAuditQuery = new SearchTemplateQuery();
            proofAuditQuery.setLimit(-1);
            proofAuditQuery.setOffset(0);
            proofAuditQuery.setSearchSource("db");
            IFilter costForAuditFilter = new Filter();
            costForAuditFilter.setFieldName(TPMActivityProofAuditFields.DEALER_ACTIVITY_COST_ID);
            costForAuditFilter.setOperator(Operator.EQ);
            costForAuditFilter.setFieldValues(Lists.newArrayList(costId));
            proofAuditQuery.setFilters(Lists.newArrayList(costForAuditFilter));

            List<IObjectData> proofAudits = CommonUtils.queryData(serviceFacade, User.systemUser(actionContext.getTenantId()), ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ, proofAuditQuery);

            LOGGER.info("proofAudits size:{}", proofAudits.size());
            List<String> proofAuditUpdateFields = Lists.newArrayList(TPMActivityProofAuditFields.DEALER_ACTIVITY_COST_ID);
            for (List<IObjectData> proofAuditObjs : Lists.partition(proofAudits, 50)) {
                proofAuditObjs.forEach(v -> v.set(TPMActivityProofAuditFields.DEALER_ACTIVITY_COST_ID, ""));
                List<IObjectData> r = serviceFacade.batchUpdateByFields(User.systemUser(actionContext.getTenantId()), proofAuditObjs, proofAuditUpdateFields);
                LOGGER.info("proofAudits update size:{}", r.stream().filter(v -> Strings.isNullOrEmpty(v.get(TPMActivityProofFields.DEALER_ACTIVITY_COST_ID, String.class))).count());
            }
        }
    }

    @Override
    protected void finallyDo() {
        try {
            super.finallyDo();
        } finally {
            budgetService.unLockBudget(actionContext);
        }
    }
}

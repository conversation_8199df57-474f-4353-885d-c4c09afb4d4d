package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.tpm.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardBulkRecoverAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.base.Strings;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/3/12 下午5:50
 */
public class TPMActivityObjBulkRecoverAction extends StandardBulkRecoverAction {

    @Override
    protected void before(Arg arg) {
        List<IObjectData> activities = serviceFacade.findObjectDataByIds(actionContext.getTenantId(), arg.getIdList(), ApiNames.TPM_ACTIVITY_OBJ);
        List<String> laji = new ArrayList<>();
        for (IObjectData activity : activities) {
            String budgetId = activity.get(TPMActivityFields.BUDGET_TABLE) == null ? "" : (String) activity.get(TPMActivityFields.BUDGET_TABLE);
            if (!Strings.isNullOrEmpty(budgetId))
                laji.add(activity.getName());
        }
        if (!CollectionUtils.isEmpty(laji)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_RELATED_BUDGET_CAN_NOT_BE_RECOVER) + laji.toString());
        }
        super.before(arg);
    }
}

package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.business.StoreBusiness;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/2/16 下午3:58
 */
public class TPMActivityObjDealerInfoController extends PreDefineController<TPMActivityObjDealerInfoController.Arg, TPMActivityObjDealerInfoController.Result> {


    private static final StoreBusiness storeBusiness = SpringUtil.getContext().getBean(StoreBusiness.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return new ArrayList<>();
    }

    @Override
    protected Result doService(Arg arg) {

         return Result.builder().build();
    }

    @Data
    @ToString
    static class Arg implements Serializable {

        @JSONField(name = "store_id")
        @JsonProperty("store_id")
        @SerializedName("store_id")
        private String storeId;

        @JSONField(name = "activity_id")
        @JsonProperty("activity_id")
        @SerializedName("activity_id")
        private String activityId;
    }


    @Data
    @ToString
    @Builder
    static class Result implements Serializable {

        @JSONField(name = "dealer_data")
        @JsonProperty("dealer_data")
        @SerializedName("dealer_data")
        private ObjectDataDocument dealerData;

        @JSONField(name = "store_data")
        @JsonProperty("store_data")
        @SerializedName("store_data")
        private ObjectDataDocument storeData;
    }
}

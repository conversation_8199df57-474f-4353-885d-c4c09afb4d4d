package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.tpm.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.apiname.TPMActivityProofFields;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.base.Strings;

/**
 * <AUTHOR>
 * @date 2021/8/4 下午3:35
 */
public class TPMActivityProofObjInvalidAction extends StandardInvalidAction {


    @Override
    protected void before(Arg arg) {

        IObjectData proof = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()),arg.getObjectDataId(), ApiNames.TPM_ACTIVITY_PROOF_OBJ);
        String status = proof.get(TPMActivityProofFields.AUDIT_STATUS,String.class);
        if(!"schedule".equals(status)){
            throw new ValidateException(I18N.text(I18NKeys.SINGLE_AUDITED_PROOF_CAN_NOT_INVALID_NAMES));
        }
        if(!Strings.isNullOrEmpty(proof.get(TPMActivityProofFields.DEALER_ACTIVITY_COST_ID, String.class))){
            throw new ValidateException(I18N.text(I18NKeys.SINGLE_PROOF_RELATED_COST_CAN_NOT_INVALID_NAMES));
        }
        super.before(arg);
    }
}

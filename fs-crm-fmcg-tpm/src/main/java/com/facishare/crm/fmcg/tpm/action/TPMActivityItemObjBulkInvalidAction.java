package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.tpm.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.apiname.CommonFields;
import com.facishare.crm.fmcg.tpm.apiname.TPMActivityDetailFields;
import com.facishare.crm.fmcg.tpm.apiname.TPMActivityItemCostStandardFields;
import com.facishare.crm.fmcg.tpm.service.BuryModule;
import com.facishare.crm.fmcg.tpm.service.BuryOperation;
import com.facishare.crm.fmcg.tpm.service.BuryService;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.stream.Collectors;

@SuppressWarnings("Duplicates")
public class TPMActivityItemObjBulkInvalidAction extends StandardBulkInvalidAction {


    @Override
    protected void before(Arg arg) {

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter idListFilter = new Filter();
        idListFilter.setFieldName(TPMActivityDetailFields.ACTIVITY_ITEM_ID);
        idListFilter.setOperator(Operator.IN);
        idListFilter.setFieldValues(arg.getDataIds());

        Filter deletedFilter = new Filter();
        deletedFilter.setFieldName(CommonFields.IS_DELETED);
        deletedFilter.setOperator(Operator.EQ);
        deletedFilter.setFieldValues(Lists.newArrayList("false"));

        query.setFilters(Lists.newArrayList(idListFilter, deletedFilter));

        List<IObjectData> activityDetails = CommonUtils.queryData(serviceFacade, User.systemUser(actionContext.getTenantId()), ApiNames.TPM_ACTIVITY_DETAIL_OBJ, query);


        if (!activityDetails.isEmpty()) {

            List<String> activityItemIds = activityDetails.stream().map(record -> (String) record.get(TPMActivityDetailFields.ACTIVITY_ITEM_ID)).distinct().collect(Collectors.toList());
            List<IObjectData> data = serviceFacade.findObjectDataByIds(actionContext.getUser().getTenantId(), activityItemIds, ApiNames.TPM_ACTIVITY_ITEM_OBJ);

            String names = data.stream().map(IObjectData::getName).collect(Collectors.joining(","));
            throw new ValidateException(I18N.text(I18NKeys.BATCH_INVALID_ACTIVITY_ITEM_ERROR_ITEM_USED, names));
        }
        validateCostStandard(arg);
        super.before(arg);
    }

    private void validateCostStandard(Arg arg) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter idListFilter = new Filter();
        idListFilter.setFieldName(TPMActivityItemCostStandardFields.ACTIVITY_ITEM);
        idListFilter.setOperator(Operator.IN);
        idListFilter.setFieldValues(arg.getDataIds());

        Filter deletedFilter = new Filter();
        deletedFilter.setFieldName(CommonFields.IS_DELETED);
        deletedFilter.setOperator(Operator.EQ);
        deletedFilter.setFieldValues(Lists.newArrayList("false"));

        query.setFilters(Lists.newArrayList(idListFilter, deletedFilter));

        List<IObjectData> costStandard = CommonUtils.queryData(serviceFacade, User.systemUser(actionContext.getTenantId()), ApiNames.TPM_ACTIVITY_ITEM_COST_STANDARD, query);


        if (!costStandard.isEmpty()) {

            List<String> activityItemIds = costStandard.stream().map(record -> (String) record.get(TPMActivityItemCostStandardFields.ACTIVITY_ITEM)).distinct().collect(Collectors.toList());
            List<IObjectData> data = serviceFacade.findObjectDataByIds(actionContext.getUser().getTenantId(), activityItemIds, ApiNames.TPM_ACTIVITY_ITEM_OBJ);

            String names = data.stream().map(IObjectData::getName).collect(Collectors.joining(","));
            throw new ValidateException(I18N.text(I18NKeys.BATCH_INVALID_ACTIVITY_ITEM_ERROR_ITEM_USED_BY_COST_STANDARD, names));
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        int size = arg.getDataIds().size() - result.getFailureObjectDataList().size();
        for (int i = 0; i < size; i++) {
            BuryService.asyncTpmLog(Integer.valueOf(actionContext.getTenantId()), actionContext.getUser().getUserIdInt(), BuryModule.TPM.TPM_ACTIVITY_ITEM, BuryOperation.DELETE);
        }
        return super.after(arg, result);
    }
}
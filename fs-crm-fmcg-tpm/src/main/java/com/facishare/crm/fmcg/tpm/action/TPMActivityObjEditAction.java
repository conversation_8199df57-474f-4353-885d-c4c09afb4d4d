package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.crm.fmcg.tpm.api.enumeration.LogType;
import com.facishare.crm.fmcg.tpm.api.log.LogData;
import com.facishare.crm.fmcg.tpm.api.method.IdempotentArgBase;
import com.facishare.crm.fmcg.tpm.apiname.*;
import com.facishare.crm.fmcg.tpm.business.BudgetService;
import com.facishare.crm.fmcg.tpm.business.OperateInfoService;
import com.facishare.crm.fmcg.tpm.business.TPM2Service;
import com.facishare.crm.fmcg.tpm.business.abstraction.ITPM2Service;
import com.facishare.crm.fmcg.tpm.common.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.service.PackTransactionProxy;
import com.facishare.crm.fmcg.tpm.service.PackTransactionProxyImpl;
import com.facishare.crm.fmcg.tpm.service.TransactionService;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.GlobalConstant;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.utils.TimeUtils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.common.MapUtils;
import com.github.autoconf.ConfigFactory;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.*;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2020/11/6 2:27 PM
 */
@SuppressWarnings("Duplicates")
public class TPMActivityObjEditAction extends StandardEditAction implements TransactionService<StandardEditAction.Arg, StandardEditAction.Result> {

    public static final Logger log = LoggerFactory.getLogger(TPMActivityObjEditAction.class);
    public static final BudgetService budgetService = SpringUtil.getContext().getBean(BudgetService.class);
    private static final String SEND_BUDGET_IDS = "SEND_BUDGET_IDS:%s";
    private static final String NEW_ADD_BUDGET = "NEW_ADD_BUDGET";
    private final OperateInfoService operateInfoService = SpringUtil.getContext().getBean(OperateInfoService.class);

    private final PackTransactionProxy packTransactionProxy = SpringUtil.getContext().getBean(PackTransactionProxyImpl.class);

    private ITPM2Service tpm2Service = SpringUtil.getContext().getBean(TPM2Service.class);

    private static Map<String, Set<String>> ALLOW_EDIT_DETAIL_FIELD_MAP = new HashMap<>();

    static {
        ConfigFactory.getConfig("fs-fmcg-tpm-config", iConfig -> {
            String json = iConfig.get("activity_allow_edit_detail_field_map");
            if (!Strings.isNullOrEmpty(json)) {
                ALLOW_EDIT_DETAIL_FIELD_MAP = JSON.parseObject(json, new TypeReference<Map<String, Set<String>>>() {
                });
            }
        });
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        validateEndDate(arg);
        validateActivityStatus(arg);
        actionContext.setAttribute("triggerFlow", true);
        validateStatus(arg);
        buildApprovalCallback();
        if (!onlyChangeDetailAllowedField()) {
            validateProof(arg);
        }
    }

    private void validateStatus(Arg arg) {
        String lifeStatus = (String) arg.getObjectData().get(CommonFields.LIFE_STATUS);
        String budgetId = (String) arg.getObjectData().get(TPMActivityFields.BUDGET_TABLE);
        if (!Strings.isNullOrEmpty(budgetId) && (ObjectLifeStatus.IN_CHANGE.getCode().equals(lifeStatus) || ObjectLifeStatus.UNDER_REVIEW.getCode().equals(lifeStatus))) {
            throw new ValidateException("活动已经被预算管控，请取消审批再次编辑。");
        }
    }

    @Override
    protected void diffObjectDataWithDbData() {
        super.diffObjectDataWithDbData();
        buildApprovalCallback();
        AddApprovalCallback(GlobalConstant.BUDGET_APPROVAL_CALLBACK_INCREASE_AMOUNT_KEY);
    }

    private void buildApprovalCallback() {
        if (!MapUtils.isNullOrEmpty(this.updatedFieldMap)) {
            if (this.updatedFieldMap.containsKey(GlobalConstant.BUDGET_APPROVAL_CALLBACK_MAP_KEY))
                return;
            Map<String, Object> callbackDatum = new HashMap<>();
            String repeatValue = actionContext.getAttribute(GlobalConstant.BUDGET_APPROVAL_CALLBACK_RELATION_KEY);
            String value = repeatValue == null ? IdGenerator.get() : repeatValue;
            callbackDatum.put(GlobalConstant.BUDGET_APPROVAL_CALLBACK_RELATION_KEY, value);
            actionContext.setAttribute(GlobalConstant.BUDGET_APPROVAL_CALLBACK_RELATION_KEY, value);
            this.updatedFieldMap.put(GlobalConstant.BUDGET_APPROVAL_CALLBACK_MAP_KEY, callbackDatum);
        } else {
            budgetService.buildCallbackKey(actionContext);
        }
    }

    private void AddApprovalCallback(String key, Object value) {
        if (!MapUtils.isNullOrEmpty(this.updatedFieldMap)) {
            Map<String, Object> callbackDatum = (Map<String, Object>) this.updatedFieldMap.getOrDefault(GlobalConstant.BUDGET_APPROVAL_CALLBACK_MAP_KEY, new HashMap<>());
            callbackDatum.put(key, value);
            this.updatedFieldMap.put(GlobalConstant.BUDGET_APPROVAL_CALLBACK_MAP_KEY, callbackDatum);
        }
    }

    private void AddApprovalCallback(String key) {
        String value = actionContext.getAttribute(key);
        if (value != null && !MapUtils.isNullOrEmpty(this.updatedFieldMap)) {
            Map<String, Object> callbackDatum = (Map<String, Object>) this.updatedFieldMap.getOrDefault(GlobalConstant.BUDGET_APPROVAL_CALLBACK_MAP_KEY, new HashMap<>());
            callbackDatum.put(key, value);
            this.updatedFieldMap.put(GlobalConstant.BUDGET_APPROVAL_CALLBACK_MAP_KEY, callbackDatum);
        }
    }

    @Override
    protected Map<String, Map<String, Object>> buildCallbackDataForAddAction(IObjectData objectData, Long detailCreateTime) {
        Map<String, Map<String, Object>> callbackData = super.buildCallbackDataForAddAction(objectData, detailCreateTime);
        String relationValue = actionContext.getAttribute(GlobalConstant.BUDGET_APPROVAL_CALLBACK_RELATION_KEY);
        if (!Strings.isNullOrEmpty(relationValue)) {
            Map<String, Object> instanceMap = callbackData.getOrDefault(objectData.getId(), new HashMap<>());
            callbackData.put(objectData.getId(), instanceMap);
            Map<String, Object> callbackDatum = new HashMap<>();
            callbackDatum.put(GlobalConstant.BUDGET_APPROVAL_CALLBACK_RELATION_KEY, relationValue);
            instanceMap.put(GlobalConstant.BUDGET_APPROVAL_CALLBACK_MAP_KEY, callbackDatum);
        }
        return callbackData;
    }

    private void validateBudget(Arg arg) {

        String type = (String) arg.getObjectData().get("record_type");
        int tenantId = Integer.parseInt(actionContext.getTenantId());
        //开启预算表
        if (budgetService.isOpenBudge(tenantId)) {
            String nowBudgetId = arg.getObjectData().get(TPMActivityFields.BUDGET_TABLE) != null ? (String) arg.getObjectData().get(TPMActivityFields.BUDGET_TABLE) : "";
            //todo:try lock
            //budgetService.tryLockBudget(actionContext, nowBudgetId);

            IObjectData beforeActivity = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), arg.getObjectData().getId(), ApiNames.TPM_ACTIVITY_OBJ);
            //之前已经关联预算表
            String beforeBudgetId = beforeActivity.get(TPMActivityFields.BUDGET_TABLE) != null ? (String) beforeActivity.get(TPMActivityFields.BUDGET_TABLE) : "";
            if (!Strings.isNullOrEmpty(beforeBudgetId) && !beforeBudgetId.equals(nowBudgetId)) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_RELATED_BUDGET_CAN_NOT_CHANGE_BUDGET));
            }
            String lifeStatus = (String) arg.getObjectData().get("life_status");
            if (!"ineffective".equals(lifeStatus) && Strings.isNullOrEmpty(beforeBudgetId) && !Strings.isNullOrEmpty(nowBudgetId)) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_RELATED_A_NEW_BUDGET_IS_NOT_ALLOW));
            }
            //新关联的预算表需要先计算活动
            if (Strings.isNullOrEmpty(beforeBudgetId) && !beforeBudgetId.equals(nowBudgetId)) {
                actionContext.setAttribute(NEW_ADD_BUDGET, "yes");
            }

            if (!Strings.isNullOrEmpty(nowBudgetId)) {

                IObjectData budget = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), (String) arg.getObjectData().get(TPMActivityFields.BUDGET_TABLE), ApiNames.TPM_ACTIVITY_BUDGET);
                log.info("budget:{}", budget);
                if (!ObjectLifeStatus.NORMAL.getCode().equals(budget.get(CommonFields.LIFE_STATUS))) {
                    throw new ValidateException("该预算表未生效或处于审批中，请更换预算表。");
                }
                //判断时间
                Integer startMonth = budgetService.getBudgetStartMonth(tenantId);
                Long activityBeginTime = (Long) arg.getObjectData().get(TPMActivityFields.BEGIN_DATE);
                int budgetYear = Integer.parseInt((String) budget.get(TPMActivityBudgetFields.PERIOD_YEAR));
                LocalDate budgetStartDate = LocalDate.of(budgetYear, startMonth, 1);
                LocalDate budgetEndDate = budgetStartDate.plusYears(1);
                LocalDate activityBeginDate = Instant.ofEpochMilli(activityBeginTime).atZone(ZoneOffset.ofHours(8)).toLocalDate();
                if (budgetStartDate.isAfter(activityBeginDate) || budgetEndDate.isBefore(activityBeginDate) || budgetEndDate.equals(activityBeginDate)) {
                    throw new ValidateException(I18N.text(I18NKeys.ADD_ACTIVITY_TIME_IS_NOT_FIT_BUDGET));
                }

                //department
                String budgetDepartment = ((List<String>) budget.get(TPMActivityBudgetFields.BUDGET_DEPARTMENT)).get(0);
                List<String> subDeptIds = serviceFacade.getSubDeptByDeptId(actionContext.getTenantId(), User.SUPPER_ADMIN_USER_ID, budgetDepartment, true);
                String activityDepartment = ((List<String>) arg.getObjectData().toObjectData().get(TPMActivityFields.DEPARTMENT_RANGE)).get(0);
                if (!(activityDepartment.equals(budgetDepartment) || subDeptIds.contains(activityDepartment))) {
                    throw new ValidateException(I18N.text(I18NKeys.ADD_ACTIVITY_BUDGET_DEPARTMENT_NOT_FIT));
                }

                double activityAmountInObj = Double.parseDouble(arg.getObjectData().getOrDefault(TPMActivityFields.ACTIVITY_AMOUNT, "0").toString());
                if (activityAmountInObj < 0)
                    throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_AMOUNT_CAN_NOT_BE_ZERO));
                //预算

                Map<String, Double> amountMap = new HashMap<>();
                double availableAmount = budgetService.getBudgetAvailableAmount(actionContext.getTenantId(), budget, amountMap);
                Double activityAmount = Double.parseDouble((String) arg.getObjectData().getOrDefault(TPMActivityFields.ACTIVITY_AMOUNT, "0"));

                LogData logData = LogData.builder().data(JSON.toJSONString(arg)).updateMap(this.updatedFieldMap).originalData(this.dbMasterData).build();
                logData.setAttribute("budget", budget);
                logData.setAttribute("amountMap", amountMap);
                String logId = null;
                IObjectData detail = null;
                //取消或则驳回后继续申请
                if ("under_review".equals(lifeStatus) || "ineffective".equals(lifeStatus)) {
                    if (!TPMGrayUtils.disableBudgetAmountJudge(actionContext.getTenantId()) && CommonUtils.keepNDecimal(availableAmount - activityAmount, 3) < 0) {
                        throw new ValidateException(I18N.text(I18NKeys.ADD_ACTIVITY_BUDGET_INSUFFICIENT));
                    }
                    boolean needTriggerApproval = budgetService.needApproval(actionContext.getTenantId(), ApiNames.TPM_ACTIVITY_OBJ, arg.getObjectData().getId(), "Create");
                    actionContext.setAttribute("needTriggerApproval", needTriggerApproval);

                    logId = operateInfoService.log(actionContext.getTenantId(), LogType.ADD.value(), JSON.toJSONString(logData), actionContext.getUser().getUserId(), ApiNames.TPM_ACTIVITY_OBJ, arg.getObjectData().getId(), needTriggerApproval);
                    detail = budgetService.addBudgetDetail(actionContext.getTenantId(), actionContext.getUser().getUserId(),
                            "1",
                            budget.getId(),
                            String.format("活动新建：「%s」申请", arg.getObjectData().get("name")),
                            -activityAmount,
                            availableAmount,
                            availableAmount - activityAmount,
                            System.currentTimeMillis(),
                            String.format("LogId:%s-traceId:%s", logId, TraceContext.get().getTraceId()),
                            arg.getObjectData().getId(),
                            TraceContext.get().getTraceId(),
                            IdempotentArgBase.builder().idempotentKey(actionContext.getPostId() + ":" + arg.getObjectData().getId()).build());
                } else {
                    boolean needTriggerApproval = !MapUtils.isNullOrEmpty(this.updatedFieldMap) && budgetService.needUpdateApproval(actionContext.getTenantId(), ApiNames.TPM_ACTIVITY_OBJ, arg.getObjectData().getId(), "Update", this.updatedFieldMap);
                    actionContext.setAttribute("needTriggerApproval", needTriggerApproval);

                    Double beforeActivityAmount = Double.parseDouble(beforeActivity.get(TPMActivityFields.ACTIVITY_AMOUNT, String.class, "0"));
                    if (!TPMGrayUtils.disableBudgetAmountJudge(actionContext.getTenantId()) && !beforeActivityAmount.equals(activityAmount) && availableAmount + beforeActivityAmount - activityAmount < 0) {
                        throw new ValidateException(I18N.text(I18NKeys.ADD_ACTIVITY_BUDGET_INSUFFICIENT));
                    }
                    if (beforeActivityAmount.equals(activityAmount)) {
                        log.info("activity has the same amount. before:{},now:{}", beforeActivityAmount, activityAmount);
                        return;
                    }
                    JSONObject snapshot = budgetService.getApprovalInstanceSnapshot(actionContext.getTenantId(), ApiNames.TPM_ACTIVITY_OBJ, arg.getObjectData().getId());
                    logData.setAttribute("snapshot", snapshot);

                    double incrementAmount = activityAmount - beforeActivityAmount;
                    if (incrementAmount > 0 || (!needTriggerApproval && CommonUtils.keepNDecimal(incrementAmount, 3) != 0)) {
                        logId = operateInfoService.log(actionContext.getTenantId(), LogType.EDIT.value(), JSON.toJSONString(logData), actionContext.getUser().getUserId(), ApiNames.TPM_ACTIVITY_OBJ, arg.getObjectData().getId(), needTriggerApproval);
                        detail = budgetService.addBudgetDetail(actionContext.getTenantId(), actionContext.getUser().getUserId(),
                                incrementAmount < 0 ? "2" : "1",
                                budget.getId(),
                                String.format("个案活动编辑：「%s」%s活动预算", arg.getObjectData().get("name"), (incrementAmount < 0 ? "追加" : "削减")),
                                -incrementAmount,
                                availableAmount,
                                availableAmount - incrementAmount,
                                System.currentTimeMillis(),
                                String.format("LogId:%s-traceId:%s", logId, TraceContext.get().getTraceId()),
                                arg.getObjectData().getId(),
                                TraceContext.get().getTraceId(),
                                IdempotentArgBase.builder().idempotentKey(actionContext.getPostId() + ":" + arg.getObjectData().getId()).build());
                    } else {
                        actionContext.setAttribute(GlobalConstant.BUDGET_APPROVAL_CALLBACK_INCREASE_AMOUNT_KEY, incrementAmount);
                        AddApprovalCallback(GlobalConstant.BUDGET_APPROVAL_CALLBACK_INCREASE_AMOUNT_KEY, incrementAmount);
                        log.info("is a add amount action to budget.updateMap:{}", this.updatedFieldMap);
                    }
                }
                if (detail != null)
                    actionContext.setAttribute("detail_id", detail.getId());
                if (logId != null)
                    actionContext.setAttribute("operate_log_id", logId);


                //calculate budget
                List<String> updateBudgetIds = new ArrayList<>();
                if (!nowBudgetId.equals(beforeBudgetId)) {
                    budgetService.calculateBudget(actionContext.getTenantId(), (String) beforeActivity.get(TPMActivityFields.BUDGET_TABLE));
                    updateBudgetIds.add(budget.getId());
                    if (!Strings.isNullOrEmpty(beforeBudgetId))
                        updateBudgetIds.add(beforeBudgetId);
                } else {
                    //just edit activity amount.should calculate
                    if (!activityAmount.equals(beforeActivity.get(TPMActivityFields.ACTIVITY_AMOUNT))) {
                        updateBudgetIds.add(budget.getId());
                    }
                }
                actionContext.setAttribute(String.format(SEND_BUDGET_IDS, actionContext.getPostId()), updateBudgetIds);
            }
        }
    }

    @Override
    protected Result doAct(Arg arg) {
        tryLock();
        return packTransactionProxy.packAct(this, arg);
    }

    private void validateProof(Arg arg) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(10);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter activityFilter = new Filter();
        activityFilter.setFieldName(TPMActivityProofFields.ACTIVITY_ID);
        activityFilter.setOperator(Operator.EQ);
        activityFilter.setFieldValues(Lists.newArrayList(arg.getObjectData().getId()));

        query.setFilters(Lists.newArrayList(activityFilter));
        List<IObjectData> list = serviceFacade.findBySearchQuery(User.systemUser(actionContext.getTenantId()), ApiNames.TPM_ACTIVITY_PROOF_OBJ, query).getData();
        if (!list.isEmpty()) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_WHICH_HAS_RELATED_BY_PROOF_CAN_NOT_EDIT));
        }
    }

    private void validateActivityStatus(Arg arg) {
        long begin = (long) arg.getObjectData().get(TPMActivityFields.BEGIN_DATE);
        long end = (long) arg.getObjectData().get(TPMActivityFields.END_DATE);

        long now = System.currentTimeMillis();
        String status;
        if (now < begin) {
            status = TPMActivityFields.ACTIVITY_STATUS__SCHEDULE;
        } else if (now < end) {
            status = TPMActivityFields.ACTIVITY_STATUS__IN_PROGRESS;
        } else {
            status = TPMActivityFields.ACTIVITY_STATUS__END;
        }
        arg.getObjectData().put(TPMActivityFields.ACTIVITY_STATUS, status);
    }

    private void validateEndDate(Arg arg) {
        long begin = (long) this.objectData.get(TPMActivityFields.BEGIN_DATE);
        long end = TimeUtils.convertToDayEndIfTimeWasDayBegin((long) this.objectData.get(TPMActivityFields.END_DATE));
        arg.getObjectData().put(TPMActivityFields.END_DATE, end);

        if (end <= begin) {
            throw new ValidateException(I18N.text(I18NKeys.ADD_ACTIVITY_DATE_ERROR));
        }

        log.info("end date : {}", end);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result afterRst = super.after(arg, result);

        //有审批没有用
        List<String> updateBudgetIds = actionContext.getAttribute(String.format(SEND_BUDGET_IDS, actionContext.getPostId()));
        log.info("updateBudgetIds:{}", updateBudgetIds);
        if (!CollectionUtils.isEmpty(updateBudgetIds)) {
            boolean isCalculateActivity = !Strings.isNullOrEmpty(actionContext.getAttribute(NEW_ADD_BUDGET));
            updateBudgetIds.forEach(id -> {
                if (isCalculateActivity) {
                    budgetService.calculateActivity(actionContext.getTenantId(), arg.getObjectData().getId());
                }
                budgetService.calculateBudget(actionContext.getTenantId(), id);
            });
        }
        resetActivityStatus(result);
        return afterRst;
    }

    private void updateApprovalIdToDetail(Result result) {
        boolean needTriggerApproval = Boolean.TRUE.equals(actionContext.getAttribute("needTriggerApproval"));
        if (needTriggerApproval) {
            if (!this.isApprovalNotExist() && actionContext.getAttribute("operate_log_id") != null) {
                String approvalId = actionContext.getAttribute(GlobalConstant.BUDGET_APPROVAL_CALLBACK_RELATION_KEY);
                operateInfoService.updateLog(actionContext.getAttribute("operate_log_id"), actionContext.getTenantId(), approvalId);
                if (actionContext.getAttribute("detail_id") != null)
                    budgetService.updateApprovalIdForDetail(actionContext.getTenantId(), actionContext.getAttribute("detail_id"), approvalId);
            }
        }
    }


    @Override
    protected void finallyDo() {
        try {
            super.finallyDo();
        } finally {
            budgetService.unLockBudget(actionContext);
        }
    }

    @Override
    public Result doActTransaction(Arg arg) {
        validateBudget(arg);
        Result result = super.doAct(arg);
        updateApprovalIdToDetail(result);
        return result;
    }

    private void resetActivityStatus(Result result) {
        if (tpm2Service.isTPM2Tenant(Integer.valueOf(actionContext.getTenantId()))) {
            String lifeStatus = (String) result.getObjectData().get(CommonFields.LIFE_STATUS);
            if (!ObjectLifeStatus.NORMAL.getCode().equals(lifeStatus)) {
                Map<String, Object> updateMap = new HashMap<>();
                updateMap.put(TPMActivityFields.ACTIVITY_STATUS, TPMActivityFields.ACTIVITY_STATUS__APPROVAL);
                serviceFacade.updateWithMap(User.systemUser(actionContext.getTenantId()), result.getObjectData().toObjectData(), updateMap);
            }
        }
    }


    private void tryLock() {
        if (budgetService.isOpenBudge(Integer.parseInt(actionContext.getTenantId()))) {
            String nowBudgetId = arg.getObjectData().get(TPMActivityFields.BUDGET_TABLE) != null ? (String) arg.getObjectData().get(TPMActivityFields.BUDGET_TABLE) : "";
            //todo:try lock
            budgetService.tryLockBudget(actionContext, nowBudgetId);
        }
    }

    private boolean onlyChangeDetailAllowedField() {
        Set<String> masterKeySet = new HashSet<>(this.updatedFieldMap.keySet());
        masterKeySet.remove(GlobalConstant.BUDGET_APPROVAL_CALLBACK_RELATION_KEY);
        Set<String> allowedDetailField = ALLOW_EDIT_DETAIL_FIELD_MAP.getOrDefault(actionContext.getTenantId(), new HashSet<>());
        if (masterKeySet.size() > 0)
            return false;
        for (Map.Entry<String, Map<String, Object>> entry : this.detailChangeMap.entrySet()) {
            String apiName = entry.getKey();
            Map<String, Object> modeMap = entry.getValue();
            for (Map.Entry<String, Object> e : modeMap.entrySet()) {
                String mode = e.getKey();
                if (!ObjectAction.UPDATE.getActionCode().equals(mode)) {
                    return false;
                }
                Object id2Value = e.getValue();
                Map<String, ObjectDataDocument> entity = (Map<String, ObjectDataDocument>) id2Value;
                if (entity.values().stream().anyMatch(v -> v.keySet().stream().anyMatch(key -> !key.endsWith("__c") && !allowedDetailField.contains(key)))) {
                    return false;
                }
            }
        }
        return true;
    }
}

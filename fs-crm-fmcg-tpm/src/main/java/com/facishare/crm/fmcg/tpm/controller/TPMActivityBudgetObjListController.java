package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.crm.fmcg.tpm.apiname.TPMActivityFields;
import com.facishare.paas.appframework.core.model.ButtonDocument;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.fxiaoke.common.release.GrayRelease;
import com.google.common.collect.Lists;

import java.util.HashMap;
import java.util.List;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2020/11/10 4:39 PM
 */
public class TPMActivityBudgetObjListController extends StandardListController {

    protected static final ButtonDocument FLUSH_BUDGET_BUTTON = new ButtonDocument();

    static {
        FLUSH_BUDGET_BUTTON.put("_id", "FlushBudget_button_default");
        FLUSH_BUDGET_BUTTON.put("api_name", "FlushBudget");
        FLUSH_BUDGET_BUTTON.put("action", "FlushBudget");
        FLUSH_BUDGET_BUTTON.put("label", "刷新预算");
        FLUSH_BUDGET_BUTTON.put("action_type", "system");
        FLUSH_BUDGET_BUTTON.put("actions", Lists.newArrayList());
        FLUSH_BUDGET_BUTTON.put("button_type", "common");
        FLUSH_BUDGET_BUTTON.put("describe_api_name", "TPMActivityBudgetObj");
        FLUSH_BUDGET_BUTTON.put("is_active", true);
        FLUSH_BUDGET_BUTTON.put("is_deleted", false);
        FLUSH_BUDGET_BUTTON.put("use_pages", Lists.newArrayList("list", "detail"));
        FLUSH_BUDGET_BUTTON.put("where", Lists.newArrayList());
    }

    @Override
    protected Result after(Arg arg, Result result) {
        if (arg.isIncludeButtonInfo() && GrayRelease.isAllow("fmcg","FMCG_FLUSH_BUDGET",controllerContext.getTenantId())) {
            if (result.getButtonInfo().getButtons() == null) {
                result.getButtonInfo().setButtons(Lists.newArrayList(FLUSH_BUDGET_BUTTON));
            } else {
                result.getButtonInfo().getButtons().add(FLUSH_BUDGET_BUTTON);
            }
            List<String> buttonArr = Lists.newArrayList("FlushBudget");
            if (result.getButtonInfo().getButtonMap() == null) {
                result.getButtonInfo().setButtonMap(new HashMap<>());
                for (ObjectDataDocument obj : result.getDataList()) {
                    result.getButtonInfo().getButtonMap().put(obj.getId(), buttonArr);
                }
            } else {
                for (ObjectDataDocument obj : result.getDataList()) {
                    if (result.getButtonInfo().getButtonMap().containsKey(obj.getId())) {
                        result.getButtonInfo().getButtonMap().get(obj.getId()).add("FlushBudget");
                    } else {
                        result.getButtonInfo().getButtonMap().put(obj.getId(), buttonArr);
                    }
                }
            }
        }
        return super.after(arg, result);
    }
}

package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.common.release.GrayRelease;

public class TPMActivityProofAuditObjRelatedListController extends StandardRelatedListController {
    @Override
    protected SearchTemplateQuery buildSearchTemplateQuery() {
        SearchTemplateQuery query = super.buildSearchTemplateQuery();
        if (GrayRelease.isAllow("fmcg", "TPM_USE_ES_QUERY", controllerContext.getTenantId())) {
            query.setSearchSource("es");
        }
        return query;
    }
}

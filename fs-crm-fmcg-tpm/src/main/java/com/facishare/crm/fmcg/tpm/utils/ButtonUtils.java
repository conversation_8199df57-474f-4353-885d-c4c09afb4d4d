package com.facishare.crm.fmcg.tpm.utils;

import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.impl.ui.layout.Button;
import com.facishare.paas.metadata.impl.ui.layout.component.MultiTableComponent;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import de.lab4inf.math.util.Strings;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2021/9/8 下午3:31
 */
@Slf4j
public class ButtonUtils {

    public static IButton buildButton(ObjectAction objectAction) {
        IButton button = new Button();
        button.setAction(objectAction.getActionCode());
        button.setLabel(objectAction.getActionLabel());
        button.set("isActive", true);
        if(Strings.isNullOrEmpty(objectAction.getButtonApiName())){
            button.setName(objectAction.getActionCode() + "_button_" + IButton.ACTION_TYPE_DEFAULT);
        }else {
            button.setName(objectAction.getButtonApiName());
        }
        button.setActionType(IButton.ACTION_TYPE_DEFAULT);
        return button;
    }

    public static IButton buildButton(String actionCode, String actionLabel) {
        IButton button = new Button();
        button.setAction(actionCode);
        button.setLabel(actionLabel);
        button.set("isActive", true);
        button.setName(actionCode + "_button_" + IButton.ACTION_TYPE_DEFAULT);
        button.setActionType(IButton.ACTION_TYPE_DEFAULT);
        return button;
    }

    /**
     * 删除各个业务类型下面的button
     */
    public static ILayout removeButtons(List<String> processedApiName, String apiName, ILayout layout) {
        if (processedApiName.contains(apiName)) {
            removeButtons(layout);
            layout.setButtons(Lists.newArrayList());
        }
        return layout;
    }

    /**
     * 删除各个业务类型下面的button
     */
    public static ILayout removeButtons(ILayout layout) {
        layout.setButtons(Lists.newArrayList());
        LayoutExt layoutExt = LayoutExt.of(layout);
        try {
            layoutExt.getComponents().stream().filter(o -> "multi_table".equals(o.getType())).findFirst().ifPresent(
                    component -> {
                        try {
                            ((MultiTableComponent) component).getChildComponents().forEach(
                                    childComponent -> childComponent.setButtons(Lists.newArrayList())
                            );
                        } catch (Exception e) {
                            log.error("SalesOrderProductDetailListController tenantId:{}", layout.getTenantId(), e);
                        }

                    }
            );
        } catch (Exception e) {
            log.error("SalesOrderProductDetailListController tenantId:{}", layout.getTenantId(), e);
        }
        return layoutExt.getLayout();
    }
    /**
     * 增加各个业务类型下面的button
     */
    public static ILayout addButtons(ILayout layout,List<IButton> buttons) {
        layout.setButtons(buttons);
        LayoutExt layoutExt = LayoutExt.of(layout);
        try {
            layoutExt.getComponents().stream().filter(o -> "multi_table".equals(o.getType())).findFirst().ifPresent(
                    component -> {
                        try {
                            ((MultiTableComponent) component).getChildComponents().forEach(
                                    childComponent -> childComponent.setButtons(buttons)
                            );
                        }catch (Exception e){
                            log.error("SalesOrderProductDetailListController tenantId:{}", layout.getTenantId(), e);
                        }

                    }
            );
        } catch (Exception e) {
            log.error("SalesOrderProductDetailListController tenantId:{}", layout.getTenantId(), e);
        }
        return layoutExt.getLayout();
    }

    private ButtonUtils() {
    }

    public static void removeMobileEditButton(ILayout layout) {
        List<IButton> buttons = layout.getButtons();
        if (RequestUtil.isMobileRequest() || RequestUtil.isH5Request()) {
            buttons.removeIf(k -> k.getAction().equals(ObjectAction.CREATE.getActionCode())
                    || k.getAction().equals(ObjectAction.UPDATE.getActionCode())
                    || k.getAction().equals(ObjectAction.DELETE.getActionCode())
                    || k.getAction().equals(ObjectAction.RECOVER.getActionCode())
                    || k.getAction().equals(ObjectAction.INVALID.getActionCode())
                    || k.getAction().equals(ObjectAction.CHANGE_OWNER.getActionCode())
                    || k.getAction().equals(ObjectAction.CLONE.getActionCode()));
            layout.setButtons(buttons);
        }
    }

    public static void removeButtonByArg(ILayout layout, Set<String> actionCode) {
        List<IButton> buttons = layout.getButtons();
        buttons.removeIf(k -> actionCode.contains(k.getAction()));
        layout.setButtons(buttons);
    }

}

package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.apiname.CommonFields;
import com.facishare.crm.fmcg.tpm.apiname.TPMActivityAgreementFields;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardFlowCompletedAction;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/7/30 11:25
 */
@Slf4j
public class TPMActivityAgreementObjFlowCompletedAction extends StandardFlowCompletedAction {

    @Override
    protected void before(Arg arg) {
        super.before(arg);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        log.info("TPMActivityAgreement flow complete call back arg : {}", JSON.toJSONString(arg));

        IObjectData agreement = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), arg.getDataId(), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);

        long begin = (long) agreement.get(TPMActivityAgreementFields.BEGIN_DATE);
        long end = (long) agreement.get(TPMActivityAgreementFields.END_DATE);

        String lifeStatus = agreement.get(CommonFields.LIFE_STATUS, String.class);
        long now = System.currentTimeMillis();

        String status;

        if (TPMActivityAgreementFields.AGREEMENT_STATUS__INVALID.equals(agreement.get(TPMActivityAgreementFields.AGREEMENT_STATUS, String.class))) {
            status = TPMActivityAgreementFields.AGREEMENT_STATUS__INVALID;
        } else {
            if (now < begin) {
                status = TPMActivityAgreementFields.AGREEMENT_STATUS__SCHEDULE;
            } else if (now < end) {
                if (ObjectLifeStatus.NORMAL.getCode().equals(lifeStatus)) {
                    status = TPMActivityAgreementFields.AGREEMENT_STATUS__IN_PROGRESS;
                } else {
                    status = TPMActivityAgreementFields.AGREEMENT_STATUS__SCHEDULE;
                }
            } else {
                status = TPMActivityAgreementFields.AGREEMENT_STATUS__END;
            }
        }

        Map<String, Object> updateField = new HashMap<>();
        updateField.put(TPMActivityAgreementFields.AGREEMENT_STATUS, status);

        log.info("TPMActivityAgreement flow complete update agreement id:{} ,map : {}", agreement.getId(), updateField);

        serviceFacade.updateWithMap(User.systemUser(actionContext.getTenantId()), agreement, updateField);

        return super.after(arg, result);
    }
}

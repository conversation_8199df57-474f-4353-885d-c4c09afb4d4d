package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.tpm.api.proof.PreAdd;
import com.facishare.crm.fmcg.tpm.apiname.*;
import com.facishare.crm.fmcg.tpm.business.StoreBusiness;
import com.facishare.crm.fmcg.tpm.common.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.common.release.GrayRelease;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.jetbrains.annotations.NotNull;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 12/2/20 4:12 PM
 */
@SuppressWarnings("Duplicates")
public class TPMActivityProofObjPreAddController extends PreDefineController<PreAdd.Arg, PreAdd.Result> {

    private static final StoreBusiness storeBusiness = SpringUtil.getContext().getBean(StoreBusiness.class);

    @Override
    protected PreAdd.Result doService(PreAdd.Arg arg) {

        if (Strings.isNullOrEmpty(arg.getVisitId()) || Strings.isNullOrEmpty(arg.getActionId())) {
            throw new ValidateException("visit_id or action_id can not be empty.");
        }

        IObjectData storeData = serviceFacade.findObjectDataIgnoreAll(User.systemUser(controllerContext.getTenantId()), arg.getStoreId(), ApiNames.ACCOUNT_OBJ);
        if (storeData == null) {
            throw new ValidateException("store not found.");
        }

        IObjectData activityData = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()), arg.getActivityId(), ApiNames.TPM_ACTIVITY_OBJ);
        if (activityData == null) {
            throw new ValidateException("activity not found.");
        }

        boolean activityIsAgreementRequired = Boolean.TRUE.equals(activityData.get(TPMActivityFields.IS_AGREEMENT_REQUIRED));

        IObjectData agreementData = null;
        if (activityIsAgreementRequired && !TPMGrayUtils.agreementNotRelatedToActivity(controllerContext.getTenantId())) {
            long now = System.currentTimeMillis();
            agreementData = findAgreement(controllerContext, now, arg.getActivityId(), arg.getStoreId());
        } else if (TPMGrayUtils.agreementNotRelatedToActivity(controllerContext.getTenantId()) && !Strings.isNullOrEmpty(arg.getActivityAgreementId())) {
            agreementData = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()), arg.getActivityAgreementId(), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);
        }

        IObjectData dealerData;
        if (GrayRelease.isAllow("fmcg", "YINLU_TPM", controllerContext.getTenantId()) && agreementData != null) {
            String agreementDealerId = agreementData.get(TPMActivityAgreementFields.DEALER_ID, String.class);
            if (Strings.isNullOrEmpty(agreementDealerId)) {
                throw new ValidateException(I18N.text(I18NKeys.DEALER_ID_FIELD_IS_NULL_CAN_NOT_PROOF));
            }
            dealerData = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()), agreementDealerId, ApiNames.ACCOUNT_OBJ);
            if (dealerData == null) {
                throw new ValidateException(I18N.text(I18NKeys.DEALER_ID_FIELD_IS_NULL_CAN_NOT_PROOF));
            }
        } else if (TPMGrayUtils.dealerProofEnable(controllerContext.getTenantId())) {
            String dealerId = storeBusiness.findDealerId(controllerContext.getTenantId(), storeData);
            if (Strings.isNullOrEmpty(dealerId)) {
                String recordType = storeBusiness.findDealerRecordType(controllerContext.getTenantId());
                if (recordType.equals(storeData.getRecordType())) {
                    dealerId = storeData.getId();
                } else {
                    throw new ValidateException("当前所选客户所属经销商字段为空，无法进行举证。");
                }
            }
            dealerData = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()), dealerId, ApiNames.ACCOUNT_OBJ);
            if (dealerData == null) {
                throw new ValidateException(I18N.text(I18NKeys.DEALER_ID_FIELD_IS_NULL_CAN_NOT_PROOF));
            }
        } else {
            String dealerId = storeBusiness.findDealerId(controllerContext.getTenantId(), storeData);
            if (Strings.isNullOrEmpty(dealerId)) {
                throw new ValidateException(I18N.text(I18NKeys.DEALER_ID_FIELD_IS_NULL_CAN_NOT_PROOF));
            }

            dealerData = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()), dealerId, ApiNames.ACCOUNT_OBJ);
            if (dealerData == null) {
                throw new ValidateException(I18N.text(I18NKeys.DEALER_ID_FIELD_IS_NULL_CAN_NOT_PROOF));
            }
        }
        return convertToPreAddResult(arg, storeData, activityData, agreementData, dealerData, activityIsAgreementRequired);
    }

    private IObjectData findAgreement(ControllerContext context, long now, String activityId, String storeId) {

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnCountNum(false);
        query.setSearchSource("db");

        Filter beginDateFilter = new Filter();
        beginDateFilter.setFieldName(TPMActivityAgreementFields.BEGIN_DATE);
        beginDateFilter.setOperator(Operator.LT);
        beginDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

        Filter endDateFilter = new Filter();
        endDateFilter.setFieldName(TPMActivityAgreementFields.END_DATE);
        endDateFilter.setOperator(Operator.GT);
        endDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

        Filter activityIdFilter = new Filter();
        activityIdFilter.setFieldName(TPMActivityAgreementFields.ACTIVITY_ID);
        activityIdFilter.setOperator(Operator.EQ);
        activityIdFilter.setFieldValues(Lists.newArrayList(activityId));

        Filter storeIdFilter = new Filter();
        storeIdFilter.setFieldName(TPMActivityAgreementFields.STORE_ID);
        storeIdFilter.setOperator(Operator.EQ);
        storeIdFilter.setFieldValues(Lists.newArrayList(storeId));

        query.setFilters(Lists.newArrayList(beginDateFilter, endDateFilter, activityIdFilter, storeIdFilter));
        List<IObjectData> agreements = CommonUtils.queryData(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, query);

        if (agreements.size() > 0) {
            return agreements.get(0);
        }
        return null;
    }

    @NotNull
    private PreAdd.Result convertToPreAddResult(PreAdd.Arg arg, IObjectData storeData, IObjectData activityData, IObjectData agreementData, IObjectData dealerData, boolean activityIsAgreementRequired) {
        PreAdd.Result data = new PreAdd.Result();

        data.setActivityIsAgreementRequired(Boolean.TRUE.equals(activityData.get(TPMActivityFields.IS_AGREEMENT_REQUIRED)));
        String dealerRecordType = storeData.getRecordType();
        data.setIsDealerActivity(!Strings.isNullOrEmpty(dealerRecordType) && dealerRecordType.equals(storeBusiness.findDealerRecordType(controllerContext.getTenantId())));
        PreAdd.ProofDataVO proof = new PreAdd.ProofDataVO();

        proof.setStoreId(storeData.getId());
        proof.setStoreName(storeData.getName());
        proof.setDealerId(dealerData.getId());
        proof.setDealerName(dealerData.getName());
        proof.setActivityId(activityData.getId());
        proof.setActivityName(activityData.getName());
        proof.setVisitId(arg.getVisitId());
        proof.setActionId(arg.getActionId());
        proof.setAuditStatus(TPMActivityProofFields.AUDIT_STATUS__SCHEDULE);
        proof.setOwner(Lists.newArrayList(controllerContext.getUser().getUpstreamOwnerIdOrUserId()));

        String activityStoreId = findActivityStoreId(controllerContext, activityData.getId(), storeData.getId());
        if (!Strings.isNullOrEmpty(activityStoreId)) {
            proof.setActivityStoreId(activityStoreId);
            proof.setActivityStoreName(storeData.getName());
        }

        data.setObjectData(proof);
        data.setDetails(new HashMap<>());

        List<IObjectData> activityDetails = queryActivityDetail(controllerContext, activityData.getId());
        if (agreementData != null) {
            proof.setActivityAgreementId(agreementData.getId());
            proof.setActivityAgreementName(agreementData.getName());

            if (TPMGrayUtils.agreementNotRelatedToActivity(controllerContext.getTenantId())) {
                List<IObjectData> agreementDetails = queryActivityAgreementDetailMap(controllerContext, proof.getActivityAgreementId());
                List<PreAdd.ProofDetailDataVO> details = Lists.newArrayList();
                for (IObjectData agreementDetail : agreementDetails) {
                    PreAdd.ProofDetailDataVO detail = new PreAdd.ProofDetailDataVO();

                    detail.setActivityAgreementDetailId(agreementDetail.getId());
                    detail.setActivityAgreementDetailName(agreementDetail.getName());
                    detail.setActivityItemId((String) agreementDetail.get(TPMActivityAgreementDetailFields.ACTIVITY_ITEM_ID));
                    detail.setAmountStandardCheck(String.valueOf(agreementDetail.get(TPMActivityAgreementDetailFields.AMOUNT_STANDARD_CHECK)));
                    detail.setAmountStandardCheckValue((Boolean) agreementDetail.get(TPMActivityAgreementDetailFields.AMOUNT_STANDARD_CHECK__V));
                    detail.setCalculatePattern((String) agreementDetail.get(TPMActivityAgreementDetailFields.CALCULATE_PATTERN));
                    detail.setCalculatePatternValue((String) agreementDetail.get(TPMActivityAgreementDetailFields.CALCULATE_PATTERN__V));
                    detail.setCalculatePatternLabel((String) agreementDetail.get(TPMActivityAgreementDetailFields.CALCULATE_PATTERN__R));
                    detail.setType((String) agreementDetail.get(TPMActivityAgreementDetailFields.TYPE));
                    detail.setAmountStandard(Double.parseDouble(agreementDetail.get(TPMActivityAgreementDetailFields.AGREEMENT_AMOUNT_STANDARD).toString()));
                    detail.setActivityCostStandard(Double.parseDouble(agreementDetail.get(TPMActivityAgreementDetailFields.AGREEMENT_COST_STANDARD).toString()));
                    detail.setAgreementAmountStandard(Double.parseDouble(agreementDetail.get(TPMActivityAgreementDetailFields.AGREEMENT_AMOUNT_STANDARD).toString()));
                    detail.setProofDetailAmountStandard(Double.parseDouble(agreementDetail.get(TPMActivityAgreementDetailFields.AGREEMENT_AMOUNT_STANDARD).toString()));
                    detail.setProofDetailCostStandard(Double.parseDouble(agreementDetail.get(TPMActivityAgreementDetailFields.AGREEMENT_COST_STANDARD).toString()));
                    IObjectData costStandard = queryCostStandard(arg.getStoreId(), detail.getActivityItemId());
                    detail.setActivityItemCostStandardId(costStandard == null ? null : costStandard.getId());

                    if (TPMGrayUtils.proofDataTypeAllUseExistOrNot(controllerContext.getTenantId())) {
                        detail.setProofDataType(TPMActivityItemFields.PROOF_DATA_TYPE__EXIST_OR_NOT);
                    } else {
                        detail.setProofDataType(TPMActivityItemFields.PROOF_DATA_TYPE__NUMBER);
                    }

                    details.add(detail);
                }
                data.getDetails().put(ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ, details);
            } else {
                List<IObjectData> agreementDetails = queryActivityAgreementDetailMap(controllerContext, proof.getActivityAgreementId());
                Map<String, IObjectData> agreementDetailMap = new HashMap<>();
                for (IObjectData datum : agreementDetails) {
                    agreementDetailMap.put((String) datum.get(TPMActivityAgreementDetailFields.ACTIVITY_DETAIL_ID), datum);
                }

                List<PreAdd.ProofDetailDataVO> details = Lists.newArrayList();
                for (IObjectData activityDetail : activityDetails) {
                    if (!agreementDetailMap.containsKey(activityDetail.getId())) {
                        continue;
                    }
                    IObjectData agreementDetail = agreementDetailMap.get(activityDetail.getId());
                    PreAdd.ProofDetailDataVO detail = new PreAdd.ProofDetailDataVO();

                    detail.setAmountStandardCheck(String.valueOf(activityDetail.get(TPMActivityDetailFields.AMOUNT_STANDARD_CHECK)));
                    detail.setAmountStandardCheckValue((Boolean) activityDetail.get(TPMActivityDetailFields.AMOUNT_STANDARD_CHECK__V));
                    detail.setCalculatePattern((String) activityDetail.get(TPMActivityDetailFields.CALCULATE_PATTERN));
                    detail.setCalculatePatternValue((String) activityDetail.get(TPMActivityDetailFields.CALCULATE_PATTERN__V));
                    detail.setCalculatePatternLabel((String) activityDetail.get(TPMActivityDetailFields.CALCULATE_PATTERN__R));
                    detail.setActivityItemId((String) activityDetail.get(TPMActivityDetailFields.ACTIVITY_ITEM_ID));
                    detail.setActivityDetailId(activityDetail.getId());
                    detail.setActivityDetailName(activityDetail.getName());
                    detail.setType((String) activityDetail.get(TPMActivityDetailFields.TYPE));
                    detail.setActivityAgreementDetailId(agreementDetail.getId());
                    detail.setActivityAgreementDetailName(agreementDetail.getName());
                    detail.setAmountStandard(Double.parseDouble(agreementDetail.get(TPMActivityAgreementDetailFields.AGREEMENT_AMOUNT_STANDARD).toString()));
                    detail.setActivityCostStandard(Double.parseDouble(agreementDetail.get(TPMActivityAgreementDetailFields.AGREEMENT_COST_STANDARD).toString()));
                    detail.setAgreementAmountStandard(Double.parseDouble(agreementDetail.get(TPMActivityAgreementDetailFields.AGREEMENT_AMOUNT_STANDARD).toString()));
                    detail.setProofDetailAmountStandard(Double.parseDouble(agreementDetail.get(TPMActivityAgreementDetailFields.AGREEMENT_AMOUNT_STANDARD).toString()));
                    detail.setProofDetailCostStandard(Double.parseDouble(agreementDetail.get(TPMActivityAgreementDetailFields.AGREEMENT_COST_STANDARD).toString()));
                    IObjectData costStandard = queryCostStandard(arg.getStoreId(), detail.getActivityItemId());
                    detail.setActivityItemCostStandardId(costStandard == null ? null : costStandard.getId());

                    if (TPMGrayUtils.proofDataTypeAllUseExistOrNot(controllerContext.getTenantId())) {
                        detail.setProofDataType(TPMActivityItemFields.PROOF_DATA_TYPE__EXIST_OR_NOT);
                    } else {
                        detail.setProofDataType(TPMActivityItemFields.PROOF_DATA_TYPE__NUMBER);
                    }

                    details.add(detail);
                }
                data.getDetails().put(ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ, details);
            }
        } else if (!activityIsAgreementRequired) {
            List<PreAdd.ProofDetailDataVO> details = Lists.newArrayList();
            for (IObjectData activityDetail : activityDetails) {
                PreAdd.ProofDetailDataVO detail = new PreAdd.ProofDetailDataVO();
                detail.setAmountStandardCheck(String.valueOf(activityDetail.get(TPMActivityDetailFields.AMOUNT_STANDARD_CHECK)));
                detail.setAmountStandardCheckValue((Boolean) activityDetail.get(TPMActivityDetailFields.AMOUNT_STANDARD_CHECK__V));
                detail.setCalculatePattern((String) activityDetail.get(TPMActivityDetailFields.CALCULATE_PATTERN));
                detail.setCalculatePatternValue((String) activityDetail.get(TPMActivityDetailFields.CALCULATE_PATTERN__V));
                detail.setCalculatePatternLabel((String) activityDetail.get(TPMActivityDetailFields.CALCULATE_PATTERN__R));
                detail.setActivityItemId((String) activityDetail.get(TPMActivityDetailFields.ACTIVITY_ITEM_ID));
                detail.setActivityDetailId(activityDetail.getId());
                detail.setActivityDetailName(activityDetail.getName());
                detail.setType((String) activityDetail.get(TPMActivityDetailFields.TYPE));
                if (TPMGrayUtils.proofDataTypeAllUseExistOrNot(controllerContext.getTenantId())) {
                    detail.setProofDataType(TPMActivityItemFields.PROOF_DATA_TYPE__EXIST_OR_NOT);
                } else {
                    detail.setProofDataType(TPMActivityItemFields.PROOF_DATA_TYPE__NUMBER);
                }
                IObjectData costStandard = queryCostStandard(arg.getStoreId(), detail.getActivityItemId());
                if (costStandard == null) {
                    detail.setAmountStandard(Double.parseDouble(activityDetail.get(TPMActivityDetailFields.ACTIVITY_AMOUNT_STANDARD).toString()));
                    detail.setActivityCostStandard(Double.parseDouble(activityDetail.get(TPMActivityDetailFields.ACTIVITY_COST_STANDARD).toString()));
                    detail.setProofDetailAmountStandard(Double.parseDouble(activityDetail.get(TPMActivityDetailFields.ACTIVITY_AMOUNT_STANDARD).toString()));
                    detail.setProofDetailCostStandard(Double.parseDouble(activityDetail.get(TPMActivityDetailFields.ACTIVITY_COST_STANDARD).toString()));
                    detail.setActivityItemCostStandardId(null);
                } else {
                    detail.setAmountStandard(Double.parseDouble(costStandard.get(TPMActivityItemCostStandardFields.AMOUNT_STANDARD).toString()));
                    detail.setActivityCostStandard(Double.parseDouble(costStandard.get(TPMActivityItemCostStandardFields.COST_STANDARD).toString()));
                    detail.setProofDetailAmountStandard(Double.parseDouble(costStandard.get(TPMActivityItemCostStandardFields.AMOUNT_STANDARD).toString()));
                    detail.setProofDetailCostStandard(Double.parseDouble(costStandard.get(TPMActivityItemCostStandardFields.COST_STANDARD).toString()));
                    detail.setActivityItemCostStandardId(costStandard.getId());
                }
                details.add(detail);
            }
            data.getDetails().put(ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ, details);
        }
        return data;
    }

    private String findActivityStoreId(ControllerContext controllerContext, String activityId, String storeId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(1);
        query.setOffset(0);
        query.setNeedReturnCountNum(false);
        query.setSearchSource("db");

        Filter activityFilter = new Filter();
        activityFilter.setFieldName(TPMActivityStoreFields.ACTIVITY_ID);
        activityFilter.setOperator(Operator.EQ);
        activityFilter.setFieldValues(Lists.newArrayList(activityId));

        Filter storeFilter = new Filter();
        storeFilter.setFieldName(TPMActivityStoreFields.STORE_ID);
        storeFilter.setOperator(Operator.EQ);
        storeFilter.setFieldValues(Lists.newArrayList(storeId));

        query.setFilters(Lists.newArrayList(activityFilter, storeFilter));

        QueryResult<IObjectData> result = serviceFacade.findBySearchQuery(User.systemUser(controllerContext.getTenantId()), ApiNames.TPM_ACTIVITY_STORE_OBJ, query);

        if (result.getData().isEmpty()) {
            return null;
        }
        return result.getData().get(0).getId();
    }

    private List<IObjectData> queryActivityDetail(ControllerContext context, String activityId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnCountNum(false);
        query.setSearchSource("db");

        Filter masterFilter = new Filter();
        masterFilter.setFieldName(TPMActivityDetailFields.ACTIVITY_ID);
        masterFilter.setOperator(Operator.EQ);
        masterFilter.setFieldValues(Lists.newArrayList(activityId));

        query.setFilters(Lists.newArrayList(masterFilter));
        return CommonUtils.queryData(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_DETAIL_OBJ, query);
    }

    private List<IObjectData> queryActivityAgreementDetailMap(ControllerContext context, String activityAgreementId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        //todo: 原先limit 0
        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnCountNum(false);
        query.setSearchSource("db");

        Filter masterFilter = new Filter();
        masterFilter.setFieldName(TPMActivityAgreementDetailFields.ACTIVITY_AGREEMENT_ID);
        masterFilter.setOperator(Operator.EQ);
        masterFilter.setFieldValues(Lists.newArrayList(activityAgreementId));

        query.setFilters(Lists.newArrayList(masterFilter));
        return CommonUtils.queryData(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_AGREEMENT_DETAIL_OBJ, query);


    }

    private IObjectData queryCostStandard(String storeId, String activityItemId) {
        SearchTemplateQuery costStandardQuery = new SearchTemplateQuery();
        costStandardQuery.setLimit(-1);
        costStandardQuery.setOffset(0);
        costStandardQuery.setNeedReturnCountNum(false);
        costStandardQuery.setSearchSource("db");

        Filter activityItemFilter = new Filter();
        activityItemFilter.setFieldName(TPMActivityItemCostStandardFields.ACTIVITY_ITEM);
        activityItemFilter.setOperator(Operator.EQ);
        activityItemFilter.setFieldValues(Lists.newArrayList(activityItemId));
        costStandardQuery.setFilters(Lists.newArrayList(activityItemFilter));

        OrderBy orderBy = new OrderBy();
        orderBy.setIsAsc(false);
        orderBy.setFieldName(CommonFields.CREATE_TIME);
        costStandardQuery.setOrders(Lists.newArrayList(orderBy));

        List<IObjectData> costStandards = CommonUtils.queryData(serviceFacade, User.systemUser(controllerContext.getTenantId()), ApiNames.TPM_ACTIVITY_ITEM_COST_STANDARD, costStandardQuery);

        SearchTemplateQuery storeQuery = new SearchTemplateQuery();
        storeQuery.setLimit(1);
        storeQuery.setOffset(0);
        storeQuery.setNeedReturnCountNum(false);
        storeQuery.setSearchSource("db");

        Filter idFilter = new Filter();
        idFilter.setFieldName(CommonFields.ID);
        idFilter.setOperator(Operator.IN);
        idFilter.setFieldValues(Lists.newArrayList(storeId));

        for (IObjectData standard : costStandards) {
            storeQuery.getFilters().clear();
            storeQuery.setFilters(Lists.newArrayList(idFilter));
            String jsonQuery = standard.get(TPMActivityItemCostStandardFields.STORE_RANGE, String.class, "");
            if (!Strings.isNullOrEmpty(jsonQuery)) {
                JSONObject rangeData = JSON.parseObject(jsonQuery);
                switch (rangeData.getString("type")) {
                    case "ALL":
                        return standard;
                    case "CONDITION":
                        List<Wheres> wheres = JSON.parseArray(rangeData.getString("value"), Wheres.class);
                        storeQuery.setWheres(wheres);
                        if (serviceFacade.findBySearchQuery(User.systemUser(controllerContext.getTenantId()), ApiNames.ACCOUNT_OBJ, storeQuery).getData().size() > 0)
                            return standard;
                        continue;
                    default:
                }
            }
        }
        return null;
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Collections.emptyList();
    }
}
package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.tpm.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.apiname.TPMActivityProofAuditFields;
import com.facishare.crm.fmcg.tpm.apiname.TPMActivityProofFields;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.base.Strings;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/8/4 下午3:35
 */
public class TPMActivityProofAuditObjInvalidAction extends StandardInvalidAction {


    @Override
    protected void before(Arg arg) {

        IObjectData audit = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()),arg.getObjectDataId(), ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ);
        String costId = audit.get(TPMActivityProofAuditFields.DEALER_ACTIVITY_COST_ID,String.class);

        if(!Strings.isNullOrEmpty(costId)){
            throw new ValidateException(I18N.text(I18NKeys.SINGLE_AUDIT_INVALID_FAIL_DUE_TO_RELATED_BY_COST));
        }
        super.before(arg);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result result1 =  super.after(arg, result);
        String proofId = (String)result1.getObjectData().get(TPMActivityProofAuditFields.ACTIVITY_PROOF_ID);
        IObjectData proof = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()),proofId,ApiNames.TPM_ACTIVITY_PROOF_OBJ);
        Map<String,Object> updateMap = new HashMap<>();
        updateMap.put(TPMActivityProofFields.AUDIT_STATUS,"schedule");
        updateMap.put(TPMActivityProofFields.RANDOM_AUDIT_STATUS,null);
        serviceFacade.updateWithMap(User.systemUser(actionContext.getTenantId()),proof,updateMap);
        return result1;
    }
}

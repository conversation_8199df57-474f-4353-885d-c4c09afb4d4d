package com.facishare.crm.fmcg.tpm.api.activity;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

public interface AccountUpdate {

    @Data
    @ToString
    class Arg implements Serializable {

        @SerializedName("name")
        @JSONField(name = "name")
        @JsonProperty("name")
        private String name;
    }

    @Data
    @ToString
    class Result implements Serializable {
    }
}
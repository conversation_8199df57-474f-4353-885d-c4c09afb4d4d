package com.facishare.crm.fmcg.tpm.service;

import com.fxiaoke.cloud.DataPersistor;

import java.util.HashMap;
import java.util.Map;



public class BuryService {
    private static final String MODULE = "fs_crm_fmcg_service_tpm";
    private static final String SERVICE = "fs_crm_fmcg_service";

    private BuryService(){

    }

    public static void asyncTpmLog(Integer enterpriseId, Integer employeeId, String subModule,String operation) {
        Map<String, Object> map = new HashMap<>();
        buildupSimpleLog(enterpriseId, employeeId,MODULE,subModule, operation, map);
        DataPersistor.asyncLog(SERVICE, map);
    }

    public static void asyncLog(Integer enterpriseId, Integer employeeId, String module,String subModule,String operation) {
        Map<String, Object> map = new HashMap<>();
        buildupSimpleLog(enterpriseId, employeeId, module,subModule,operation, map);
        DataPersistor.asyncLog(SERVICE, map);
    }

    private static void buildupSimpleLog(Integer enterpriseId, Integer employeeId,String module,String subModule, String operation, Map<String, Object> data) {
        data.put("tenantId", enterpriseId);
        data.put("userId", employeeId);
        data.put("fullUserId", enterpriseId + "." + employeeId);
        data.put("module", module);
        data.put("subModule", subModule);
        data.put("operation", operation);
        data.put("eventId", module + "_" + subModule + "_" + operation);
    }
}

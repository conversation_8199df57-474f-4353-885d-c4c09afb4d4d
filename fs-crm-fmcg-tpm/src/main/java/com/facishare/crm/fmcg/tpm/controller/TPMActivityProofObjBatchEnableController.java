package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.tpm.api.proof.BatchEnable;
import com.facishare.crm.fmcg.tpm.apiname.*;
import com.facishare.crm.fmcg.tpm.business.StoreBusiness;
import com.facishare.crm.fmcg.tpm.business.abstraction.IDescribeCacheService;
import com.facishare.crm.fmcg.tpm.common.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.service.OrganizationService;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * author: wuyx
 * description:
 * createTime: 2022/5/10 17:44
 */
@SuppressWarnings("Duplicates")
public class TPMActivityProofObjBatchEnableController extends PreDefineController<BatchEnable.Arg, BatchEnable.Result> {

    private static final OrganizationService organizationService = SpringUtil.getContext().getBean(OrganizationService.class);
    private static final StoreBusiness storeBusiness = SpringUtil.getContext().getBean(StoreBusiness.class);
    private static final IDescribeCacheService I_DESCRIBE_CACHE_SERVICE = SpringUtil.getContext().getBean(IDescribeCacheService.class);

    @Override
    protected BatchEnable.Result doService(BatchEnable.Arg arg) {
        log.info("batchEnable arg : {}", JSON.toJSONString(arg));

        stopWatch.lap("batchEnable start");

        BatchEnable.Result enableResult = new BatchEnable.Result();
        if (CollectionUtils.isEmpty(arg.getStoreIds())) {
            enableResult.setStoreIdEnable(Maps.newHashMap());
            return enableResult;
        }

        List<String> storeIds = arg.getStoreIds().stream().distinct().collect(Collectors.toList());
        Map<String, Boolean> batchEnableMap = Maps.newHashMap();
        storeIds.forEach(s -> batchEnableMap.put(s, false));

        if (TPMGrayUtils.skipEnableCheck(controllerContext.getTenantId())) {
            storeIds.forEach(s -> batchEnableMap.put(s, true));
            enableResult.setStoreIdEnable(batchEnableMap);
            return enableResult;
        }

        List<IObjectData> storeDataByIds = serviceFacade.findObjectDataByIds(controllerContext.getTenantId(), arg.getStoreIds(), ApiNames.ACCOUNT_OBJ);

        Map<String, String> storeIdDealerId = storeDataByIds.stream().collect(Collectors.toMap(IObjectData::getId,
                o -> {
                    String dealerId = storeBusiness.findDealerId(controllerContext.getTenantId(), o);
                    return Objects.isNull(dealerId) ? "" : dealerId;
                }));

        List<String> dealerIds = storeIdDealerId.values().stream().filter(o -> !o.isEmpty()).distinct().collect(Collectors.toList());

        if (!TPMGrayUtils.isYinLu(controllerContext.getTenantId()) && !TPMGrayUtils.dealerProofEnable(controllerContext.getTenantId())) {
            if (CollectionUtils.isEmpty(dealerIds)) {
                enableResult.setStoreIdEnable(batchEnableMap);
                return enableResult;
            }
            List<IObjectData> dealerDatas = serviceFacade.findObjectDataByIds(controllerContext.getTenantId(), dealerIds, ApiNames.ACCOUNT_OBJ);
            if (CollectionUtils.isEmpty(dealerDatas)) {
                enableResult.setStoreIdEnable(batchEnableMap);
                return enableResult;
            }
            List<String> yinLuDealerIds = dealerDatas.stream().map(IObjectData::getId).distinct().collect(Collectors.toList());
            dealerIds = yinLuDealerIds;
            storeIdDealerId = storeIdDealerId.entrySet().stream().filter(map -> yinLuDealerIds.contains(map.getKey())).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        }

        stopWatch.lap("batchEnable enableCheck");
        enableResult.setStoreIdEnable(enableCheck(controllerContext, dealerIds, storeIdDealerId, batchEnableMap));

        log.info("batchEnable result : {}", enableResult);

        stopWatch.lap("batchEnable end");
        return enableResult;
    }

    private Map<String, Boolean> enableCheck(
            ControllerContext context,
            List<String> dealerIds,
            Map<String, String> storeIdDealerId,
            Map<String, Boolean> batchEnableMap) {

        boolean isApplets = controllerContext.getUser().isOutUser();
        List<Integer> departmentIds = isApplets ? Lists.newArrayList() : organizationService.getDepartmentIds(controllerContext.getUser().getTenantIdInt(), controllerContext.getUser().getUserIdInt());
        if (!isApplets && CollectionUtils.isEmpty(departmentIds)) {
            return batchEnableMap;
        }

        long now = System.currentTimeMillis();
        List<String> activityStoreIds = Lists.newArrayList(storeIdDealerId.keySet());
        List<IObjectData> dealerActivity = queryActivityByDealerStore(context, now, departmentIds, activityStoreIds);
        if (!dealerActivity.isEmpty()) {
            List<String> activityDealerIds = dealerActivity.stream().distinct().map(o -> (String) o.get(TPMActivityFields.DEALER_ID)).collect(Collectors.toList());
            activityDealerIds.forEach(o -> batchEnableMap.put(o, true));
            activityStoreIds = activityStoreIds.stream().filter(storeId -> !activityDealerIds.contains(storeId)).collect(Collectors.toList());
        }

        // 根据有无经销商分开
        List<String> finalStoreIds = activityStoreIds;
        List<String> storeIdsNotDealerId = storeIdDealerId.keySet().stream().filter(s -> storeIdDealerId.get(s).isEmpty() && finalStoreIds.contains(s)).collect(Collectors.toList());
        List<String> storeIdsOfDealerId = storeIdDealerId.keySet().stream().filter(s -> !storeIdDealerId.get(s).isEmpty() && finalStoreIds.contains(s)).collect(Collectors.toList());

        List<IObjectData> activityByStore = queryActivityByStore(context, now, departmentIds);
        Map<String, List<IObjectData>> storeIdActivities = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(storeIdsNotDealerId)) {
            if (!CollectionUtils.isEmpty(activityByStore)) {
                for (String storeId : storeIdsNotDealerId) {
                    storeIdActivities.put(storeId, activityByStore);
                }
            }
        }

        if (!CollectionUtils.isEmpty(storeIdsOfDealerId)) {
            List<IObjectData> activityByDealerIds = queryActivityByStore(context, now, departmentIds, dealerIds);

            if (!CollectionUtils.isEmpty(activityByDealerIds)) {
                Map<String, List<IObjectData>> dealerIdActivities = activityByDealerIds.stream().collect(Collectors.groupingBy(o -> (String) o.get(TPMActivityFields.DEALER_ID)));
                for (String storeId : storeIdsOfDealerId) {
                    String dealerId = storeIdDealerId.get(storeId);
                    List<IObjectData> dealerIdActivitiesOrDefault = dealerIdActivities.getOrDefault(dealerId, Lists.newArrayList());
                    if (!CollectionUtils.isEmpty(activityByStore)) {
                        dealerIdActivitiesOrDefault.addAll(activityByStore);
                    }
                    storeIdActivities.put(storeId, dealerIdActivitiesOrDefault);
                }
            } else {
                if (!CollectionUtils.isEmpty(activityByStore)) {
                    for (String storeId : storeIdsOfDealerId) {
                        storeIdActivities.put(storeId, activityByStore);
                    }
                }
            }
        }


        if (TPMGrayUtils.isYinLu(context.getTenantId())) {
            storeIdActivities = queryHasAgreementActivity(context, now, storeIdActivities);
        }

        Map<String, List<IObjectData>> finalStoreIdActivities = storeIdActivities;
        storeIdActivities.keySet().forEach(
                storeId -> {
                    for (IObjectData activity : finalStoreIdActivities.get(storeId)) {
                        String storeRangeJson = (String) activity.get(TPMActivityFields.STORE_RANGE);
                        if (Strings.isNullOrEmpty(storeRangeJson)) {
                            continue;
                        }
                        JSONObject storeRange = JSON.parseObject(storeRangeJson);
                        String type = storeRange.getString("type");
                        switch (type) {
                            case "FIXED":
                                if (CollectionUtils.isEmpty(queryActivityStore(context, activity.getId(), storeId))) {
                                    continue;
                                }
                                break;
                            case "CONDITION":
                                if (CollectionUtils.isEmpty(queryConditionStore(context, storeRange, storeId))) {
                                    continue;
                                }
                                break;
                            default:
                            case "ALL":
                                break;
                        }
                        batchEnableMap.put(storeId, true);
                    }
                }
        );

        return batchEnableMap;
    }

    /**
     * query activities that has agreement related
     * <p>
     * ## YINLU custom
     *
     * @param context           controller context
     * @param now               current time stamp
     * @param storeIdActivities
     * @return has agreement related activities
     */
    private Map<String, List<IObjectData>> queryHasAgreementActivity(ControllerContext context, long now, Map<String, List<IObjectData>> storeIdActivities) {

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setNeedReturnCountNum(false);
        query.setSearchSource("db");

        Filter storeIdFilter = new Filter();
        storeIdFilter.setFieldName(TPMActivityAgreementFields.STORE_ID);
        storeIdFilter.setOperator(Operator.IN);
        storeIdFilter.setFieldValues(Lists.newArrayList(storeIdActivities.keySet()));

        List<String> activityIds = Lists.newArrayList();
        storeIdActivities.values().forEach(o -> activityIds.addAll(Lists.newArrayList(o.stream().map(DBRecord::getId).collect(Collectors.toList()))));
        Filter activityFilter = new Filter();
        activityFilter.setFieldName(TPMActivityAgreementFields.ACTIVITY_ID);
        activityFilter.setOperator(Operator.IN);
        activityFilter.setFieldValues(Lists.newArrayList(activityIds.stream().distinct().collect(Collectors.toList())));

        Filter beginTimeFilter = new Filter();
        beginTimeFilter.setFieldName(TPMActivityAgreementFields.BEGIN_DATE);
        beginTimeFilter.setOperator(Operator.LTE);
        beginTimeFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

        Filter endTimeFilter = new Filter();
        endTimeFilter.setFieldName(TPMActivityAgreementFields.END_DATE);
        endTimeFilter.setOperator(Operator.GTE);
        endTimeFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

        query.setFilters(Lists.newArrayList(storeIdFilter, activityFilter, beginTimeFilter, endTimeFilter));

        // query agreements that related to this store and activity
        List<IObjectData> agreements = CommonUtils.queryData(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, query);

        Map<String, List<IObjectData>> storeIdAgreements = agreements.stream().collect(Collectors.groupingBy(agreement -> (String) agreement.get(TPMActivityAgreementFields.STORE_ID)));
        Map<String, List<IObjectData>> storeIdActivitiesFilter = Maps.newHashMap();

        Set<String> storeIdSet = storeIdAgreements.keySet();
        for (String storeId : storeIdSet) {
            List<IObjectData> agreementList = storeIdAgreements.get(storeId);
            if (storeIdActivities.containsKey(storeId)) {
                List<String> hasAgreementActivityIds = agreementList.stream().map(agreement -> agreement.get(TPMActivityAgreementFields.ACTIVITY_ID, String.class)).collect(Collectors.toList());
                List<IObjectData> hasActivities = storeIdActivities.get(storeId).stream().filter(activity -> hasAgreementActivityIds.contains(activity.getId())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(hasActivities)) {
                    storeIdActivitiesFilter.put(storeId, hasActivities);
                }
            }
        }

        return storeIdActivitiesFilter;
    }

    private List<IObjectData> queryActivityByDealerStore(ControllerContext context, long now, List<Integer> departmentIds, List<String> dealerIds) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnCountNum(false);
        query.setSearchSource("db");

        Filter dealerIdFilter = new Filter();
        dealerIdFilter.setFieldName(TPMActivityFields.DEALER_ID);
        dealerIdFilter.setOperator(Operator.IN);
        dealerIdFilter.setFieldValues(Lists.newArrayList(dealerIds));

        Filter beginDateFilter = new Filter();
        beginDateFilter.setFieldName(TPMActivityFields.BEGIN_DATE);
        beginDateFilter.setOperator(Operator.LT);
        beginDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

        Filter endDateFilter = new Filter();
        endDateFilter.setFieldName(TPMActivityFields.END_DATE);
        endDateFilter.setOperator(Operator.GT);
        endDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

        Filter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.EQ);
        lifeStatusFilter.setFieldValues(Lists.newArrayList("normal"));

        query.setFilters(Lists.newArrayList(dealerIdFilter, beginDateFilter, endDateFilter, lifeStatusFilter));

        StringBuilder pattern = new StringBuilder(" 1 and 2 and 3 and 4 ");

        if (!controllerContext.getUser().isOutUser() && !TPMGrayUtils.denyDepartmentFilterOnActivity(context.getTenantId())) {

            Filter departmentRangeFilter = new Filter();
            departmentRangeFilter.setFieldName(TPMActivityFields.DEPARTMENT_RANGE);
            departmentRangeFilter.setOperator(Operator.IN);
            departmentRangeFilter.setFieldValues(departmentIds.stream().map(Object::toString).collect(Collectors.toList()));

            Filter multiDepartmentRangeFilter = new Filter();
            multiDepartmentRangeFilter.setFieldName(TPMActivityFields.MULTI_DEPARTMENT_RANGE);
            multiDepartmentRangeFilter.setOperator(Operator.HASANYOF);
            multiDepartmentRangeFilter.setFieldValues(departmentIds.stream().map(Object::toString).collect(Collectors.toList()));

            if (I_DESCRIBE_CACHE_SERVICE.isExistField(context.getTenantId(), ApiNames.TPM_ACTIVITY_OBJ, TPMActivityFields.DEPARTMENT_RANGE)) {
                query.getFilters().addAll(Lists.newArrayList(multiDepartmentRangeFilter, departmentRangeFilter));
                pattern.append(" and (5 or 6) ");
            } else {
                query.getFilters().add(multiDepartmentRangeFilter);
                pattern.append(" and 5 ");
            }
        }
        query.setPattern(pattern.toString());
        return CommonUtils.queryData(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_OBJ, query);
    }

    private List<IObjectData> queryActivityByStore(ControllerContext context, long now, List<Integer> departmentIds, List<String> dealerIds) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnCountNum(false);
        query.setSearchSource("db");

        Filter beginDateFilter = new Filter();
        beginDateFilter.setFieldName(TPMActivityFields.BEGIN_DATE);
        beginDateFilter.setOperator(Operator.LT);
        beginDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

        Filter endDateFilter = new Filter();
        endDateFilter.setFieldName(TPMActivityFields.END_DATE);
        endDateFilter.setOperator(Operator.GT);
        endDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

        Filter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.EQ);
        lifeStatusFilter.setFieldValues(Lists.newArrayList("normal"));

        Filter dealerIdEqualFilter = new Filter();
        dealerIdEqualFilter.setFieldName(TPMActivityFields.DEALER_ID);
        dealerIdEqualFilter.setOperator(Operator.IN);
        dealerIdEqualFilter.setFieldValues(Lists.newArrayList(dealerIds));

        query.setFilters(Lists.newArrayList(beginDateFilter, endDateFilter, lifeStatusFilter, dealerIdEqualFilter));
        StringBuilder pattern = new StringBuilder(" 1 and 2 and 3 and 4 ");

        if (!controllerContext.getUser().isOutUser() && !TPMGrayUtils.denyDepartmentFilterOnActivity(context.getTenantId())) {

            Filter departmentRangeFilter = new Filter();
            departmentRangeFilter.setFieldName(TPMActivityFields.DEPARTMENT_RANGE);
            departmentRangeFilter.setOperator(Operator.IN);
            departmentRangeFilter.setFieldValues(departmentIds.stream().map(Object::toString).collect(Collectors.toList()));

            Filter multiDepartmentRangeFilter = new Filter();
            multiDepartmentRangeFilter.setFieldName(TPMActivityFields.MULTI_DEPARTMENT_RANGE);
            multiDepartmentRangeFilter.setOperator(Operator.HASANYOF);
            multiDepartmentRangeFilter.setFieldValues(departmentIds.stream().map(Object::toString).collect(Collectors.toList()));

            if (I_DESCRIBE_CACHE_SERVICE.isExistField(context.getTenantId(), ApiNames.TPM_ACTIVITY_OBJ, TPMActivityFields.DEPARTMENT_RANGE)) {
                pattern.append(" and (5 or 6)");
                query.getFilters().addAll(Lists.newArrayList(departmentRangeFilter, multiDepartmentRangeFilter));
            } else {
                pattern.append(" and 5 ");
                query.getFilters().add(multiDepartmentRangeFilter);
            }

        }
        query.setPattern(pattern.toString());

        return CommonUtils.queryData(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_OBJ, query);
    }

    private List<IObjectData> queryActivityByStore(ControllerContext context, long now, List<Integer> departmentIds) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnCountNum(false);
        query.setSearchSource("db");

        Filter beginDateFilter = new Filter();
        beginDateFilter.setFieldName(TPMActivityFields.BEGIN_DATE);
        beginDateFilter.setOperator(Operator.LT);
        beginDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

        Filter endDateFilter = new Filter();
        endDateFilter.setFieldName(TPMActivityFields.END_DATE);
        endDateFilter.setOperator(Operator.GT);
        endDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

        Filter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.EQ);
        lifeStatusFilter.setFieldValues(Lists.newArrayList("normal"));

        Filter dealerIdEmptyFilter = new Filter();
        dealerIdEmptyFilter.setFieldName(TPMActivityFields.DEALER_ID);
        dealerIdEmptyFilter.setOperator(Operator.IS);
        dealerIdEmptyFilter.setFieldValues(Lists.newArrayList());


        StringBuilder pattern = new StringBuilder(" 1 and 2 and 3 and 4");

        query.setFilters(Lists.newArrayList(beginDateFilter, endDateFilter, lifeStatusFilter, dealerIdEmptyFilter));

        if (!controllerContext.getUser().isOutUser() && !TPMGrayUtils.denyDepartmentFilterOnActivity(context.getTenantId())) {

            Filter departmentRangeFilter = new Filter();
            departmentRangeFilter.setFieldName(TPMActivityFields.DEPARTMENT_RANGE);
            departmentRangeFilter.setOperator(Operator.IN);
            departmentRangeFilter.setFieldValues(departmentIds.stream().map(Object::toString).collect(Collectors.toList()));

            Filter multiDepartmentRangeFilter = new Filter();
            multiDepartmentRangeFilter.setFieldName(TPMActivityFields.MULTI_DEPARTMENT_RANGE);
            multiDepartmentRangeFilter.setOperator(Operator.HASANYOF);
            multiDepartmentRangeFilter.setFieldValues(departmentIds.stream().map(Object::toString).collect(Collectors.toList()));

            if (I_DESCRIBE_CACHE_SERVICE.isExistField(context.getTenantId(), ApiNames.TPM_ACTIVITY_OBJ, TPMActivityFields.DEPARTMENT_RANGE)) {
                query.getFilters().add(departmentRangeFilter);
                query.getFilters().add(multiDepartmentRangeFilter);
                pattern.append(" and (5 or 6) ");
            } else {
                query.getFilters().add(multiDepartmentRangeFilter);
                pattern.append(" and 5 ");
            }
        }
        query.setPattern(pattern.toString());
        return CommonUtils.queryData(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_OBJ, query);
    }

    private List<IObjectData> queryActivityStore(ControllerContext context, String activityId, String storeId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter activityFilter = new Filter();
        activityFilter.setFieldName(TPMActivityStoreFields.ACTIVITY_ID);
        activityFilter.setOperator(Operator.EQ);
        activityFilter.setFieldValues(Lists.newArrayList(activityId));

        Filter storeIdFilter = new Filter();
        storeIdFilter.setFieldName(TPMActivityStoreFields.STORE_ID);
        storeIdFilter.setOperator(Operator.EQ);
        storeIdFilter.setFieldValues(Lists.newArrayList(storeId));

        query.setFilters(Lists.newArrayList(activityFilter, storeIdFilter));
        return serviceFacade.findBySearchQuery(User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_STORE_OBJ, query).getData();
    }

    private List<IObjectData> queryConditionStore(ControllerContext context, JSONObject storeRange, String storeId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(1);
        query.setOffset(0);
        query.setSearchSource("db");

        List<Wheres> conditionWheres = new ArrayList<>();
        String valueJson = storeRange.getString("value");
        JSONArray wheres = JSON.parseArray(valueJson);
        for (int whereIndex = 0; whereIndex < wheres.size(); whereIndex++) {
            JSONObject where = wheres.getJSONObject(0);
            Wheres conditionWhere = new Wheres();
            conditionWhere.setConnector(where.getString("connector"));
            conditionWhere.setFilters(Lists.newArrayList());
            JSONArray filters = where.getJSONArray("filters");
            for (int filterIndex = 0; filterIndex < filters.size(); filterIndex++) {
                JSONObject filter = filters.getJSONObject(filterIndex);
                Filter conditionFilter = new Filter();
                conditionFilter.setFieldName(filter.getString("field_name"));
                conditionFilter.setIndexName(filter.getString("index_name"));
                conditionFilter.setFieldValueType(filter.getString("field_value_type"));
                conditionFilter.setOperator(Operator.valueOf(filter.getString("operator")));
                conditionFilter.setFieldValues(filter.getJSONArray("field_values").toJavaList(String.class));
                conditionFilter.setConnector(filter.getString("connector"));
                conditionFilter.setValueType(filter.getInteger("value_type"));
                conditionFilter.setRefDescribeApiName(filter.getString("ref_describe_api_name"));
                conditionFilter.setRefFieldApiName(filter.getString("ref_field_api_name"));
                conditionFilter.setIsCascade(filter.getBoolean("is_cascade"));
                conditionFilter.setIsMasterField(filter.getBoolean("is_master_field"));
                conditionFilter.setFilterGroup(filter.getString("filterGroup"));
                conditionWhere.getFilters().add(conditionFilter);
            }

            Filter idFilter = new Filter();
            idFilter.setFieldName("_id");
            idFilter.setOperator(Operator.EQ);
            idFilter.setFieldValues(Lists.newArrayList(storeId));
            idFilter.setConnector("AND");

            conditionWhere.getFilters().add(idFilter);
            conditionWheres.add(conditionWhere);
        }
        query.setWheres(conditionWheres);
        return serviceFacade.findBySearchQuery(User.systemUser(context.getTenantId()), ApiNames.ACCOUNT_OBJ, query).getData();
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Collections.emptyList();
    }
}
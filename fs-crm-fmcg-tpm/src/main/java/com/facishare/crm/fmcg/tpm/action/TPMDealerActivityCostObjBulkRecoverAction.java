package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.tpm.apiname.*;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardBulkRecoverAction;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/3/12 下午5:50
 */
public class TPMDealerActivityCostObjBulkRecoverAction extends StandardBulkRecoverAction {

    @Override
    protected void before(Arg arg) {
        List<IObjectData> costs = serviceFacade.findObjectDataByIds(actionContext.getTenantId(), arg.getIdList(), arg.getObjectDescribeAPIName());
        Set<String> activityIds = costs.stream().map(cost -> cost.get(TPMDealerActivityCostFields.ACTIVITY_ID, String.class, "")).collect(Collectors.toSet());
        List<IObjectData> activities = serviceFacade.findObjectDataByIds(actionContext.getTenantId(), new ArrayList<>(activityIds), ApiNames.TPM_ACTIVITY_OBJ);
        List<String> laji = new ArrayList<>();
        for (IObjectData activity : activities) {
            String budgetId = activity.get(TPMActivityFields.BUDGET_TABLE) == null ? "" : (String) activity.get(TPMActivityFields.BUDGET_TABLE);
            if (!Strings.isNullOrEmpty(budgetId))
                laji.add(activity.getName());
        }
        if (!CollectionUtils.isEmpty(laji)) {
            throw new ValidateException(I18N.text(I18NKeys.THE_COST_RELATED_BUDGET_CAN_NOT_RECOVER) + laji.toString());
        }
        super.before(arg);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result rst = super.after(arg, result);
        deal1(arg, result);
        return rst;
    }


    private void deal1(Arg arg, Result result) {
        if (result.getSuccess()) {
            List<IObjectData> allCosts = serviceFacade.findObjectDataByIds(actionContext.getTenantId(), arg.getIdList(), ApiNames.TPM_DEALER_ACTIVITY_COST);
            for (IObjectData obj : allCosts) {
                Long startTime = (Long) obj.get(TPMDealerActivityCostFields.BEGIN_DATE);
                Long endTime = (Long) obj.get(TPMDealerActivityCostFields.END_DATE);

                String dealerId = (String) obj.get(TPMDealerActivityCostFields.DEALER_ID);
                String activityId = (String) obj.get(TPMDealerActivityCostFields.ACTIVITY_ID);
                String dealerActivityId = (String) obj.get(TPMDealerActivityCostFields.DEALER_ACTIVITY_ID);

                SearchTemplateQuery query = new SearchTemplateQuery();
                int offset = 0;
                query.setLimit(500);
                query.setOffset(offset);
                query.setSearchSource("db");

                Filter proofCreateTimeFilter = new Filter();
                proofCreateTimeFilter.setFieldName(TPMActivityProofFields.CREATE_TIME);
                proofCreateTimeFilter.setOperator(Operator.BETWEEN);
                proofCreateTimeFilter.setFieldValues(Lists.newArrayList(startTime.toString(), endTime.toString()));

                Filter proofCostFilter = new Filter();
                proofCostFilter.setFieldName(TPMActivityProofFields.DEALER_ACTIVITY_COST_ID);
                proofCostFilter.setOperator(Operator.IS);
                proofCostFilter.setFieldValues(Lists.newArrayList());

                Filter dealerActivityFilter = new Filter();
                dealerActivityFilter.setFieldName(TPMActivityProofFields.DEALER_ACTIVITY);
                dealerActivityFilter.setFieldValues(Lists.newArrayList(dealerActivityId));
                dealerActivityFilter.setOperator(Operator.EQ);


                Filter dealerIdFilter = new Filter();
                dealerIdFilter.setFieldName(TPMActivityProofFields.DEALER_ID);
                dealerIdFilter.setOperator(Operator.EQ);
                dealerIdFilter.setFieldValues(Lists.newArrayList(dealerId));

                Filter activityFilter = new Filter();
                activityFilter.setFieldName(TPMActivityProofFields.ACTIVITY_ID);
                activityFilter.setOperator(Operator.EQ);
                activityFilter.setFieldValues(Lists.newArrayList(activityId));


                Filter auditStatusFilter = new Filter();
                auditStatusFilter.setFieldName(TPMActivityProofFields.AUDIT_STATUS);
                auditStatusFilter.setFieldValues(Lists.newArrayList(TPMActivityProofFields.AUDIT_STATUS__PASS, TPMActivityProofFields.AUDIT_STATUS__REJECT));
                auditStatusFilter.setOperator(Operator.IN);

                query.setFilters(Lists.newArrayList(proofCostFilter, proofCreateTimeFilter, dealerIdFilter, activityFilter, auditStatusFilter, dealerActivityFilter));

                OrderBy createTimeOrder = new OrderBy();
                createTimeOrder.setFieldName(CommonFields.CREATE_TIME);
                createTimeOrder.setIsAsc(true);
                query.setOrders(Lists.newArrayList(createTimeOrder));
                List<IObjectData> proofs = Lists.newArrayList();
                List<IObjectData> tmpList;
                while (!(tmpList = serviceFacade.findBySearchQuery(User.systemUser(actionContext.getTenantId()), ApiNames.TPM_ACTIVITY_PROOF_OBJ, query).getData()).isEmpty()) {
                    proofs.addAll(tmpList);
                    offset += tmpList.size();
                    query.setOffset(offset);
                }
                if (!proofs.isEmpty()) {

                    Filter proofIdFilter = new Filter();
                    proofIdFilter.setFieldName(TPMActivityProofAuditFields.ACTIVITY_PROOF_ID);
                    proofIdFilter.setOperator(Operator.IN);
                    proofIdFilter.setFieldValues(proofs.stream().map(DBRecord::getId).collect(Collectors.toList()));
                    query.setFilters(Lists.newArrayList(proofIdFilter));
                    List<IObjectData> proofAuditList = serviceFacade.findBySearchQuery(User.systemUser(actionContext.getTenantId()), ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ, query).getData();

                    List<List<IObjectData>> listArr = Lists.partition(proofAuditList, 50);
                    List<String> updateField = Lists.newArrayList(TPMActivityProofAuditFields.DEALER_ACTIVITY_COST_ID);
                    for (List<IObjectData> list : listArr) {
                        for (IObjectData datum : list) {
                            datum.set(TPMActivityProofAuditFields.DEALER_ACTIVITY_COST_ID, obj.getId());
                        }
                        serviceFacade.batchUpdateByFields(User.systemUser(actionContext.getTenantId()), list, updateField);
                    }

                    updateField = Lists.newArrayList(TPMActivityProofFields.DEALER_ACTIVITY_COST_ID);
                    listArr = Lists.partition(proofs, 50);
                    for (List<IObjectData> list : listArr) {
                        for (IObjectData datum : list) {
                            datum.set(TPMActivityProofFields.DEALER_ACTIVITY_COST_ID, obj.getId());
                        }
                        serviceFacade.batchUpdateByFields(User.systemUser(actionContext.getTenantId()), list, updateField);
                    }
                }
            }
        }

    }
}

package com.facishare.crm.fmcg.tpm.api.activity;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

public interface EnableAuditList {

    @Data
    @ToString
    class Arg implements Serializable {

        @JSONField(name = "visit_status")
        @JsonProperty("visit_status")
        @SerializedName("visit_status")
        private String visitStatus;

        @SerializedName("visit_id")
        @JSONField(name = "visit_id")
        @JsonProperty("visit_id")
        private String visitId;

        @SerializedName("action_id")
        @JSONField(name = "action_id")
        @JsonProperty("action_id")
        private String actionId;

        @SerializedName("store_id")
        @JSONField(name = "store_id")
        @JsonProperty("store_id")
        private String storeId;
    }

    @Data
    @ToString
    class Result implements Serializable {

        /**
         * activity_list
         * proof_audit
         * proof
         * no_activity
         */
        @SerializedName("navigate_strategy")
        @JSONField(name = "navigate_strategy")
        @JsonProperty("navigate_strategy")
        private String navigateStrategy;

        private List<ActivityGroupVO> data = Lists.newArrayList();
    }

    @Data
    @ToString
    class ActivityGroupVO implements Serializable {

        private String groupKey;

        private String groupName;

        @SerializedName("activity_list")
        @JSONField(name = "activity_list")
        @JsonProperty("activity_list")
        private List<ActivityVO> activityList = Lists.newArrayList();
    }

    @Data
    @ToString
    class ActivityVO implements Serializable {

        private String id;

        private String name;

        @SerializedName("agreement_required")
        @JSONField(name = "agreement_required")
        @JsonProperty("agreement_required")
        private Boolean agreementRequired;

        @SerializedName("activity_agreement_id")
        @JSONField(name = "activity_agreement_id")
        @JsonProperty("activity_agreement_id")
        private String activityAgreementId;

        @SerializedName("agreement_begin_date")
        @JSONField(name = "agreement_begin_date")
        @JsonProperty("agreement_begin_date")
        private long agreementBeginDate;

        @SerializedName("agreement_end_date")
        @JSONField(name = "agreement_end_date")
        @JsonProperty("agreement_end_date")
        private long agreementEndDate;

        @SerializedName("begin_date")
        @JSONField(name = "begin_date")
        @JsonProperty("begin_date")
        private long beginDate;

        @SerializedName("end_date")
        @JSONField(name = "end_date")
        @JsonProperty("end_date")
        private long endDate;

        /**
         * schedule         - 待检核
         * reject           - 不合格
         * pass             - 合格
         */
        private String status;

        @SerializedName("data_api_name")
        @JSONField(name = "data_api_name")
        @JsonProperty("data_api_name")
        private String dataApiName;

        @SerializedName("data_id")
        @JSONField(name = "data_id")
        @JsonProperty("data_id")
        private String dataId;

        @SerializedName("proof_id")
        @JSONField(name = "proof_id")
        @JsonProperty("proof_id")
        private String proofId;
    }
}
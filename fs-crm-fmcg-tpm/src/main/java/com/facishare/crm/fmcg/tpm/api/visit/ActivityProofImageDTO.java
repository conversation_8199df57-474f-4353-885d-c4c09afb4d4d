package com.facishare.crm.fmcg.tpm.api.visit;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 12/9/20 2:45 PM
 */
@Data
@ToString
public class ActivityProofImageDTO implements Serializable {

    private String path;

    @JSONField(name = "file_name")
    private String fileName;
    /**
     * 协议迁移 使用到的文件名称字段
     */
    @JSONField(name = "filename")
    private String agreementsFileName;
    private String ext;

    private Long size;
}

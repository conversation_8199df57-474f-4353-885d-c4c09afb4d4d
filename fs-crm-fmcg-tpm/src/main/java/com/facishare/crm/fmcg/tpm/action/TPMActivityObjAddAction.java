package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.api.enumeration.LogType;
import com.facishare.crm.fmcg.tpm.api.log.LogData;
import com.facishare.crm.fmcg.tpm.api.method.IdempotentArgBase;
import com.facishare.crm.fmcg.tpm.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.apiname.CommonFields;
import com.facishare.crm.fmcg.tpm.apiname.TPMActivityBudgetFields;
import com.facishare.crm.fmcg.tpm.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.tpm.business.BudgetService;
import com.facishare.crm.fmcg.tpm.business.OperateInfoService;
import com.facishare.crm.fmcg.tpm.business.TPM2Service;
import com.facishare.crm.fmcg.tpm.business.abstraction.ITPM2Service;
import com.facishare.crm.fmcg.tpm.common.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.service.*;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.GlobalConstant;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.utils.TimeUtils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2020/11/6 2:01 PM
 */
@SuppressWarnings("Duplicates")
public class TPMActivityObjAddAction extends StandardAddAction implements TransactionService<BaseObjectSaveAction.Arg, BaseObjectSaveAction.Result> {

    private static final Logger LOGGER = LoggerFactory.getLogger(TPMActivityObjAddAction.class);

    private static final String SEND_BUDGET_IDS = "SEND_BUDGET_IDS:%s";

    private final BudgetService budgetService = SpringUtil.getContext().getBean(BudgetService.class);
    private final ITPM2Service tpm2Service = SpringUtil.getContext().getBean(TPM2Service.class);
    private final OperateInfoService operateInfoService = SpringUtil.getContext().getBean(OperateInfoService.class);
    private final PackTransactionProxy packTransactionProxy = SpringUtil.getContext().getBean(PackTransactionProxyImpl.class);

    @Override
    protected void customProcessArg(Arg arg) {
        super.customProcessArg(arg);

        //预算逻辑保证，防止其他来源导致无法触发审批流
        actionContext.setAttribute("triggerFlow", true);
    }

    @Override
    protected void before(Arg arg) {

        validateEndDate(arg);
        setDefaultValue(arg);
        validateActivityStatus(arg);

        super.before(arg);
        budgetService.buildCallbackKey(actionContext);
    }

    private void validateEndDate(Arg arg) {
        long begin = (long) arg.getObjectData().get(TPMActivityFields.BEGIN_DATE);
        long end = TimeUtils.convertToDayEndIfTimeWasDayBegin((long) arg.getObjectData().get(TPMActivityFields.END_DATE));
        arg.getObjectData().put(TPMActivityFields.END_DATE, end);

        if (end <= begin) {
            throw new ValidateException(I18N.text(I18NKeys.ADD_ACTIVITY_DATE_ERROR));
        }

        LOGGER.info("end date : {}", end);
    }

    private void setDefaultValue(Arg arg) {
        if (Strings.isNullOrEmpty((String) arg.getObjectData().get(TPMActivityFields.STORE_RANGE))) {
            arg.getObjectData().put(TPMActivityFields.STORE_RANGE, "{\"type\":\"ALL\",\"value\":\"ALL\"}");
        }
    }

    private void validateActivityStatus(Arg arg) {
        long begin = (long) arg.getObjectData().get(TPMActivityFields.BEGIN_DATE);
        long end = (long) arg.getObjectData().get(TPMActivityFields.END_DATE);

        long now = System.currentTimeMillis();
        String status;
        if (now < begin) {
            status = TPMActivityFields.ACTIVITY_STATUS__SCHEDULE;
        } else if (now < end) {
            status = TPMActivityFields.ACTIVITY_STATUS__IN_PROGRESS;
        } else {
            status = TPMActivityFields.ACTIVITY_STATUS__END;
        }
        arg.getObjectData().put(TPMActivityFields.ACTIVITY_STATUS, status);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result localResult = super.after(arg, result);

        List<String> updateBudgetIds = actionContext.getAttribute(String.format(SEND_BUDGET_IDS, actionContext.getPostId()));
        String key = String.format(SEND_BUDGET_IDS, actionContext.getPostId());

        LOGGER.info("key : {}, update budget id list : {}, action context : {}.", key, updateBudgetIds, actionContext);

        if (!CollectionUtils.isEmpty(updateBudgetIds)) {
            updateBudgetIds.forEach(id -> budgetService.calculateBudget(actionContext.getTenantId(), id));
        }

        String type = (String) arg.getObjectData().get("record_type");
        if ("default__c".equals(type)) {
            BuryService.asyncTpmLog(Integer.valueOf(actionContext.getTenantId()), actionContext.getUser().getUserIdInt(), BuryModule.TPM.TPM_ACTIVITY, BuryOperation.CREATE_CUSTOM);
        }

        if ("dealer_activity__c".equals(type)) {
            BuryService.asyncTpmLog(Integer.valueOf(actionContext.getTenantId()), actionContext.getUser().getUserIdInt(), BuryModule.TPM.TPM_ACTIVITY, BuryOperation.CREATE_PERSONAL);
        }

        BuryService.asyncTpmLog(Integer.valueOf(actionContext.getTenantId()), actionContext.getUser().getUserIdInt(), BuryModule.TPM.TPM_ACTIVITY, BuryOperation.CREATE);
        resetActivityStatus(result);
        return localResult;
    }

    @Override
    protected void finallyDo() {
        try {
            super.finallyDo();
        } finally {
            budgetService.unLockBudget(actionContext);
        }
    }

    private void resetActivityStatus(Result result) {
        if (tpm2Service.isTPM2Tenant(Integer.valueOf(actionContext.getTenantId()))) {
            String lifeStatus = (String) result.getObjectData().get(CommonFields.LIFE_STATUS);
            if (!ObjectLifeStatus.NORMAL.getCode().equals(lifeStatus)) {
                Map<String, Object> updateMap = new HashMap<>();
                updateMap.put(TPMActivityFields.ACTIVITY_STATUS, TPMActivityFields.ACTIVITY_STATUS__APPROVAL);
                serviceFacade.updateWithMap(User.systemUser(actionContext.getTenantId()), result.getObjectData().toObjectData(), updateMap);
            }
        }
    }

    @Override
    protected Result doAct(Arg arg) {
        try {
            tryLock();
            return packTransactionProxy.packAct(this, arg);
        } catch (Exception e) {
            budgetService.rmSaveIdempotent(actionContext);
            throw e;
        }
    }

    private void tryLock() {
        if (budgetService.isOpenBudge(Integer.parseInt(actionContext.getTenantId()))) {
            String nowBudgetId = (String) arg.getObjectData().get(TPMActivityFields.BUDGET_TABLE);
            if (!Strings.isNullOrEmpty(nowBudgetId)) {
                budgetService.tryLockBudget(actionContext, nowBudgetId);
            }
        }
    }

    @Override
    protected Map<String, Map<String, Object>> buildCallbackDataForAddAction(IObjectData objectData, Long detailCreateTime) {
        Map<String, Map<String, Object>> callbackData = super.buildCallbackDataForAddAction(objectData, detailCreateTime);
        String relationValue = actionContext.getAttribute(GlobalConstant.BUDGET_APPROVAL_CALLBACK_RELATION_KEY);
        if (!Strings.isNullOrEmpty(relationValue)) {
            Map<String, Object> instanceMap = callbackData.getOrDefault(objectData.getId(), new HashMap<>());
            callbackData.put(objectData.getId(), instanceMap);
            Map<String, Object> callbackDatum = new HashMap<>();
            callbackDatum.put(GlobalConstant.BUDGET_APPROVAL_CALLBACK_RELATION_KEY, relationValue);
            instanceMap.put(GlobalConstant.BUDGET_APPROVAL_CALLBACK_MAP_KEY, callbackDatum);
        }
        return callbackData;
    }

    @Override
    public Result doActTransaction(Arg arg) {
        Result result = super.doAct(arg);
        validateBudget(arg, result);
        updateApprovalIdToDetail();
        return result;
    }

    private void validateBudget(Arg arg, Result result) {
        int tenantId = Integer.parseInt(actionContext.getTenantId());

        LOGGER.info("tpm budget open : {}", arg);

        if (budgetService.isOpenBudge(tenantId)) {
            String nowBudgetId = (String) arg.getObjectData().get(TPMActivityFields.BUDGET_TABLE);

            //关联预算表校验
            if (!Strings.isNullOrEmpty(nowBudgetId)) {

                IObjectData budget = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), nowBudgetId, ApiNames.TPM_ACTIVITY_BUDGET);
                LOGGER.info("budget:{}", budget);
                if (!ObjectLifeStatus.NORMAL.getCode().equals(budget.get(CommonFields.LIFE_STATUS))) {
                    throw new ValidateException("该预算表未生效或处于审批中，请更换预算表。");
                }

                //判断时间
                int startMonth = budgetService.getBudgetStartMonth(tenantId);
                Long activityBeginTime = (Long) arg.getObjectData().get(TPMActivityFields.BEGIN_DATE);
                int budgetYear = Integer.parseInt((String) budget.get(TPMActivityBudgetFields.PERIOD_YEAR));
                LocalDate budgetStartDate = LocalDate.of(budgetYear, startMonth, 1);
                LocalDate budgetEndDate = budgetStartDate.plusYears(1);
                LocalDate activityBeginDate = Instant.ofEpochMilli(activityBeginTime).atZone(ZoneOffset.ofHours(8)).toLocalDate();
                if (budgetStartDate.isAfter(activityBeginDate) || budgetEndDate.isBefore(activityBeginDate) || budgetEndDate.equals(activityBeginDate)) {
                    throw new ValidateException(I18N.text(I18NKeys.ADD_ACTIVITY_TIME_IS_NOT_FIT_BUDGET));
                }

                //department
                String budgetDepartment = ((List<String>) budget.get(TPMActivityBudgetFields.BUDGET_DEPARTMENT)).get(0);
                List<String> subDeptIds = serviceFacade.getSubDeptByDeptId(actionContext.getTenantId(), User.SUPPER_ADMIN_USER_ID, budgetDepartment, true);
                String activityDepartment = ((List<String>) arg.getObjectData().toObjectData().get(TPMActivityFields.DEPARTMENT_RANGE)).get(0);

                if (!(activityDepartment.equals(budgetDepartment) || subDeptIds.contains(activityDepartment))) {
                    throw new ValidateException(I18N.text(I18NKeys.ADD_ACTIVITY_BUDGET_DEPARTMENT_NOT_FIT));
                }

                double activityAmountInObj = Double.parseDouble(arg.getObjectData().getOrDefault(TPMActivityFields.ACTIVITY_AMOUNT, "0").toString());
                if (activityAmountInObj < 0) {
                    throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_AMOUNT_CAN_NOT_BE_ZERO));
                }

                //预算
                Map<String, Double> amountMap = new HashMap<>();
                double availableAmount = budgetService.getBudgetAvailableAmount(actionContext.getTenantId(), budget, amountMap);
                double activityAmount = Double.parseDouble((String) arg.getObjectData().getOrDefault(TPMActivityFields.ACTIVITY_AMOUNT, "0"));
                availableAmount = CommonUtils.keepNDecimal(availableAmount, 3);
                activityAmount = CommonUtils.keepNDecimal(activityAmount, 3);
                if (!TPMGrayUtils.disableBudgetAmountJudge(actionContext.getTenantId()) && CommonUtils.keepNDecimal(availableAmount - activityAmount, 3) < 0) {
                    throw new ValidateException(I18N.text(I18NKeys.ADD_ACTIVITY_BUDGET_INSUFFICIENT));
                }
                boolean needTriggerApproval = budgetService.needApproval(actionContext.getTenantId(), ApiNames.TPM_ACTIVITY_OBJ, "Create");
                actionContext.setAttribute("needTriggerApproval", needTriggerApproval);

                LogData logData = LogData.builder().data(JSON.toJSONString(arg)).build();
                logData.setAttribute("budget", budget);
                logData.setAttribute("amountMap", amountMap);
                String logId = operateInfoService.log(actionContext.getTenantId(), LogType.ADD.value(), JSON.toJSONString(logData), actionContext.getUser().getUserId(), ApiNames.TPM_ACTIVITY_OBJ, result.getObjectData().getId(), needTriggerApproval);

                double beforeAmount = needTriggerApprovalFlow() ? availableAmount : availableAmount + activityAmount;
                double afterAmount = needTriggerApprovalFlow() ? availableAmount - activityAmount : activityAmount;

                IObjectData detail = budgetService.addBudgetDetail(actionContext.getTenantId(), actionContext.getUser().getUserId(),
                        "1",
                        budget.getId(),
                        String.format("活动新建：「%s」申请", arg.getObjectData().get("name")),
                        -activityAmount,
                        beforeAmount,
                        afterAmount,
                        System.currentTimeMillis(),
                        String.format("LogId:%s-traceId:%s", logId, TraceContext.get().getTraceId()),
                        result.getObjectData().getId(),
                        TraceContext.get().getTraceId(),
                        IdempotentArgBase.builder().idempotentKey(actionContext.getPostId() + ":" + result.getObjectData().getId()).build());

                //calculate budget
                actionContext.setAttribute(String.format(SEND_BUDGET_IDS, actionContext.getPostId()), Lists.newArrayList(budget.getId()));
                actionContext.setAttribute("operate_log_id", logId);
                actionContext.setAttribute("detail_id", detail.getId());
            }
        }
    }

    private void updateApprovalIdToDetail() {
        boolean needTriggerApproval = Boolean.TRUE.equals(actionContext.getAttribute("needTriggerApproval"));
        if (needTriggerApproval && actionContext.getAttribute("operate_log_id") != null) {
            String approvalId = actionContext.getAttribute(GlobalConstant.BUDGET_APPROVAL_CALLBACK_RELATION_KEY);
            operateInfoService.updateLog(actionContext.getAttribute("operate_log_id"), actionContext.getTenantId(), approvalId);
            if (actionContext.getAttribute("detail_id") != null) {
                budgetService.updateApprovalIdForDetail(actionContext.getTenantId(), actionContext.getAttribute("detail_id"), approvalId);
            }
        }
    }
}

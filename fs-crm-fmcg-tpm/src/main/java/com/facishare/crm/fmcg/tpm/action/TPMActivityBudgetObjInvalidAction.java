package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.tpm.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.apiname.TPMActivityBudgetAdjustFields;
import com.facishare.crm.fmcg.tpm.apiname.TPMActivityBudgetDetailFields;
import com.facishare.crm.fmcg.tpm.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import org.apache.logging.log4j.util.Strings;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/3/22 下午2:55
 */
public class TPMActivityBudgetObjInvalidAction extends StandardInvalidAction {

    @Override
    protected void before(Arg arg) {
        validateRelated(arg);
        super.before(arg);
    }

    private void validateRelated(Arg arg) {
        SearchTemplateQuery queryActivity = new SearchTemplateQuery();
        queryActivity.setOffset(0);
        queryActivity.setLimit(2000);
        queryActivity.setSearchSource("db");

        IFilter budgetFilter = new Filter();
        budgetFilter.setFieldName(TPMActivityFields.BUDGET_TABLE);
        budgetFilter.setOperator(Operator.IN);
        budgetFilter.setFieldValues(Lists.newArrayList(arg.getObjectDataId()));
        queryActivity.setFilters(Lists.newArrayList(budgetFilter));

        List<IObjectData> activities = serviceFacade.findBySearchQuery(User.systemUser(actionContext.getTenantId()), ApiNames.TPM_ACTIVITY_OBJ, queryActivity).getData();
        List<String> names = Lists.newArrayList();
        List<String> ids = activities.stream().map(v -> (String) v.get(TPMActivityFields.BUDGET_TABLE)).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(ids)) {
            List<IObjectData> budgets = serviceFacade.findObjectDataByIds(actionContext.getTenantId(), ids, ApiNames.TPM_ACTIVITY_BUDGET);
            if (!CollectionUtils.isEmpty(budgets)) {
                budgets.forEach(budget -> names.add((String) budget.get("name")));
            }
            if (!CollectionUtils.isEmpty(names)) {
                throw new ValidateException(I18N.text(I18NKeys.THOSE_BUDGET_RELATED_BY_ACTIVITY_CAN_NOT_BE_INVALID) + names);
            }
        }

        //预算调整校验
        SearchTemplateQuery queryAdjust = new SearchTemplateQuery();
        queryAdjust.setOffset(0);
        queryAdjust.setLimit(2000);
        queryAdjust.setSearchSource("db");

        IFilter fromBudgetFilter = new Filter();
        fromBudgetFilter.setFieldName(TPMActivityBudgetAdjustFields.FROM_BUDGET_TABLE_ID);
        fromBudgetFilter.setOperator(Operator.IN);
        fromBudgetFilter.setFieldValues(Lists.newArrayList(arg.getObjectDataId()));

        IFilter toBudgetFilter = new Filter();
        toBudgetFilter.setFieldName(TPMActivityBudgetAdjustFields.TO_BUDGET_TABLE_ID);
        toBudgetFilter.setOperator(Operator.IN);
        toBudgetFilter.setFieldValues(Lists.newArrayList(arg.getObjectDataId()));

        Wheres wheres1 = new Wheres();
        wheres1.setFilters(Lists.newArrayList(fromBudgetFilter));

        Wheres wheres2 = new Wheres();
        wheres2.setFilters(Lists.newArrayList(toBudgetFilter));

        queryAdjust.setFilters(Lists.newArrayList());
        queryAdjust.setWheres(Lists.newArrayList(wheres1, wheres2));

        List<IObjectData> adjusts = serviceFacade.findBySearchQuery(User.systemUser(actionContext.getTenantId()), ApiNames.TPM_ACTIVITY_BUDGET_ADJUST_OBJ, queryAdjust).getData();
        names.clear();
        Set<String> idSet = adjusts.stream().map(v -> (String) v.get(TPMActivityBudgetAdjustFields.TO_BUDGET_TABLE_ID)).collect(Collectors.toSet());
        idSet.addAll(adjusts.stream().map(v -> (String) v.get(TPMActivityBudgetAdjustFields.FROM_BUDGET_TABLE_ID)).collect(Collectors.toSet()));
        if (!CollectionUtils.isEmpty(idSet)) {
            List<IObjectData> budgets = serviceFacade.findObjectDataByIds(actionContext.getTenantId(), new ArrayList<>(idSet), ApiNames.TPM_ACTIVITY_BUDGET);
            if (!CollectionUtils.isEmpty(budgets)) {
                budgets.forEach(budget -> names.add((String) budget.get("name")));
            }
            if (!CollectionUtils.isEmpty(names)) {
                throw new ValidateException(I18N.text(I18NKeys.THOSE_BUDGET_RELATED_BY_ADJUST_CAN_NOT_BE_INVALID) + names);
            }
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result finalRst =  super.after(arg, result);
        invalidDetail((String) finalRst.getObjectData().get(TPMActivityBudgetDetailFields.BUDGET_TABLE_ID));
        return finalRst;
    }

    private void invalidDetail(String budgetId) {
        if(Strings.isEmpty(budgetId)){
            return;
        }
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter idFilter = new Filter();
        idFilter.setFieldName(TPMActivityBudgetDetailFields.BUDGET_TABLE_ID);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(budgetId));
        query.setFilters(Lists.newArrayList(idFilter));

        List<IObjectData> dataList = CommonUtils.queryData(serviceFacade, User.systemUser(actionContext.getTenantId()), ApiNames.TPM_ACTIVITY_BUDGET_DETAIL_OBJ, query);

        for (List<IObjectData> tmpList : Lists.partition(dataList, 100)) {
            serviceFacade.bulkInvalid(tmpList, User.systemUser(actionContext.getTenantId()));
        }
    }
}

package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.tpm.service.*;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.metadata.util.SpringUtil;

import javax.annotation.Resource;


/**
 * <AUTHOR>
 * @date 2021/1/25 下午5:09
 */
public class TPMActivityItemObjAddAction extends StandardAddAction  {


    @Override
    protected Result after(Arg arg, Result result) {
        BuryService.asyncTpmLog(Integer.valueOf(actionContext.getTenantId()), actionContext.getUser().getUserIdInt(), BuryModule.TPM.TPM_ACTIVITY_ITEM, BuryOperation.CREATE);
        return super.after(arg, result);
    }

}

package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.fxiaoke.common.release.GrayRelease;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/5/15 11:49 PM
 */
public class TPMActivityItemCostStandardObjAddAction extends StandardAddAction {

    @Override
    protected void before(Arg arg) {
        if (!GrayRelease.isAllow("fmcg", "FMCG.TPM.COST_STANDARD", actionContext.getTenantId())) {
            throw new ValidateException(I18N.text(I18NKeys.CURRENT_TENANT_DO_NOT_SUPPORT_COST_STANDARD));
        }
        super.before(arg);
    }
}

package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.api.enumeration.LogType;
import com.facishare.crm.fmcg.tpm.api.log.LogData;
import com.facishare.crm.fmcg.tpm.api.method.IdempotentArgBase;
import com.facishare.crm.fmcg.tpm.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.apiname.CommonFields;
import com.facishare.crm.fmcg.tpm.apiname.TPMActivityBudgetAdjustFields;
import com.facishare.crm.fmcg.tpm.business.BudgetService;
import com.facishare.crm.fmcg.tpm.business.OperateInfoService;
import com.facishare.crm.fmcg.tpm.common.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.service.*;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.GlobalConstant;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.common.release.GrayRelease;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/7/21 下午3:15
 */
public class TPMActivityBudgetAdjustObjAddAction extends StandardAddAction implements TransactionService<StandardAddAction.Arg, StandardAddAction.Result> {

    private static final Logger log = LoggerFactory.getLogger(TPMActivityBudgetAdjustObjAddAction.class);

    private static final BudgetService budgetService = SpringUtil.getContext().getBean(BudgetService.class);

    private final OperateInfoService operateInfoService = SpringUtil.getContext().getBean(OperateInfoService.class);

    private final PackTransactionProxy packTransactionProxy = SpringUtil.getContext().getBean(PackTransactionProxyImpl.class);

    private static final String SEND_BUDGET_IDS = "SEND_BUDGET_IDS:%s";

    @Override
    protected void before(Arg arg) {
        Object amountObj = arg.getObjectData().get(TPMActivityBudgetAdjustFields.AMOUNT);
        if (Objects.isNull(amountObj)) {
            throw new ValidateException(I18N.text(I18NKeys.ADJUST_AMOUNT_MUST_MORE_THAN_ZERO));
        }
        double amount = Double.parseDouble(String.valueOf(amountObj));
        boolean transferInSupportNegative = TPMGrayUtils.budgetTransferInSupportNegative(actionContext.getTenantId());
        String recordType = (String) arg.getObjectData().get(CommonFields.RECORD_TYPE);
        if (!transferInSupportNegative && amount <= 0 || transferInSupportNegative && amount <= 0 && !"transform_in__c".equals(recordType)) {
            throw new ValidateException(I18N.text(I18NKeys.ADJUST_AMOUNT_MUST_MORE_THAN_ZERO));
        }
        validateArg(arg);

        /*if (arg.getObjectData().get(TPMActivityBudgetAdjustFields.TO_BUDGET_TABLE_ID) == null || arg.getObjectData().get(TPMActivityBudgetAdjustFields.FROM_BUDGET_TABLE_ID) == null) {
            actionContext.setAttribute("triggerFlow", false);
        }*/
        super.before(arg);
        budgetService.buildCallbackKey(actionContext);
    }

    void validateArg(Arg arg) {
        if (!TPMGrayUtils.isSupportOverrideAdjustTime(actionContext.getTenantId()))
            arg.getObjectData().put(TPMActivityBudgetAdjustFields.ADJUST_TIME, System.currentTimeMillis());
        Object fromId = arg.getObjectData().get(TPMActivityBudgetAdjustFields.FROM_BUDGET_TABLE_ID) != null ? arg.getObjectData().get(TPMActivityBudgetAdjustFields.FROM_BUDGET_TABLE_ID) : "";
        if (fromId.equals(arg.getObjectData().getOrDefault(TPMActivityBudgetAdjustFields.TO_BUDGET_TABLE_ID, ""))) {
            throw new ValidateException(I18N.text(I18NKeys.TRANSFER_IN_BUDGET_AND_TRANSFER_OUT_BUDGET_CAN_NOT_BE_THE_SAME));
        }
    }

    @Override
    protected Result doAct(Arg arg) {
        try {
            tryLock();
            return packTransactionProxy.packAct(this, arg);
        } catch (Exception e) {
            budgetService.rmSaveIdempotent(actionContext);
            throw e;
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result result1 = super.after(arg, result);
        deleteDetailAndLog(result.getObjectData().getId());

        BuryService.asyncTpmLog(Integer.valueOf(actionContext.getTenantId()), actionContext.getUser().getUserIdInt(), BuryModule.TPM.TPM_ACTIVITY_BUDGET_ADJUST, BuryOperation.CREATE);

        List<String> updateBudgetIds = actionContext.getAttribute(String.format(SEND_BUDGET_IDS, actionContext.getPostId()));
        log.info("key:{},updateBudgetIds:{},actionContext:{}", String.format(SEND_BUDGET_IDS, actionContext.getPostId()), updateBudgetIds, actionContext);

        if (!CollectionUtils.isEmpty(updateBudgetIds))
            updateBudgetIds.forEach(id -> budgetService.calculateBudget(actionContext.getTenantId(), id));
        return result1;
    }

    private void updateApprovalIdToDetail(Result result) {
        log.info("need trigger master approval : {}, need trigger approval : {}, is start success : {}, is start success asynchronous : {}, biz info : {}",
                this.needTriggerMasterApproval(),
                this.needTriggerApprovalFlow(),
                this.isApprovalFlowStartSuccess(result.getObjectData().getId()),
                this.isApprovalFlowStartSuccessOrAsynchronous(result.getObjectData().getId()),
                JSON.toJSONString(RequestContextManager.getContext().getBizInfo())
        );
        boolean needTriggerApproval = Boolean.TRUE.equals(actionContext.getAttribute("needTriggerApproval"));
        if (needTriggerApproval && actionContext.getAttribute("operate_log_id") != null && this.needTriggerApprovalFlow()) {
            String approvalId = actionContext.getAttribute(GlobalConstant.BUDGET_APPROVAL_CALLBACK_RELATION_KEY);
            operateInfoService.updateLog(actionContext.getAttribute("operate_log_id"), actionContext.getTenantId(), approvalId);
            if (actionContext.getAttribute("detail_id") != null)
                budgetService.updateApprovalIdForDetail(actionContext.getTenantId(), actionContext.getAttribute("detail_id"), approvalId);
        }
    }


    @Override
    protected Map<String, Map<String, Object>> buildCallbackDataForAddAction(IObjectData objectData, Long detailCreateTime) {
        Map<String, Map<String, Object>> callbackData = super.buildCallbackDataForAddAction(objectData, detailCreateTime);
        String relationValue = actionContext.getAttribute(GlobalConstant.BUDGET_APPROVAL_CALLBACK_RELATION_KEY);
        if (!Strings.isNullOrEmpty(relationValue)) {
            Map<String, Object> instanceMap = callbackData.getOrDefault(objectData.getId(), new HashMap<>());
            callbackData.put(objectData.getId(), instanceMap);
            Map<String, Object> callbackDatum = new HashMap<>();
            callbackDatum.put(GlobalConstant.BUDGET_APPROVAL_CALLBACK_RELATION_KEY, relationValue);
            instanceMap.put(GlobalConstant.BUDGET_APPROVAL_CALLBACK_MAP_KEY, callbackDatum);
        }
        return callbackData;
    }

    @Override
    public Result doActTransaction(Arg arg) {
        int mode = getAdjustMode(arg);
        Result result = super.doAct(arg);
        String fromBudgetId = (String) arg.getObjectData().get(TPMActivityBudgetAdjustFields.FROM_BUDGET_TABLE_ID);
        String toBudgetId = (String) arg.getObjectData().get(TPMActivityBudgetAdjustFields.TO_BUDGET_TABLE_ID);


        boolean needTriggerApproval = budgetService.needApproval(actionContext.getTenantId(), ApiNames.TPM_ACTIVITY_BUDGET_ADJUST_OBJ, "Create");
        actionContext.setAttribute("needTriggerApproval", needTriggerApproval);
        LogData logData = null;
        String logId = null;
        List<String> budgetIds = Lists.newArrayList();
        Map<String, Double> amountMap = new HashMap<>();
        double amount = Double.parseDouble(String.valueOf(arg.getObjectData().get(TPMActivityBudgetAdjustFields.AMOUNT)));
        long adjustTime = arg.getObjectData().get(TPMActivityBudgetAdjustFields.ADJUST_TIME) == null ? System.currentTimeMillis() : Long.parseLong(arg.getObjectData().get(TPMActivityBudgetAdjustFields.ADJUST_TIME).toString());
        amount = CommonUtils.keepNDecimal(amount, 3);
        if (mode == 1 || mode == 2) {
            budgetIds.add(fromBudgetId);
            //budgetService.tryLockBudget(actionContext, "fromBudget", fromBudgetId);
            IObjectData fromBudget = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), fromBudgetId, ApiNames.TPM_ACTIVITY_BUDGET);
            String fromLifeStatus = (String) fromBudget.get(CommonFields.LIFE_STATUS);
            if (!ObjectLifeStatus.NORMAL.getCode().equals(fromLifeStatus)) {
                throw new ValidateException("转出预算表未生效或处于审批中。");
            }

            double availableAmount = budgetService.getBudgetAvailableAmount(actionContext.getTenantId(), fromBudget, amountMap);
            double cha = CommonUtils.keepNDecimal(availableAmount - amount, 3);

            if (GrayRelease.isAllow("fmcg", "IGNORE_BUDGET_ADJUST_AMOUNT_VALIDATE_WHEN_TRANSFER_OUT", actionContext.getTenantId())) {
                if (mode == 1 && cha < 0) {
                    log.info("availableAmount:{},amount:{}", availableAmount, amount);
                    throw new ValidateException(I18N.text(I18NKeys.SOURCE_BUDGET_DON_NOT_HAVE_ENOUGH_MONEY));
                }
            } else {
                if (!GrayRelease.isAllow("fmcg", "IGNORE_BUDGET_ADJUST_AMOUNT_VALIDATE", actionContext.getTenantId()) && cha < 0) {
                    log.info("availableAmount:{},amount:{}", availableAmount, amount);
                    throw new ValidateException(I18N.text(I18NKeys.SOURCE_BUDGET_DON_NOT_HAVE_ENOUGH_MONEY));
                }
            }


            logData = LogData.builder().data(JSON.toJSONString(arg)).build();
            logData.setAttribute("fromBudget", fromBudget);
            logData.setAttribute("toAmountMap", amountMap);
            logId = operateInfoService.log(actionContext.getTenantId(), LogType.ADD.value(), JSON.toJSONString(logData), actionContext.getUser().getUserId(), ApiNames.TPM_ACTIVITY_BUDGET_ADJUST_OBJ, result.getObjectData().getId(), needTriggerApproval);

            double beforeAmount = this.needTriggerApprovalFlow() ? availableAmount : availableAmount + amount;
            double afterAmount = this.needTriggerApprovalFlow() ? availableAmount - amount : availableAmount;
            IObjectData detail = budgetService.addBudgetDetail(actionContext.getTenantId(), actionContext.getUser().getUserId(),
                    "1",
                    fromBudget.getId(),
                    String.format("预算调整：「%s」调整", fromBudget.get("name")),
                    -amount,
                    beforeAmount,
                    afterAmount,
                    adjustTime,
                    String.format("LogId:%s-traceId:%s", logId, TraceContext.get().getTraceId()),
                    null,
                    TraceContext.get().getTraceId(),
                    result.getObjectData().getId(),
                    IdempotentArgBase.builder().idempotentKey(actionContext.getPostId() + ":" + fromBudget.getId()).build()
            );
            actionContext.setAttribute("detail_id", detail.getId());
            budgetService.updateBudgetAdjustAmount(actionContext.getTenantId(), result.getObjectData().toObjectData(), null, null, beforeAmount, afterAmount);
        }


        if (mode != 2) {
            //budgetService.tryLockBudget(actionContext, "toBudget", toBudgetId);

            IObjectData toBudget = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), toBudgetId, ApiNames.TPM_ACTIVITY_BUDGET);
            String toLifeStatus = (String) toBudget.get(CommonFields.LIFE_STATUS);

            if (!ObjectLifeStatus.NORMAL.getCode().equals(toLifeStatus)) {
                throw new ValidateException("转入预算表未生效或处于审批中。");
            }

            double availableAmount = budgetService.getBudgetAvailableAmount(actionContext.getTenantId(), toBudget, amountMap);

            logData = logData == null ? LogData.builder().data(JSON.toJSONString(arg)).build() : logData;
            logData.setAttribute("toBudget", toBudget);
            logData.setAttribute("toAmountMap", amountMap);
            Map<String, Object> updateField = new HashMap<>();
            updateField.put("log_data", JSON.toJSONString(logData));
            if (mode == 1 && logId != null) {
                operateInfoService.updateLog(logId, actionContext.getTenantId(), updateField);
            } else {
                logId = operateInfoService.log(actionContext.getTenantId(), LogType.ADD.value(), JSON.toJSONString(logData), actionContext.getUser().getUserId(), ApiNames.TPM_ACTIVITY_BUDGET_ADJUST_OBJ, result.getObjectData().getId(), needTriggerApproval);
            }
            double beforeAmount = availableAmount;
            double afterAmount = availableAmount + amount;
            IObjectData toDetail = budgetService.addBudgetDetail(actionContext.getTenantId(), actionContext.getUser().getUserId(),
                    "2",
                    toBudget.getId(),
                    String.format("预算调整：「%s」调整", toBudget.get("name")),
                    amount,
                    beforeAmount,
                    afterAmount,
                    adjustTime,
                    String.format("LogId:%s-traceId:%s", logId, TraceContext.get().getTraceId()),
                    null,
                    TraceContext.get().getTraceId(),
                    result.getObjectData().getId(),
                    IdempotentArgBase.builder().idempotentKey(actionContext.getPostId() + ":" + toBudget.getId()).build());
            budgetIds.add(toBudgetId);
            actionContext.setAttribute("to_detail_obj", toDetail);
            budgetService.updateBudgetAdjustAmount(actionContext.getTenantId(), result.getObjectData().toObjectData(), beforeAmount, afterAmount, null, null);
        }
        actionContext.setAttribute(String.format(SEND_BUDGET_IDS, actionContext.getPostId()), budgetIds);
        actionContext.setAttribute("operate_log_id", logId);


        updateApprovalIdToDetail(result);
        return result;
    }

    private int getAdjustMode(Arg arg) {
        ObjectDataDocument argDoc = arg.getObjectData();
        if (!Strings.isNullOrEmpty((String) argDoc.get(TPMActivityBudgetAdjustFields.FROM_BUDGET_TABLE_ID)) && !Strings.isNullOrEmpty((String) argDoc.get(TPMActivityBudgetAdjustFields.TO_BUDGET_TABLE_ID))) {
            return 1;
        } else if (!Strings.isNullOrEmpty((String) argDoc.get(TPMActivityBudgetAdjustFields.FROM_BUDGET_TABLE_ID)) && Strings.isNullOrEmpty((String) argDoc.get(TPMActivityBudgetAdjustFields.TO_BUDGET_TABLE_ID))) {
            arg.getObjectData().put(TPMActivityBudgetAdjustFields.BALANCE_BEFORE_TRANSFER_IN, null);
            arg.getObjectData().put(TPMActivityBudgetAdjustFields.BALANCE_AFTER_TRANSFER_IN, null);
            return 2;
        } else if (Strings.isNullOrEmpty((String) argDoc.get(TPMActivityBudgetAdjustFields.FROM_BUDGET_TABLE_ID)) && !Strings.isNullOrEmpty((String) argDoc.get(TPMActivityBudgetAdjustFields.TO_BUDGET_TABLE_ID))) {
            arg.getObjectData().put(TPMActivityBudgetAdjustFields.BALANCE_AFTER_TRANSFER_OUT, null);
            arg.getObjectData().put(TPMActivityBudgetAdjustFields.BALANCE_BEFORE_TRANSFER_OUT, null);
            return 3;
        } else {
            throw new ValidateException(I18N.text(I18NKeys.BOTH_BUDGET_IN_ADJUST_CAN_NOT_BE_EMPTY));
        }
    }

    private void deleteDetailAndLog(String adjustId) {
        if (this.needTriggerApprovalFlow() && (!this.isApprovalNotExist() || this.isApprovalFlowStartSuccess(adjustId))) {
            String logId = actionContext.getAttribute("operate_log_id");
            if (Strings.isNullOrEmpty(logId)) {
                operateInfoService.deleteLog(actionContext.getTenantId(), logId);
            }
            IObjectData toDetail = actionContext.getAttribute("to_detail_obj");
            if (toDetail != null) {
                toDetail = serviceFacade.invalid(toDetail, User.systemUser(actionContext.getTenantId()));
                serviceFacade.bulkDelete(Lists.newArrayList(toDetail), User.systemUser(actionContext.getTenantId()));
            }
        }
    }


    @Override
    protected void finallyDo() {
        try {
            super.finallyDo();
        } finally {
            budgetService.unLockBudget(actionContext, "fromBudget");
            budgetService.unLockBudget(actionContext, "toBudget");
        }
    }

    public void tryLock() {

        String fromBudgetId = (String) arg.getObjectData().get(TPMActivityBudgetAdjustFields.FROM_BUDGET_TABLE_ID);
        String toBudgetId = (String) arg.getObjectData().get(TPMActivityBudgetAdjustFields.TO_BUDGET_TABLE_ID);
        int mode = getAdjustMode(arg);
        if (mode == 1 || mode == 2) {
            budgetService.tryLockBudget(actionContext, "fromBudget", fromBudgetId);
        }
        if (mode != 2) {
            budgetService.tryLockBudget(actionContext, "toBudget", toBudgetId);
        }
    }
}

package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.api.enumeration.LogType;
import com.facishare.crm.fmcg.tpm.api.log.LogData;
import com.facishare.crm.fmcg.tpm.api.method.IdempotentArgBase;
import com.facishare.crm.fmcg.tpm.apiname.*;
import com.facishare.crm.fmcg.tpm.business.BudgetService;
import com.facishare.crm.fmcg.tpm.business.OperateInfoService;
import com.facishare.crm.fmcg.tpm.common.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.service.PackTransactionProxy;
import com.facishare.crm.fmcg.tpm.service.PackTransactionProxyImpl;
import com.facishare.crm.fmcg.tpm.service.TransactionService;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/12/16 下午4:02
 */
@SuppressWarnings("Duplicates")
public class TPMDealerActivityCostObjInvalidAction extends StandardInvalidAction implements TransactionService<StandardInvalidAction.Arg, StandardInvalidAction.Result> {

    private static final BudgetService budgetService = SpringUtil.getContext().getBean(BudgetService.class);

    private final PackTransactionProxy packTransactionProxy = SpringUtil.getContext().getBean(PackTransactionProxyImpl.class);

    private OperateInfoService operateInfoService = SpringUtil.getContext().getBean(OperateInfoService.class);

    private static final Logger LOGGER = LoggerFactory.getLogger(TPMDealerActivityCostObjInvalidAction.class);


    @Override
    protected void before(Arg arg) {
        IObjectData cost = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), arg.getObjectDataId(), ApiNames.TPM_DEALER_ACTIVITY_COST);
        if (cost.get("enter_into_account", Boolean.class, false)) {
            throw new ValidateException(I18N.text(I18NKeys.THIS_SINGLE_COST_HAS_TRANSFER_IN_ACCOUNT_DO_NOT_SUPPORT_INVALID));
        }
        super.before(arg);
    }

    @Override
    protected Result doAct(Arg arg) {

        return packTransactionProxy.packAct(this, arg);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        return super.after(arg, result);
    }


    public void rmRelated(Result result) {

        String costId = result.getObjectData().getId();

        SearchTemplateQuery proofQuery = new SearchTemplateQuery();
        proofQuery.setLimit(-1);
        proofQuery.setOffset(0);
        proofQuery.setSearchSource("db");

        IFilter costFilter = new Filter();
        costFilter.setFieldName(TPMActivityProofFields.DEALER_ACTIVITY_COST_ID);
        costFilter.setOperator(Operator.EQ);
        costFilter.setFieldValues(Lists.newArrayList(costId));
        proofQuery.setFilters(Lists.newArrayList(costFilter));

        List<IObjectData> proofs = CommonUtils.queryData(serviceFacade, User.systemUser(actionContext.getTenantId()), ApiNames.TPM_ACTIVITY_PROOF_OBJ, proofQuery);

        LOGGER.info("proofs size:{}", proofs.size());
        List<String> proofUpdateFields = Lists.newArrayList(TPMActivityProofFields.DEALER_ACTIVITY_COST_ID);
        for (List<IObjectData> proofObjs : Lists.partition(proofs, 50)) {
            proofObjs.forEach(v -> v.set(TPMActivityProofFields.DEALER_ACTIVITY_COST_ID, ""));
            List<IObjectData> r = serviceFacade.batchUpdateByFields(User.systemUser(actionContext.getTenantId()), proofObjs, proofUpdateFields);
            LOGGER.info("proofs update size:{}", r.stream().filter(v -> Strings.isNullOrEmpty(v.get(TPMActivityProofFields.DEALER_ACTIVITY_COST_ID, String.class))).count());
        }


        SearchTemplateQuery proofAuditQuery = new SearchTemplateQuery();
        proofAuditQuery.setLimit(-1);
        proofAuditQuery.setOffset(0);
        proofAuditQuery.setSearchSource("db");

        IFilter costForAuditFilter = new Filter();
        costForAuditFilter.setFieldName(TPMActivityProofAuditFields.DEALER_ACTIVITY_COST_ID);
        costForAuditFilter.setOperator(Operator.EQ);
        costForAuditFilter.setFieldValues(Lists.newArrayList(costId));
        proofAuditQuery.setFilters(Lists.newArrayList(costForAuditFilter));

        List<IObjectData> proofAudits = CommonUtils.queryData(serviceFacade, User.systemUser(actionContext.getTenantId()), ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ, proofAuditQuery);
        LOGGER.info("proofAudits size:{}", proofAudits.size());
        List<String> proofAuditUpdateFields = Lists.newArrayList(TPMActivityProofAuditFields.DEALER_ACTIVITY_COST_ID);
        for (List<IObjectData> proofAuditObjs : Lists.partition(proofAudits, 50)) {
            proofAuditObjs.forEach(v -> v.set(TPMActivityProofAuditFields.DEALER_ACTIVITY_COST_ID, ""));
            List<IObjectData> r = serviceFacade.batchUpdateByFields(User.systemUser(actionContext.getTenantId()), proofAuditObjs, proofAuditUpdateFields);
            LOGGER.info("proofAudits update size:{}", r.stream().filter(v -> Strings.isNullOrEmpty(v.get(TPMActivityProofFields.DEALER_ACTIVITY_COST_ID, String.class))).count());
        }
    }

    @Override
    public Result doActTransaction(Arg arg) {
        Result result = super.doAct(arg);
        String activityId = (String) result.getObjectData().getOrDefault(TPMDealerActivityCostFields.ACTIVITY_ID, "");
        IObjectData activity = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), activityId, ApiNames.TPM_ACTIVITY_OBJ);
        if (budgetService.isOpenBudge(Integer.parseInt(actionContext.getTenantId()))) {
            try {
                //IObjectData cost = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), arg.getObjectDataId(), ApiNames.TPM_DEALER_ACTIVITY_COST);
                String budgetId = activity.get(TPMActivityFields.BUDGET_TABLE, String.class, "");
                if (!Strings.isNullOrEmpty(activityId)) {
                    if (!Strings.isNullOrEmpty(budgetId)) {
                        budgetService.tryLockBudget(actionContext, budgetId);
                    } else {
                        budgetService.tryLockBudget(actionContext, activityId);
                    }
                    if (TPMGrayUtils.excessDeductionForCost(actionContext.getTenantId())) {
                        Map<String, Double> amountMap = budgetService.calculateActivity(actionContext.getTenantId(), activity.getId());
                        double activityAmount = Double.parseDouble(activity.get(TPMActivityFields.ACTIVITY_AMOUNT, String.class, "0"));
                        double actualAmount = Double.parseDouble(activity.get(TPMActivityFields.ACTIVITY_ACTUAL_AMOUNT, String.class, "0"));
                        double calActivityAmount = amountMap.getOrDefault(TPMActivityFields.ACTIVITY_ACTUAL_AMOUNT, 0D);
                        if (activityAmount < actualAmount) {
                            double change = actualAmount - Math.max(calActivityAmount, activityAmount);
                            LogData logData = LogData.builder().data(JSON.toJSONString(arg)).build();
                            String logId = operateInfoService.log(actionContext.getTenantId(), LogType.INVALID.value(), JSON.toJSONString(logData), actionContext.getUser().getUserId(), ApiNames.TPM_DEALER_ACTIVITY_COST, arg.getObjectDataId(), false);
                            IObjectData lastDetail = budgetService.getLastBudgetDetail(actionContext.getTenantId(), budgetId);

                            double beforeAmount = lastDetail.get(TPMActivityBudgetDetailFields.AMOUNT_AFTER_OPERATION, Double.class, 0D);
                            double afterAmount = beforeAmount + change;

                            budgetService.addBudgetDetail(actionContext.getTenantId(), actionContext.getUser().getUserId(),
                                    "2",
                                    budgetId,
                                    String.format("「%s」活动方案超额核销作废", activity.getName()),
                                    change,
                                    beforeAmount,
                                    afterAmount,
                                    System.currentTimeMillis(),
                                    String.format("LogId:%s-traceId:%s", logId, TraceContext.get().getTraceId()),
                                    activity.getId(),
                                    TraceContext.get().getTraceId(),
                                    IdempotentArgBase.builder().idempotentKey(actionContext.getPostId() + ":" + budgetId).build());
                        }
                        budgetService.calculateBudget(actionContext.getTenantId(), budgetId);
                    } else {
                        budgetService.calculateActivity(actionContext.getTenantId(), activityId);
                        budgetService.calculateBudget(actionContext.getTenantId(), budgetId);
                    }
                }
            } finally {
                budgetService.unLockBudget(actionContext);
            }
        } else {
            budgetService.calculateActivity(actionContext.getTenantId(), activityId);
        }
        rmRelated(result);
        return result;
    }
}

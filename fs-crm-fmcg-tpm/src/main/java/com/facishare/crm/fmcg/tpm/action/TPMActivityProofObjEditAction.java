package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.api.visit.ActivityProofDTO;
import com.facishare.crm.fmcg.tpm.api.visit.ActivityProofDetailDTO;
import com.facishare.crm.fmcg.tpm.api.visit.ActivityProofImageDTO;
import com.facishare.crm.fmcg.tpm.api.visit.VisitActionDataDTO;
import com.facishare.crm.fmcg.tpm.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.apiname.CommonFields;
import com.facishare.crm.fmcg.tpm.apiname.TPMActivityProofDetailFields;
import com.facishare.crm.fmcg.tpm.apiname.TPMActivityProofFields;
import com.facishare.crm.fmcg.tpm.common.EasyValidator;
import com.facishare.crm.fmcg.tpm.service.BuryModule;
import com.facishare.crm.fmcg.tpm.service.BuryOperation;
import com.facishare.crm.fmcg.tpm.service.BuryService;
import com.facishare.crm.fmcg.tpm.service.CheckinService;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.common.release.GrayRelease;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2020/11/12 2:47 PM
 */
@SuppressWarnings("Duplicates")
public class TPMActivityProofObjEditAction extends StandardEditAction {

    private static final CheckinService checkinService = SpringUtil.getContext().getBean(CheckinService.class);
    private static final List<String> ALLOW_EDIT_FIELD_TYPE = Lists.newArrayList("image");

    @Override
    protected void before(Arg arg) {
        IObjectData oldData = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), arg.getObjectData().getId(), ApiNames.TPM_ACTIVITY_PROOF_OBJ);
        if (!TPMActivityProofFields.AUDIT_STATUS__SCHEDULE.equals(oldData.get(TPMActivityProofFields.AUDIT_STATUS))) {
            throw new ValidateException(I18N.text(I18NKeys.AUDITED_PROOF_CAN_NOT_EDIT));
        }

        visitInformationValidate();
        super.before(arg);
        if (!"ineffective".equalsIgnoreCase(arg.getObjectData().toObjectData().get(CommonFields.LIFE_STATUS, String.class))) {
            validateAllowEditField();
        }
    }

    void validateAllowEditField() {
        if (!this.detailChangeMap.isEmpty()) {
            throw new ValidateException(I18N.text(I18NKeys.CAN_NOT_EDIT_PROOF_DETAIL));
        }

        IObjectDescribe describe = serviceFacade.findObject(actionContext.getTenantId(), ApiNames.TPM_ACTIVITY_PROOF_OBJ);
        Set<String> allowField = Sets.newHashSet();

        describe.getFieldDescribeMap().forEach((k, field) -> {
            String apiName = (String) field.get("api_name");
            String type = (String) field.get("type");
            String defineType = (String) field.get("define_type");

            if ("custom".equals(defineType)) {
                allowField.add(apiName);
            }
            if (ALLOW_EDIT_FIELD_TYPE.contains(type)) {
                allowField.add(apiName);
            }
        });

        this.updatedFieldMap.keySet().forEach(v -> {
            if (!allowField.contains(v)) {
                throw new ValidateException(I18N.text(I18NKeys.CAN_NOT_EDIT_NON_IMAGE_FIELD));
            }
        });
    }

    @Override
    protected Result doAct(Arg arg) {
        //执行事务
        return super.doAct(arg);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        // 保存外勤后动作
        saveCheckinsAction(result);

        // 埋点
        BuryService.asyncTpmLog(Integer.valueOf(actionContext.getTenantId()), Integer.parseInt(actionContext.getUser().getUserIdOrOutUserIdIfOutUser()), BuryModule.TPM.TPM_ACTIVITY_PROOF, BuryOperation.UPDATE);
        return super.after(arg, result);
    }

    private void saveCheckinsAction(Result result) {
        String visitId = (String) result.getObjectData().get(TPMActivityProofFields.VISIT_ID);
        String actionId = (String) result.getObjectData().get(TPMActivityProofFields.ACTION_ID);
        if (!Strings.isNullOrEmpty(visitId) && !Strings.isNullOrEmpty(actionId)) {
            String storeId = (String) result.getObjectData().get(TPMActivityProofFields.STORE_ID);
            VisitActionDataDTO data = new VisitActionDataDTO();
            List<IObjectData> masterList = queryProof(actionContext, storeId, visitId, actionId);
            List<String> masterIds = masterList.stream().map(DBRecord::getId).collect(Collectors.toList());
            List<String> activityIds = masterList.stream().map(master -> (String) master.get(TPMActivityProofFields.ACTIVITY_ID)).collect(Collectors.toList());
            Map<String, IObjectData> activityMap = queryActivity(actionContext, activityIds).stream().collect(Collectors.toMap(DBRecord::getId, v -> v));
            Map<String, List<IObjectData>> detailsMap = queryActivityProofDetails(actionContext, masterIds).stream().collect(Collectors.groupingBy(detail -> (String) detail.get(TPMActivityProofDetailFields.ACTIVITY_PROOF_ID)));
            Map<String, String> itemNameMap = queryActivityItem(actionContext).stream().collect(Collectors.toMap(DBRecord::getId, IObjectData::getName));
            data.setActivityProofList(Lists.newArrayList());
            for (IObjectData master : masterList) {
                ActivityProofDTO datum = new ActivityProofDTO();
                datum.setProofId(master.getId());
                datum.setRemark((String) master.get(TPMActivityProofFields.REMARK));
                String activityId = (String) master.get(TPMActivityProofFields.ACTIVITY_ID);
                if (activityMap.containsKey(activityId)) {
                    datum.setActivityName(activityMap.get(activityId).getName());
                }
                List<ActivityProofImageDTO> proofImages = JSON.parseArray(JSON.toJSONString(master.get(TPMActivityProofFields.PROOF_IMAGES)), ActivityProofImageDTO.class);
                datum.setImages(CollectionUtils.isEmpty(proofImages) ? Lists.newArrayList() : proofImages);
                datum.setImagesTotalCount(datum.getImages().size());
                datum.setDetails(Lists.newArrayList());
                if (detailsMap.containsKey(master.getId())) {
                    List<IObjectData> details = detailsMap.get(master.getId());
                    for (IObjectData detail : details) {
                        ActivityProofDetailDTO detailDatum = new ActivityProofDetailDTO();
                        String activityItemId = (String) detail.get(TPMActivityProofDetailFields.ACTIVITY_ITEM_ID);
                        detailDatum.setName(itemNameMap.getOrDefault(activityItemId, "--"));
                        detailDatum.setAmount((String) detail.get(TPMActivityProofDetailFields.AMOUNT));
                        datum.getDetails().add(detailDatum);
                    }
                }
                data.getActivityProofList().add(datum);
            }
            data.setActivityProofListSize(data.getActivityProofList().size());
            String updateActionResult = checkinService.updateProofAction(actionContext.getUser(), visitId, actionId, data);
            result.getObjectData().put("__update_action_result", updateActionResult);
        }
    }


    private void visitInformationValidate() {
        if (!GrayRelease.isAllow("fmcg", "TPM_WEB_PROOF", actionContext.getTenantId())) {
            EasyValidator.notNullOrEmpty((String) arg.getObjectData().get(TPMActivityProofFields.VISIT_ID), String.format(I18N.text(I18NKeys.CHECKINS_INFO_IS_LOST_CAN_NOT_SUPPORT_PROOF), "visit_id"));
            EasyValidator.notNullOrEmpty((String) arg.getObjectData().get(TPMActivityProofFields.ACTION_ID), String.format(I18N.text(I18NKeys.CHECKINS_INFO_IS_LOST_CAN_NOT_SUPPORT_PROOF), "action_id"));
        }
    }


    private List<IObjectData> queryActivityItem(ActionContext context) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");
        query.setFilters(Lists.newArrayList());

        return CommonUtils.queryData(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_ITEM_OBJ, query);
    }

    private List<IObjectData> queryActivityProofDetails(ActionContext context, List<String> masterIds) {

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter masterFilter = new Filter();
        masterFilter.setFieldName(TPMActivityProofDetailFields.ACTIVITY_PROOF_ID);
        masterFilter.setOperator(Operator.IN);
        masterFilter.setFieldValues(masterIds);

        query.setFilters(Lists.newArrayList(masterFilter));
        return CommonUtils.queryData(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ, query);
    }

    private List<IObjectData> queryActivity(ActionContext context, List<String> ids) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter idFilter = new Filter();
        idFilter.setFieldName(CommonFields.ID);
        idFilter.setOperator(Operator.IN);
        idFilter.setFieldValues(ids);

        query.setFilters(Lists.newArrayList(idFilter));
        return CommonUtils.queryData(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_OBJ, query);
    }

    private List<IObjectData> queryProof(ActionContext context, String storeId, String visitId, String actionId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter storeIdFilter = new Filter();
        storeIdFilter.setFieldName(TPMActivityProofFields.STORE_ID);
        storeIdFilter.setOperator(Operator.EQ);
        storeIdFilter.setFieldValues(Lists.newArrayList(storeId));

        Filter visitIdFilter = new Filter();
        visitIdFilter.setFieldName(TPMActivityProofFields.VISIT_ID);
        visitIdFilter.setOperator(Operator.EQ);
        visitIdFilter.setFieldValues(Lists.newArrayList(visitId));

        Filter actionIdFilter = new Filter();
        actionIdFilter.setFieldName(TPMActivityProofFields.ACTION_ID);
        actionIdFilter.setOperator(Operator.EQ);
        actionIdFilter.setFieldValues(Lists.newArrayList(actionId));

        query.setFilters(Lists.newArrayList(storeIdFilter, actionIdFilter, visitIdFilter));

        return CommonUtils.queryData(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_PROOF_OBJ, query);
    }
}
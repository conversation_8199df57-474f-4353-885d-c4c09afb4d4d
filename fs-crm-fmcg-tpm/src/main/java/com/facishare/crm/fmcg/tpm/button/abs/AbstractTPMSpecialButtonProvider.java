package com.facishare.crm.fmcg.tpm.button.abs;

import com.facishare.crm.fmcg.tpm.utils.ButtonUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.metadata.button.SpecialButtonProvider;
import com.facishare.paas.metadata.ui.layout.IButton;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/9/8 下午3:26
 */
public abstract class AbstractTPMSpecialButtonProvider implements SpecialButtonProvider {

    @Override
    public List<IButton> getSpecialButtons() {
        List<IButton> buttons = new ArrayList<>(3);
        buttons.add(ButtonUtils.buildButton(ObjectAction.CHANGE_PARTNER));
        buttons.add(ButtonUtils.buildButton(ObjectAction.DELETE_PARTNER));
        buttons.add(ButtonUtils.buildButton(ObjectAction.CHANGE_PARTNER_OWNER));
        return buttons;
    }
}

package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.tpm.api.activity.EnableList;
import com.facishare.crm.fmcg.tpm.apiname.*;
import com.facishare.crm.fmcg.tpm.business.StoreBusiness;
import com.facishare.crm.fmcg.tpm.common.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.service.OrganizationService;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.privilege.dto.PrivilegeResult;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@SuppressWarnings("Duplicates")
public class TPMActivityObjEnableListController extends PreDefineController<EnableList.Arg, EnableList.Result> {

    public static final String VISIT_COMPLETED_STATUS = "4";
    private static final OrganizationService organizationService = SpringUtil.getContext().getBean(OrganizationService.class);
    private static final StoreBusiness storeBusiness = SpringUtil.getContext().getBean(StoreBusiness.class);

    private static final Map<String, String> GROUP_NAME_MAP = new HashMap<>();

    private static final String PARTICIPATED_GROUP_KEY = "participated";
    private static final String NO_PARTICIPATED_GROUP_KEY = "no_participated";

    static {
        GROUP_NAME_MAP.put(PARTICIPATED_GROUP_KEY, "已参加");
        GROUP_NAME_MAP.put(NO_PARTICIPATED_GROUP_KEY, "未参加");
    }

    private final IObjectDataService objectDataService = SpringUtil.getContext().getBean(ObjectDataServiceImpl.class);

    @Override
    protected EnableList.Result doService(EnableList.Arg arg) {

        log.info("enable list arg : {}", JSON.toJSONString(arg));

        boolean visitCompleted = VISIT_COMPLETED_STATUS.equals(arg.getVisitStatus());

        if (Strings.isNullOrEmpty(arg.getVisitId()) || Strings.isNullOrEmpty(arg.getActionId())) {
            throw new ValidateException("visit_id or action_id can not be empty.");
        }

        if (Strings.isNullOrEmpty(arg.getStoreId())) {
            throw new ValidateException("未获取到门店信息，请重试。");
        }

        IObjectData storeData = serviceFacade.findObjectDataIgnoreAll(User.systemUser(controllerContext.getTenantId()), arg.getStoreId(), ApiNames.ACCOUNT_OBJ);
        if (storeData == null) {
            throw new ValidateException("store not found.");
        }

        if (visitCompleted) {
            return listWhenCompleted(controllerContext, arg, storeData);
        } else {
            if (TPMGrayUtils.newActivityEnableCheck(controllerContext.getTenantId())) {
                return listWhenScheduleV1(controllerContext, arg, storeData);
            } else {
                return listWhenSchedule(controllerContext, arg, storeData);
            }
        }
    }

    private EnableList.Result listWhenCompleted(ControllerContext context, EnableList.Arg arg, IObjectData store) {
        List<IObjectData> proofList = queryProof(context, arg.getStoreId(), arg.getVisitId(), arg.getActionId());

        Map<String, IObjectData> proofMap = new HashMap<>();
        for (IObjectData proof : proofList) {
            String activityId = (String) proof.get(TPMActivityProofFields.ACTIVITY_ID);
            proofMap.put(activityId, proof);
        }

        EnableList.Result result = new EnableList.Result();
        Map<String, List<String>> functionCodeListMap = new HashMap<>();
        functionCodeListMap.put(ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, Lists.newArrayList("Add", "Edit", "View", "List"));
        result.setFunctionCodeMap(queryFunctionCodeMap(functionCodeListMap));
        result.setStoreId(store.getId());
        result.setStoreName(store.getName());
        result.setNavigateStrategy("activity_list");
        result.setData(Lists.newArrayList());
        result.setSystemTime(System.currentTimeMillis());

        if (proofMap.keySet().isEmpty()) {
            return result;
        }

        List<IObjectData> activityList = queryActivity(context, new ArrayList<>(proofMap.keySet()));

        EnableList.ActivityGroupVO group = new EnableList.ActivityGroupVO();
        group.setGroupKey(PARTICIPATED_GROUP_KEY);
        group.setGroupName(GROUP_NAME_MAP.getOrDefault(PARTICIPATED_GROUP_KEY, "--"));

        Map<String, Long> proofCountMap = countProof(context.getTenantId(), arg.getStoreId()).stream().collect(Collectors.toMap(k -> k.get(TPMActivityProofFields.ACTIVITY_ID).toString(), v -> Long.parseLong(v.get("count").toString())));
        Map<String, Long> agreementCountMap = countAgreement(context.getTenantId(), arg.getStoreId()).stream().collect(Collectors.toMap(k -> k.get(TPMActivityAgreementFields.ACTIVITY_ID) == null ? "" : k.get(TPMActivityAgreementFields.ACTIVITY_ID).toString(), v -> Long.parseLong(v.get("count").toString())));

        for (IObjectData activity : activityList) {
            EnableList.ActivityVO datum = new EnableList.ActivityVO();

            datum.setId(activity.getId());
            datum.setName(activity.getName());
            datum.setBeginDate((long) activity.get(TPMActivityFields.BEGIN_DATE));
            datum.setEndDate((long) activity.get(TPMActivityFields.END_DATE));
            datum.setProofCount(proofCountMap.getOrDefault(activity.getId(), 0L));
            datum.setAgreementCount(agreementCountMap.getOrDefault(activity.getId(), 0L));
            datum.setAgreementRequired(false);

            String proofRecordType = activity.get(TPMActivityFields.PROOF_RECORD_TYPE, String.class);
            if (Strings.isNullOrEmpty(proofRecordType)) {
                proofRecordType = activity.get(TPMActivityFields.PROOF_RECORD_TYPE__C, String.class);
                if (Strings.isNullOrEmpty(proofRecordType)) {
                    proofRecordType = "default__c";
                }
            }
            datum.setProofRecordType(proofRecordType);

            datum.setActivityAgreementId("");
            datum.setStatus("completed");

            if (proofMap.containsKey(activity.getId())) {
                datum.setDataApiName(ApiNames.TPM_ACTIVITY_PROOF_OBJ);
                datum.setDataId(proofMap.get(activity.getId()).getId());
            }

            group.getActivityList().add(datum);
        }
        result.getData().add(group);

        return result;
    }

    private EnableList.Result listWhenScheduleV1(ControllerContext context, EnableList.Arg arg, IObjectData store) {
        EnableList.Result result = new EnableList.Result();

        Map<String, List<String>> functionCodeListMap = new HashMap<>();
        functionCodeListMap.put(ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, Lists.newArrayList("Add", "Edit", "View", "List"));
        result.setFunctionCodeMap(queryFunctionCodeMap(functionCodeListMap));
        result.setStoreId(store.getId());
        result.setStoreName(store.getName());
        result.setSystemTime(System.currentTimeMillis());

        String dealerId = storeBusiness.findDealerId(context.getTenantId(), store);
        List<Integer> departmentIds = new ArrayList<>();
        if (!controllerContext.getUser().isOutUser()) {
            departmentIds = organizationService.getDepartmentIds(controllerContext.getUser().getTenantIdInt(), controllerContext.getUser().getUserIdInt());
            if (CollectionUtils.isEmpty(departmentIds)) {
                return result;
            }
            log.info("departments : {}", departmentIds);
        }
        long now = System.currentTimeMillis();

        List<String> activityTypeList = arg.getActivityTypeList();

        List<IObjectData> activities = TPMGrayUtils.isYinLuEnableList(context.getTenantId()) ? queryActivity(departmentIds, store.getId(), activityTypeList) : queryActivityV1(context, now, departmentIds, store.getId(), dealerId, activityTypeList);

        log.info("activities : {}", activities);

        Map<String, Long> proofCountMap = countProof(context.getTenantId(), arg.getStoreId()).stream().collect(Collectors.toMap(k -> k.get(TPMActivityProofFields.ACTIVITY_ID).toString(), v -> Long.parseLong(v.get("count").toString())));
        List<IObjectData> proofList = queryProof(context, arg.getStoreId(), arg.getVisitId(), arg.getActionId());
        Map<String, Long> agreementCountMap = countAgreement(context.getTenantId(), arg.getStoreId()).stream().collect(Collectors.toMap(k -> k.get(TPMActivityAgreementFields.ACTIVITY_ID) == null ? "" : k.get(TPMActivityAgreementFields.ACTIVITY_ID).toString(), v -> Long.parseLong(v.get("count").toString())));

        Map<String, IObjectData> proofMap = new HashMap<>();
        for (IObjectData proof : proofList) {
            String activityId = (String) proof.get(TPMActivityProofFields.ACTIVITY_ID);
            proofMap.put(activityId, proof);
        }

        List<IObjectData> completedActivities = queryActivityV1(context, new ArrayList<>(proofMap.keySet()));
        activities.addAll(completedActivities);

        Map<String, List<EnableList.ActivityVO>> activityMap = new HashMap<>();
        int total = 0;
        int inProgress = 0;
        int completed = 0;
        Set<String> duplicateActivityIdCache = new HashSet<>();
        for (IObjectData activity : activities) {
            if (duplicateActivityIdCache.contains(activity.getId())) {
                continue;
            }
            if (!proofMap.containsKey(activity.getId())) {
                String storeRangeJson = (String) activity.get(TPMActivityFields.STORE_RANGE);
                if (!Strings.isNullOrEmpty(storeRangeJson)) {
                    JSONObject storeRange = JSON.parseObject(storeRangeJson);
                    String type = storeRange.getString("type");
                    switch (type) {
                        case "CONDITION":
                            if (CollectionUtils.isEmpty(queryConditionStore(context, storeRange, arg.getStoreId()))) {
                                continue;
                            }
                            break;
                        default:
                        case "FIXED":
                        case "ALL":
                            break;
                    }
                }
            }

            EnableList.ActivityVO datum = toActivityVO(context, now, activity, proofCountMap, proofMap, agreementCountMap);
            if (TPMGrayUtils.isYinLu(controllerContext.getTenantId()) && "no_agreement".equals(datum.getStatus())) {
                continue;
            }

            duplicateActivityIdCache.add(datum.getId());

            total++;
            if ("in_progress".equals(datum.getStatus())) {
                inProgress++;
            }
            if ("completed".equals(datum.getStatus())) {
                completed++;
            }

            if (datum.getProofCount() > 0) {
                if (!activityMap.containsKey(PARTICIPATED_GROUP_KEY)) {
                    activityMap.put(PARTICIPATED_GROUP_KEY, Lists.newArrayList(datum));
                } else {
                    activityMap.get(PARTICIPATED_GROUP_KEY).add(datum);
                }
            } else {
                if (!activityMap.containsKey(NO_PARTICIPATED_GROUP_KEY)) {
                    activityMap.put(NO_PARTICIPATED_GROUP_KEY, Lists.newArrayList(datum));
                } else {
                    activityMap.get(NO_PARTICIPATED_GROUP_KEY).add(datum);
                }
            }
        }

        for (Map.Entry<String, List<EnableList.ActivityVO>> entry : activityMap.entrySet()) {
            EnableList.ActivityGroupVO group = new EnableList.ActivityGroupVO();
            group.setGroupKey(entry.getKey());
            group.setGroupName(GROUP_NAME_MAP.getOrDefault(entry.getKey(), "--"));
            group.setActivityList(entry.getValue());
            result.getData().add(group);
        }

        result.getData().sort(Comparator.comparing(EnableList.ActivityGroupVO::getGroupKey).reversed());

        if (total <= 0) {
            result.setNavigateStrategy("no_activity");
        } else if (total == 1 && inProgress == 1) {
            result.setNavigateStrategy("proof_audit");
        } else if (total == 1 && completed == 1) {
            result.setNavigateStrategy("activity_list");
        } else {
            result.setNavigateStrategy("activity_list");
        }

        return result;
    }

    private EnableList.Result listWhenSchedule(ControllerContext context, EnableList.Arg arg, IObjectData store) {
        EnableList.Result result = new EnableList.Result();

        Map<String, List<String>> functionCodeListMap = new HashMap<>();
        functionCodeListMap.put(ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, Lists.newArrayList("Add", "Edit", "View", "List"));
        result.setFunctionCodeMap(queryFunctionCodeMap(functionCodeListMap));
        result.setStoreId(store.getId());
        result.setStoreName(store.getName());
        result.setSystemTime(System.currentTimeMillis());

        String dealerId = storeBusiness.findDealerId(context.getTenantId(), store);
        List<Integer> departmentIds = new ArrayList<>();
        if (!controllerContext.getUser().isOutUser()) {
            departmentIds = organizationService.getDepartmentIds(controllerContext.getUser().getTenantIdInt(), controllerContext.getUser().getUserIdInt());
            if (CollectionUtils.isEmpty(departmentIds)) {
                return result;
            }
            log.info("departments : {}", departmentIds);
        }
        long now = System.currentTimeMillis();

        List<String> activityTypeList = arg.getActivityTypeList();

        List<IObjectData> activities = TPMGrayUtils.isYinLuEnableList(context.getTenantId()) ? queryActivity(departmentIds, store.getId(), activityTypeList) : queryActivity(context, now, departmentIds, store.getId(), dealerId, activityTypeList);

        log.info("activities : {}", activities);

        Map<String, Long> proofCountMap = countProof(context.getTenantId(), arg.getStoreId()).stream().collect(Collectors.toMap(k -> k.get(TPMActivityProofFields.ACTIVITY_ID).toString(), v -> Long.parseLong(v.get("count").toString())));
        List<IObjectData> proofList = queryProof(context, arg.getStoreId(), arg.getVisitId(), arg.getActionId());
        Map<String, Long> agreementCountMap = countAgreement(context.getTenantId(), arg.getStoreId()).stream().collect(Collectors.toMap(k -> k.get(TPMActivityAgreementFields.ACTIVITY_ID) == null ? "" : k.get(TPMActivityAgreementFields.ACTIVITY_ID).toString(), v -> Long.parseLong(v.get("count").toString())));

        Map<String, IObjectData> proofMap = new HashMap<>();
        for (IObjectData proof : proofList) {
            String activityId = (String) proof.get(TPMActivityProofFields.ACTIVITY_ID);
            proofMap.put(activityId, proof);
        }

        List<IObjectData> completedActivities = queryActivity(context, new ArrayList<>(proofMap.keySet()));
        activities.addAll(completedActivities);

        Map<String, List<EnableList.ActivityVO>> activityMap = new HashMap<>();
        int total = 0;
        int inProgress = 0;
        int completed = 0;
        Set<String> duplicateActivityIdCache = new HashSet<>();
        for (IObjectData activity : activities) {
            if (duplicateActivityIdCache.contains(activity.getId())) {
                continue;
            }
            if (!proofMap.containsKey(activity.getId())) {
                String storeRangeJson = (String) activity.get(TPMActivityFields.STORE_RANGE);
                if (!Strings.isNullOrEmpty(storeRangeJson)) {
                    JSONObject storeRange = JSON.parseObject(storeRangeJson);
                    String type = storeRange.getString("type");
                    switch (type) {
                        case "FIXED":
                            String activityDealerId = activity.get(TPMActivityFields.DEALER_ID, String.class, "");
                            if (!Strings.isNullOrEmpty(activityDealerId) && activityDealerId.equals(store.getId()))
                                break;
                            if (CollectionUtils.isEmpty(queryActivityStore(context, activity.getId(), arg.getStoreId()))) {
                                continue;
                            }
                            break;
                        case "CONDITION":
                            if (CollectionUtils.isEmpty(queryConditionStore(context, storeRange, arg.getStoreId()))) {
                                continue;
                            }
                            break;
                        default:
                        case "ALL":
                            break;
                    }
                }
            }

            EnableList.ActivityVO datum = toActivityVO(context, now, activity, proofCountMap, proofMap, agreementCountMap);
            if (TPMGrayUtils.isYinLu(controllerContext.getTenantId()) && "no_agreement".equals(datum.getStatus())) {
                continue;
            }

            duplicateActivityIdCache.add(datum.getId());

            total++;
            if ("in_progress".equals(datum.getStatus())) {
                inProgress++;
            }
            if ("completed".equals(datum.getStatus())) {
                completed++;
            }

            if (datum.getProofCount() > 0) {
                if (!activityMap.containsKey(PARTICIPATED_GROUP_KEY)) {
                    activityMap.put(PARTICIPATED_GROUP_KEY, Lists.newArrayList(datum));
                } else {
                    activityMap.get(PARTICIPATED_GROUP_KEY).add(datum);
                }
            } else {
                if (!activityMap.containsKey(NO_PARTICIPATED_GROUP_KEY)) {
                    activityMap.put(NO_PARTICIPATED_GROUP_KEY, Lists.newArrayList(datum));
                } else {
                    activityMap.get(NO_PARTICIPATED_GROUP_KEY).add(datum);
                }
            }
        }

        for (Map.Entry<String, List<EnableList.ActivityVO>> entry : activityMap.entrySet()) {
            EnableList.ActivityGroupVO group = new EnableList.ActivityGroupVO();
            group.setGroupKey(entry.getKey());
            group.setGroupName(GROUP_NAME_MAP.getOrDefault(entry.getKey(), "--"));
            group.setActivityList(entry.getValue());
            result.getData().add(group);
        }

        result.getData().sort(Comparator.comparing(EnableList.ActivityGroupVO::getGroupKey).reversed());

        if (total <= 0) {
            result.setNavigateStrategy("no_activity");
        } else if (total == 1 && inProgress == 1) {
            result.setNavigateStrategy("proof_audit");
        } else if (total == 1 && completed == 1) {
            result.setNavigateStrategy("activity_list");
        } else {
            result.setNavigateStrategy("activity_list");
        }

        return result;
    }

    private List<IObjectData> queryProof(ControllerContext context, String storeId, String visitId, String actionId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter storeIdFilter = new Filter();
        storeIdFilter.setFieldName(TPMActivityProofFields.STORE_ID);
        storeIdFilter.setOperator(Operator.EQ);
        storeIdFilter.setFieldValues(Lists.newArrayList(storeId));

        Filter visitIdFilter = new Filter();
        visitIdFilter.setFieldName(TPMActivityProofFields.VISIT_ID);
        visitIdFilter.setOperator(Operator.EQ);
        visitIdFilter.setFieldValues(Lists.newArrayList(visitId));

        Filter actionIdFilter = new Filter();
        actionIdFilter.setFieldName(TPMActivityProofFields.ACTION_ID);
        actionIdFilter.setOperator(Operator.EQ);
        actionIdFilter.setFieldValues(Lists.newArrayList(actionId));

        query.setFilters(Lists.newArrayList(storeIdFilter, actionIdFilter, visitIdFilter));

        OrderBy orderBy = new OrderBy();
        orderBy.setFieldName("create_time");
        orderBy.setIsAsc(true);
        orderBy.setIsNullLast(false);

        query.setOrders(Lists.newArrayList(orderBy));

        return CommonUtils.queryData(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_PROOF_OBJ, query);
    }

    private Map<String, Map<String, Boolean>> queryFunctionCodeMap(Map<String, List<String>> functionCodeListMap) {
        PrivilegeResult<Map<String, Map<String, Boolean>>> codeRst = serviceFacade.batchObjectFuncPrivilegeCheck(controllerContext.getRequestContext(), functionCodeListMap);
        return codeRst.getCode() == 0 ? codeRst.getResult() : new HashMap<>();
    }

    private List<IObjectData> queryActivityV1(ControllerContext context, List<String> activityIds) {
        if (activityIds.isEmpty()) {
            return new ArrayList<>();
        }
        String sql = String.format("select id as _id, * from fmcg_tpm_activity where tenant_id = '%s' and id in (%s)",
                context.getTenantId(),
                activityIds.stream().map(id -> String.format("'%s'", id)).collect(Collectors.joining(",")));
        try {
            return objectDataService.findBySql(sql, context.getTenantId(), ApiNames.TPM_ACTIVITY_OBJ).getData();
        } catch (MetadataServiceException e) {
            return Lists.newArrayList();
        }
    }

    private List<IObjectData> queryActivity(ControllerContext context, List<String> activityIds) {
        if (activityIds.isEmpty()) {
            return new ArrayList<>();
        }
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter idFilter = new Filter();
        idFilter.setFieldName(CommonFields.ID);
        idFilter.setOperator(Operator.IN);
        idFilter.setFieldValues(activityIds);

        query.setFilters(Lists.newArrayList(idFilter));
        return CommonUtils.queryData(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_OBJ, query);
    }

    private List<Map> countProof(String tenantId, String storeId) {
        String sql = String.format(
                "select activity_id, count(1) as count from fmcg_tpm_activity_proof where tenant_id = '%s' and store_id = '%s' and life_status = 'normal' group by activity_id",
                tenantId, storeId);
        try {
            return objectDataService.findBySql(tenantId, sql);
        } catch (MetadataServiceException e) {
            return Lists.newArrayList();
        }
    }

    private List<Map> countAgreement(String tenantId, String storeId) {
        String sql = String.format(
                "select activity_id, count(1) as count from fmcg_tpm_activity_agreement where tenant_id = '%s' and store_id = '%s' and is_deleted = 0 group by activity_id",
                tenantId, storeId);
        try {
            return objectDataService.findBySql(tenantId, sql);
        } catch (MetadataServiceException e) {
            return Lists.newArrayList();
        }
    }

    private List<IObjectData> queryActivityV1(ControllerContext context, long now, List<Integer> departmentIds, String storeId, String dealerId, List<String> activityTypeList) {
        String sql = "select\n" +
                "    id as _id,*\n" +
                "from\n" +
                "    fmcg_tpm_activity table_01\n" +
                "where\n" +
                "    tenant_id = '[tenant_id]'\n" +
                "    and life_status = 'normal'\n" +
                "    and begin_date < [now]\n" +
                "    and end_date > [now]\n" +
                "    and ( [skip_department_filter] = true or department_range in ([department_ids]))\n" +
                "    and (\n" +
                "        '[activity_type]' = 'all'\n" +
                "        or activity_type = '[activity_type]'\n" +
                "    )\n" +
                "    and (\n" +
                "        (dealer_id = '[store_id]')\n" +
                "        or (\n" +
                "            dealer_id is null\n" +
                "            and (\n" +
                "                store_range like '%\"ALL\"%'\n" +
                "                or (\n" +
                "                    store_range like '%\"FIXED\"%'\n" +
                "                    and exists (\n" +
                "                        select\n" +
                "                            id\n" +
                "                        from\n" +
                "                            fmcg_tpm_activity_store table_02\n" +
                "                        where\n" +
                "                            table_02.tenant_id = table_01.tenant_id\n" +
                "                            and table_02.life_status = 'normal'\n" +
                "                            and table_02.is_deleted = 0\n" +
                "                            and table_02.activity_id = table_01.id\n" +
                "                            and table_02.store_id = '[store_id]'\n" +
                "                    )\n" +
                "                )\n" +
                "                or store_range like '%\"CONDITION\"%'\n" +
                "            )\n" +
                "        )\n" +
                "        or (\n" +
                "            dealer_id = '[dealer_id]'\n" +
                "            and (\n" +
                "                store_range like '%\"ALL\"%'\n" +
                "                or (\n" +
                "                    store_range like '%\"FIXED\"%'\n" +
                "                    and exists (\n" +
                "                        select\n" +
                "                            id\n" +
                "                        from\n" +
                "                            fmcg_tpm_activity_store table_03\n" +
                "                        where\n" +
                "                            table_03.tenant_id = table_01.tenant_id\n" +
                "                            and table_03.life_status = 'normal'\n" +
                "                            and table_03.is_deleted = 0\n" +
                "                            and table_03.activity_id = table_01.id\n" +
                "                            and table_03.store_id = '[store_id]'\n" +
                "                    )\n" +
                "                )\n" +
                "                or store_range like '%\"CONDITION\"%'\n" +
                "            )\n" +
                "        )\n" +
                "    )";

        sql = sql.replace("[now]", String.valueOf(now));
        sql = sql.replace("[tenant_id]", context.getTenantId());
        String departmentIdsStr = departmentIds.stream().map(id -> String.format("'%s'", id)).collect(Collectors.joining(","));
        boolean skipDepartmentFilter = controllerContext.getUser().isOutUser() || TPMGrayUtils.denyDepartmentFilterOnActivity(context.getTenantId());
        sql = sql.replace("[skip_department_filter]", String.valueOf(skipDepartmentFilter));
        sql = sql.replace("[department_ids]", departmentIdsStr);
        String activityTypeStr = "all";
        if (CollectionUtils.isEmpty(activityTypeList) || activityTypeList.contains("all")) {
            activityTypeStr = "all";
        } else {
            activityTypeStr = activityTypeList.get(0);
        }
        sql = sql.replace("[activity_type]", activityTypeStr);
        sql = sql.replace("[store_id]", Strings.isNullOrEmpty(storeId) ? "" : storeId);
        sql = sql.replace("[dealer_id]", Strings.isNullOrEmpty(dealerId) ? "EMPTY" : dealerId);

        log.info("find activity sql : {}", sql);

        try {
            return objectDataService.findBySql(sql, context.getTenantId(), ApiNames.TPM_ACTIVITY_OBJ).getData();
        } catch (MetadataServiceException e) {
            return Lists.newArrayList();
        }
    }

    private List<IObjectData> queryActivity(ControllerContext context, long now, List<Integer> departmentIds, String storeId, String dealerId, List<String> activityTypeList) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setSearchSource("db");

        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnCountNum(false);
        query.setFilters(Lists.newArrayList());

        Filter beginDateFilter = new Filter();
        beginDateFilter.setFieldName(TPMActivityFields.BEGIN_DATE);
        beginDateFilter.setOperator(Operator.LT);
        beginDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));
        query.getFilters().add(beginDateFilter);

        Filter endDateFilter = new Filter();
        endDateFilter.setFieldName(TPMActivityFields.END_DATE);
        endDateFilter.setOperator(Operator.GT);
        endDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));
        query.getFilters().add(endDateFilter);

        Filter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.EQ);
        lifeStatusFilter.setFieldValues(Lists.newArrayList("normal"));
        query.getFilters().add(lifeStatusFilter);

        if (!CollectionUtils.isEmpty(activityTypeList) && !activityTypeList.contains("all")) {
            if (activityTypeList.size() == 1) {
                Filter activityType = new Filter();
                activityType.setFieldName(TPMActivityFields.ACTIVITY_TYPE);
                activityType.setOperator(Operator.EQ);
                activityType.setFieldValues(activityTypeList);
                query.getFilters().add(activityType);
            } else {
                Filter activityType = new Filter();
                activityType.setFieldName(TPMActivityFields.ACTIVITY_TYPE);
                activityType.setOperator(Operator.HASANYOF);
                activityType.setFieldValues(activityTypeList);
                query.getFilters().add(activityType);
            }
        }

        if (!controllerContext.getUser().isOutUser() && !TPMGrayUtils.denyDepartmentFilterOnActivity(context.getTenantId())) {
            Filter departmentRangeFilter = new Filter();
            departmentRangeFilter.setFieldName(TPMActivityFields.DEPARTMENT_RANGE);
            departmentRangeFilter.setOperator(Operator.IN);
            departmentRangeFilter.setFieldValues(departmentIds.stream().map(Object::toString).collect(Collectors.toList()));
            query.getFilters().add(departmentRangeFilter);
        }

        if (!Strings.isNullOrEmpty(dealerId)) {

            Wheres dealerIdEqualWhere = new Wheres();
            dealerIdEqualWhere.setConnector("OR");
            Filter dealerIdEqualFilter = new Filter();
            dealerIdEqualFilter.setFieldName(TPMActivityFields.DEALER_ID);
            dealerIdEqualFilter.setOperator(Operator.EQ);
            dealerIdEqualFilter.setFieldValues(Lists.newArrayList(dealerId));
            dealerIdEqualWhere.setFilters(Lists.newArrayList(dealerIdEqualFilter));

            Wheres dealerIdEmptyWhere = new Wheres();
            dealerIdEmptyWhere.setConnector("OR");
            Filter dealerIdEmptyFilter = new Filter();
            dealerIdEmptyFilter.setFieldName(TPMActivityFields.DEALER_ID);
            dealerIdEmptyFilter.setOperator(Operator.IS);
            dealerIdEmptyFilter.setFieldValues(Lists.newArrayList());
            dealerIdEmptyWhere.setFilters(Lists.newArrayList(dealerIdEmptyFilter));

            query.setWheres(Lists.newArrayList(dealerIdEmptyWhere, dealerIdEqualWhere));
        } else {

            Wheres dealerIdEqualStoreIdWhere = new Wheres();
            dealerIdEqualStoreIdWhere.setConnector("OR");
            Filter dealerIdEqualStoreIdFilter = new Filter();
            dealerIdEqualStoreIdFilter.setFieldName(TPMActivityFields.DEALER_ID);
            dealerIdEqualStoreIdFilter.setOperator(Operator.EQ);
            dealerIdEqualStoreIdFilter.setFieldValues(Lists.newArrayList(storeId));
            dealerIdEqualStoreIdWhere.setFilters(Lists.newArrayList(dealerIdEqualStoreIdFilter));

            Wheres dealerIdEmptyWhere = new Wheres();
            dealerIdEmptyWhere.setConnector("OR");
            Filter dealerIdEmptyFilter = new Filter();
            dealerIdEmptyFilter.setFieldName(TPMActivityFields.DEALER_ID);
            dealerIdEmptyFilter.setOperator(Operator.IS);
            dealerIdEmptyFilter.setFieldValues(Lists.newArrayList());
            dealerIdEmptyWhere.setFilters(Lists.newArrayList(dealerIdEmptyFilter));

            query.setWheres(Lists.newArrayList(dealerIdEmptyWhere, dealerIdEqualStoreIdWhere));
        }

        return CommonUtils.queryData(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_OBJ, query);
    }

    private List<IObjectData> queryActivityStore(ControllerContext context, String activityId, String storeId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(1);
        query.setOffset(0);
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);
        query.setSearchSource("db");

        Filter activityFilter = new Filter();
        activityFilter.setFieldName(TPMActivityStoreFields.ACTIVITY_ID);
        activityFilter.setOperator(Operator.EQ);
        activityFilter.setFieldValues(Lists.newArrayList(activityId));

        Filter storeIdFilter = new Filter();
        storeIdFilter.setFieldName(TPMActivityStoreFields.STORE_ID);
        storeIdFilter.setOperator(Operator.EQ);
        storeIdFilter.setFieldValues(Lists.newArrayList(storeId));

        query.setFilters(Lists.newArrayList(activityFilter, storeIdFilter));
        return serviceFacade.findBySearchQuery(User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_STORE_OBJ, query).getData();
    }

    private List<IObjectData> queryConditionStore(ControllerContext context, JSONObject storeRange, String storeId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(1);
        query.setOffset(0);
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);
        query.setSearchSource("db");

        List<Wheres> conditionWheres = new ArrayList<>();
        String valueJson = storeRange.getString("value");
        JSONArray wheres = JSON.parseArray(valueJson);
        for (int whereIndex = 0; whereIndex < wheres.size(); whereIndex++) {
            JSONObject where = wheres.getJSONObject(0);
            Wheres conditionWhere = new Wheres();
            conditionWhere.setConnector(where.getString("connector"));
            conditionWhere.setFilters(Lists.newArrayList());
            JSONArray filters = where.getJSONArray("filters");
            for (int filterIndex = 0; filterIndex < filters.size(); filterIndex++) {
                JSONObject filter = filters.getJSONObject(filterIndex);
                Filter conditionFilter = new Filter();
                conditionFilter.setFieldName(filter.getString("field_name"));
                conditionFilter.setIndexName(filter.getString("index_name"));
                conditionFilter.setFieldValueType(filter.getString("field_value_type"));
                conditionFilter.setOperator(Operator.valueOf(filter.getString("operator")));
                conditionFilter.setFieldValues(filter.getJSONArray("field_values").toJavaList(String.class));
                conditionFilter.setConnector(filter.getString("connector"));
                conditionFilter.setValueType(filter.getInteger("value_type"));
                conditionFilter.setRefDescribeApiName(filter.getString("ref_describe_api_name"));
                conditionFilter.setRefFieldApiName(filter.getString("ref_field_api_name"));
                conditionFilter.setIsCascade(filter.getBoolean("is_cascade"));
                conditionFilter.setIsMasterField(filter.getBoolean("is_master_field"));
                conditionFilter.setFilterGroup(filter.getString("filterGroup"));
                conditionWhere.getFilters().add(conditionFilter);
            }

            Filter idFilter = new Filter();
            idFilter.setFieldName("_id");
            idFilter.setOperator(Operator.EQ);
            idFilter.setFieldValues(Lists.newArrayList(storeId));
            idFilter.setConnector("AND");

            conditionWhere.getFilters().add(idFilter);
            conditionWheres.add(conditionWhere);
        }
        query.setWheres(conditionWheres);
        return serviceFacade.findBySearchQuery(User.systemUser(context.getTenantId()), ApiNames.ACCOUNT_OBJ, query).getData();
    }

    private EnableList.ActivityVO toActivityVO(ControllerContext context, long now, IObjectData activity, Map<String, Long> proofCountMap, Map<String, IObjectData> proofMap, Map<String, Long> agreementCountMap) {
        boolean agreementRequired = Boolean.TRUE.equals(activity.get(TPMActivityFields.IS_AGREEMENT_REQUIRED));

        EnableList.ActivityVO datum = new EnableList.ActivityVO();

        datum.setId(activity.getId());
        datum.setName(activity.getName());
        datum.setBeginDate((long) activity.get(TPMActivityFields.BEGIN_DATE));
        datum.setEndDate((long) activity.get(TPMActivityFields.END_DATE));
        datum.setAgreementRequired(agreementRequired);
        String proofRecordType = activity.get(TPMActivityFields.PROOF_RECORD_TYPE, String.class);
        if (Strings.isNullOrEmpty(proofRecordType)) {
            proofRecordType = activity.get(TPMActivityFields.PROOF_RECORD_TYPE__C, String.class);
            if (Strings.isNullOrEmpty(proofRecordType)) {
                proofRecordType = "default__c";
            }
        }
        datum.setProofRecordType(proofRecordType);
        datum.setAgreementCount(agreementCountMap.getOrDefault(activity.getId(), 0L));

        List<IObjectData> agreements = datum.getAgreementCount() != 0 ? queryAgreement(context, now, arg.getStoreId(), activity.getId()) : new ArrayList<>();

        Map<String, List<IObjectData>> agreementMap;
        if (TPMGrayUtils.agreementNotRelatedToActivity(controllerContext.getTenantId())) {
            agreementMap = new HashMap<>();
        } else {
            agreementMap = agreements.stream().collect(Collectors.groupingBy(agreement -> (String) agreement.get(TPMActivityAgreementFields.ACTIVITY_ID)));
        }

        if (!TPMGrayUtils.agreementNotRelatedToActivity(controllerContext.getTenantId()) && agreementRequired && agreementMap.containsKey(activity.getId())) {
            IObjectData agreement = agreementMap.get(activity.getId()).get(0);
            datum.setActivityAgreementId(agreement.getId());
            datum.setAgreementLifeStatus(agreement.get(CommonFields.LIFE_STATUS, String.class));
            datum.setAgreementBeginDate((long) agreement.get(TPMActivityAgreementFields.BEGIN_DATE));
            datum.setAgreementEndDate((long) agreement.get(TPMActivityAgreementFields.END_DATE));
            datum.setAgreementStatus(agreement.get(TPMActivityAgreementFields.AGREEMENT_STATUS, String.class));
        }

        datum.setProofCount(proofCountMap.getOrDefault(activity.getId(), 0L));
        if (proofMap.containsKey(activity.getId())) {
            datum.setStatus("completed");
            datum.setDataApiName(ApiNames.TPM_ACTIVITY_PROOF_OBJ);
            datum.setDataId(proofMap.get(activity.getId()).getId());
        } else {
            if (!TPMGrayUtils.agreementNotRelatedToActivity(controllerContext.getTenantId()) && datum.isAgreementRequired() && Strings.isNullOrEmpty(datum.getActivityAgreementId())) {
                datum.setStatus("no_agreement");
            } else if (TPMActivityAgreementFields.AGREEMENT_STATUS__SCHEDULE.equals(datum.getAgreementStatus())) {
                datum.setStatus("schedule_agreement");
            } else if (TPMActivityAgreementFields.AGREEMENT_STATUS__END.equals(datum.getAgreementStatus())) {
                datum.setStatus("agreement_expired");
            } else if (TPMActivityAgreementFields.AGREEMENT_STATUS__INVALID.equals(datum.getAgreementStatus())) {
                datum.setStatus("agreement_invalid");
            } else {
                datum.setStatus("in_progress");
            }
        }
        return datum;
    }

    private List<IObjectData> queryAgreement(ControllerContext context, long now, String storeId, String activityId) {

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnCountNum(false);
        query.setSearchSource("db");

        Filter beginDateFilter = new Filter();
        beginDateFilter.setFieldName(TPMActivityAgreementFields.BEGIN_DATE);
        beginDateFilter.setOperator(Operator.LT);
        beginDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

        Filter endDateFilter = new Filter();
        endDateFilter.setFieldName(TPMActivityAgreementFields.END_DATE);
        endDateFilter.setOperator(Operator.GT);
        endDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

        Filter storeIdFilter = new Filter();
        storeIdFilter.setFieldName(TPMActivityAgreementFields.STORE_ID);
        storeIdFilter.setOperator(Operator.EQ);
        storeIdFilter.setFieldValues(Lists.newArrayList(storeId));

        Filter activityIdFilter = new Filter();
        activityIdFilter.setFieldName(TPMActivityAgreementFields.ACTIVITY_ID);
        activityIdFilter.setOperator(Operator.EQ);
        activityIdFilter.setFieldValues(Lists.newArrayList(activityId));

        query.setFilters(Lists.newArrayList(beginDateFilter, endDateFilter, storeIdFilter, activityIdFilter));

        List<IObjectData> agreements = CommonUtils.queryData(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, query);
        //查未来
        if (CollectionUtils.isEmpty(agreements)) {
            query.setLimit(1);
            OrderBy timeOrder = new OrderBy();
            timeOrder.setIsAsc(true);
            timeOrder.setFieldName(TPMActivityAgreementFields.BEGIN_DATE);
            query.getOrders().clear();
            query.setOrders(Lists.newArrayList(timeOrder));
            query.getFilters().clear();

            Filter beginDate1Filter = new Filter();
            beginDate1Filter.setFieldName(TPMActivityAgreementFields.BEGIN_DATE);
            beginDate1Filter.setOperator(Operator.GT);
            beginDate1Filter.setFieldValues(Lists.newArrayList(String.valueOf(now)));
            query.setFilters(Lists.newArrayList(storeIdFilter, activityIdFilter, beginDate1Filter));

            agreements = CommonUtils.queryData(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, query);
            //查询过去
            if (CollectionUtils.isEmpty(agreements)) {
                query.setLimit(1);
                OrderBy time1Order = new OrderBy();
                time1Order.setIsAsc(false);
                time1Order.setFieldName(TPMActivityAgreementFields.END_DATE);
                query.getOrders().clear();
                query.setOrders(Lists.newArrayList(time1Order));
                query.getFilters().clear();

                Filter endDate1Filter = new Filter();
                endDate1Filter.setFieldName(TPMActivityAgreementFields.END_DATE);
                endDate1Filter.setOperator(Operator.LT);
                endDate1Filter.setFieldValues(Lists.newArrayList(String.valueOf(now)));
                query.setFilters(Lists.newArrayList(storeIdFilter, activityIdFilter, endDate1Filter));
                agreements = CommonUtils.queryData(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, query);
            }
        }

        return agreements;
    }

    private List<IObjectData> queryActivity(List<Integer> departmentIds, String storeId, List<String> activityTypeList) {
        SearchTemplateQuery queryAgreement = new SearchTemplateQuery();

        queryAgreement.setOffset(0);
        queryAgreement.setLimit(2000);

        queryAgreement.setSearchSource("db");

        Filter agreementStatusFilter = new Filter();
        agreementStatusFilter.setFieldName(TPMActivityAgreementFields.AGREEMENT_STATUS);
        agreementStatusFilter.setOperator(Operator.EQ);
        agreementStatusFilter.setFieldValues(Lists.newArrayList(TPMActivityAgreementFields.AGREEMENT_STATUS__IN_PROGRESS));

        Filter storeFilter = new Filter();
        storeFilter.setFieldName(TPMActivityAgreementFields.STORE_ID);
        storeFilter.setOperator(Operator.EQ);
        storeFilter.setFieldValues(Lists.newArrayList(storeId));

        queryAgreement.setFilters(Lists.newArrayList(agreementStatusFilter, storeFilter));

        List<IObjectData> aggList = serviceFacade.aggregateFindBySearchQuery(controllerContext.getTenantId(), queryAgreement, ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, TPMActivityAgreementFields.ACTIVITY_ID, "count", "");
        Set<String> activityIds = aggList.stream().map(v -> (String) v.get(TPMActivityAgreementFields.ACTIVITY_ID)).collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(activityIds)) {
            return new ArrayList<>();
        }

        SearchTemplateQuery queryActivity = new SearchTemplateQuery();

        queryActivity.setSearchSource("db");

        Filter activityIdFilter = new Filter();
        activityIdFilter.setFieldName(CommonFields.ID);
        activityIdFilter.setOperator(Operator.IN);
        activityIdFilter.setFieldValues(new ArrayList<>(activityIds));

        queryActivity.setFilters(Lists.newArrayList(activityIdFilter));

        if (!controllerContext.getUser().isOutUser() && !TPMGrayUtils.denyDepartmentFilterOnActivity(controllerContext.getTenantId())) {
            Filter departmentFilter = new Filter();
            departmentFilter.setFieldName(TPMActivityFields.DEPARTMENT_RANGE);
            departmentFilter.setOperator(Operator.IN);
            departmentFilter.setFieldValues(departmentIds.stream().map(String::valueOf).collect(Collectors.toList()));
            queryActivity.getFilters().add(departmentFilter);
        }

        if (!CollectionUtils.isEmpty(activityTypeList) && !activityTypeList.contains("all")) {
            Filter activityTypeFilter = new Filter();
            activityTypeFilter.setFieldName(TPMActivityFields.ACTIVITY_TYPE);
            activityTypeFilter.setOperator(Operator.IN);
            activityTypeFilter.setFieldValues(activityTypeList);
            queryActivity.getFilters().add(activityTypeFilter);
        }
        queryActivity.setLimit(-1);
        queryActivity.setOffset(0);

        Set<String> mainActivityIds = new HashSet<>();
        CommonUtils.queryData(serviceFacade, User.systemUser(controllerContext.getTenantId()), ApiNames.TPM_ACTIVITY_OBJ, queryActivity, partialData -> partialData.forEach(v -> mainActivityIds.add(v.getId())));
        return serviceFacade.findObjectDataByIds(controllerContext.getTenantId(), new ArrayList<>(mainActivityIds), ApiNames.TPM_ACTIVITY_OBJ);
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Collections.emptyList();
    }
}

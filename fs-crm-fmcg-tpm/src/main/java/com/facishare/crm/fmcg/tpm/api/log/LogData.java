package com.facishare.crm.fmcg.tpm.api.log;

import com.facishare.paas.metadata.api.IObjectData;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/7/19 下午3:20
 */
@Builder
@Data
@ToString
public class LogData {

    private String data;

    private String actionContext;

    private Map<String, Object> attributes;

    private Map<String,Object> updateMap;

    private IObjectData originalData;


    public void setAttribute(String key, Object value) {
        if (attributes == null)
            attributes = new HashMap<>();
        attributes.put(key, value);
    }
}

package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.crm.fmcg.tpm.common.TPMGrayUtils;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;

/**
 * author: wuyx
 * description:
 * createTime: 2022/8/11 18:18
 */
public class TPMActivityProofObjListController extends StandardListController {

    @Override
    protected void beforeQueryData(SearchTemplateQuery query) {
        if (TPMGrayUtils.TPMUseEs(controllerContext.getTenantId())) {
            query.setSearchSource("es");
        }
        super.beforeQueryData(query);
    }
}

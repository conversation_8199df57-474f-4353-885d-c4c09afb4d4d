package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.api.activity.ActivityClose;
import com.facishare.crm.fmcg.tpm.api.enumeration.LogType;
import com.facishare.crm.fmcg.tpm.api.log.LogData;
import com.facishare.crm.fmcg.tpm.api.method.IdempotentArgBase;
import com.facishare.crm.fmcg.tpm.apiname.*;
import com.facishare.crm.fmcg.tpm.business.BudgetService;
import com.facishare.crm.fmcg.tpm.business.OperateInfoService;
import com.facishare.crm.fmcg.tpm.common.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.service.*;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.AbstractStandardAction;
import com.facishare.paas.appframework.core.predef.action.StandardAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.github.jedis.support.MergeJedisCmd;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2020/11/6 2:27 PM
 */
@SuppressWarnings("Duplicates")
public class TPMActivityObjCloseActivityAction extends AbstractStandardAction<ActivityClose.Arg, ActivityClose.Result> implements TransactionService<ActivityClose.Arg, ActivityClose.Result> {

    public static final Logger log = LoggerFactory.getLogger(TPMActivityObjCloseActivityAction.class);

    private static final BudgetService budgetService = SpringUtil.getContext().getBean(BudgetService.class);

    private final PackTransactionProxy packTransactionProxy = SpringUtil.getContext().getBean(PackTransactionProxyImpl.class);

    private final OperateInfoService operateInfoService = SpringUtil.getContext().getBean(OperateInfoService.class);

    private static final String DEALER_COST_KEY = "DEALER_COST_KEY:%s:%s";

    private static final String PROOF_AUDIT_KEY = "PROOF_AUDIT_KEY:%s:%s";

    private final MergeJedisCmd redisCmd = SpringUtil.getContext().getBean("redisCmd", MergeJedisCmd.class);

    private boolean skipFuncPrivilege = false;

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        if (skipFuncPrivilege) {
            return new ArrayList<>();
        }
        return TPMGrayUtils.isSupportCloseActivityPrivileges(actionContext.getTenantId()) ? Lists.newArrayList(ObjectAction.CLOSE_TPM_ACTIVITY.getActionCode()) : StandardAction.Edit.getFunPrivilegeCodes();
    }

    @Override
    protected List<String> getDataPrivilegeIds(ActivityClose.Arg arg) {
        return Lists.newArrayList(arg.getObjectDataId());
    }

    @Override
    protected void before(ActivityClose.Arg arg) {
        if(Boolean.TRUE.equals(arg.getSkipDataValidate())){
            skipFuncPrivilege = true;
        }
        super.before(arg);
    }

    @Override
    protected ActivityClose.Result doAct(ActivityClose.Arg arg) {
        return packTransactionProxy.packAct(this, arg);
    }

    @Override
    protected ActivityClose.Result after(ActivityClose.Arg arg, ActivityClose.Result result) {
        BuryService.asyncTpmLog(Integer.parseInt(actionContext.getTenantId()), actionContext.getUser().getUserIdInt(), BuryModule.TPM.TPM_ACTIVITY, BuryOperation.CLOSE_THE_CASE);
        return super.after(arg, result);
    }

    private void validateEndActivity(ActivityClose.Arg arg, IObjectData activity) {
        if (activity == null) {
            throw new ValidateException("activity not found.");
        }

        log.info("activity info : {}", activity);

        String activityStatus = (String) activity.get(TPMActivityFields.ACTIVITY_STATUS);
        if (!TPMGrayUtils.isAllowProcessActivityClose(actionContext.getTenantId()) && !TPMActivityFields.ACTIVITY_STATUS__END.equals(activityStatus)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_HAS_NOT_END_CAN_NOT_CLOSE));
        }
        SearchTemplateQuery costQuery = new SearchTemplateQuery();
        costQuery.setLimit(1);
        costQuery.setOffset(0);
        costQuery.setSearchSource("db");
        IFilter costLifeStatusFilter = new Filter();
        costLifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        costLifeStatusFilter.setOperator(Operator.IN);
        costLifeStatusFilter.setFieldValues(Lists.newArrayList("under_review", "in_change"));
        IFilter costActivityFilter = new Filter();
        costActivityFilter.setFieldName(TPMDealerActivityCostFields.ACTIVITY_ID);
        costActivityFilter.setOperator(Operator.EQ);
        costActivityFilter.setFieldValues(Lists.newArrayList(arg.getObjectDataId()));
        costQuery.setFilters(Lists.newArrayList(costLifeStatusFilter, costActivityFilter));
        List<IObjectData> activities = serviceFacade.findBySearchQuery(User.systemUser(actionContext.getTenantId()), ApiNames.TPM_DEALER_ACTIVITY_COST, costQuery).getData();
        if (!activities.isEmpty()) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_HAS_UNCOMPLETED_COST_CAN_NOT_CLOSE));
        }


        //总共多少矩阵
        SearchTemplateQuery proofQuery2 = new SearchTemplateQuery();
        proofQuery2.setLimit(1);
        proofQuery2.setOffset(0);
        proofQuery2.setSearchSource("db");

        IFilter proofActivityFilter = new Filter();
        proofActivityFilter.setFieldName(TPMActivityProofFields.ACTIVITY_ID);
        proofActivityFilter.setOperator(Operator.EQ);
        proofActivityFilter.setFieldValues(Lists.newArrayList(arg.getObjectDataId()));

        proofQuery2.setFilters(Lists.newArrayList(proofActivityFilter));
        int proofTotal = serviceFacade.findBySearchQuery(User.systemUser(actionContext.getTenantId()), ApiNames.TPM_ACTIVITY_PROOF_OBJ, proofQuery2).getTotalNumber();

        SearchTemplateQuery proofAuditQuery = new SearchTemplateQuery();
        proofAuditQuery.setLimit(1);
        proofAuditQuery.setOffset(0);
        proofAuditQuery.setSearchSource("db");

        IFilter proofAuditActivityFilter = new Filter();
        proofAuditActivityFilter.setFieldName(TPMActivityProofAuditFields.ACTIVITY_ID);
        proofAuditActivityFilter.setOperator(Operator.EQ);
        proofAuditActivityFilter.setFieldValues(Lists.newArrayList(arg.getObjectDataId()));
        proofAuditQuery.setFilters(Lists.newArrayList(proofAuditActivityFilter));
        int proofAuditTotal = serviceFacade.findBySearchQuery(User.systemUser(actionContext.getTenantId()), ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ, proofAuditQuery).getTotalNumber();

        String isProofAuditConflict = redisCmd.get(String.format(PROOF_AUDIT_KEY, actionContext.getTenantId(), arg.getObjectDataId()));
        if (proofAuditTotal != proofTotal && Strings.isNullOrEmpty(isProofAuditConflict)) {
            redisCmd.set(String.format(PROOF_AUDIT_KEY, actionContext.getTenantId(), arg.getObjectDataId()), UUID.randomUUID().toString(), "nx", "ex", 10);
            throw new ValidateException("该活动方案有数据未检核，是否确认结案，\n如果确定结案，请10秒内重新结案。\n注意：活动结案后将无法进行活动检核与核销处理！");
        }


        String isDealerCostConflict = redisCmd.get(String.format(DEALER_COST_KEY, actionContext.getTenantId(), arg.getObjectDataId()));
        if (Strings.isNullOrEmpty(isDealerCostConflict) && validateProofNotWriteOff(arg)) {
            redisCmd.set(String.format(DEALER_COST_KEY, actionContext.getTenantId(), arg.getObjectDataId()), UUID.randomUUID().toString(), "nx", "ex", 10);
            throw new ValidateException("该活动方案有数据未核销，是否确认结案，\n如果确定结案，请10秒内重新结案。\n注意：活动结案后将无法进行活动检核与核销处理！");
        }


        String closeStatus = (String) activity.get(TPMActivityFields.CLOSED_STATUS);
        if (TPMActivityFields.CLOSE_STATUS__CLOSED.equals(closeStatus)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_HAS_CLOSED));
        }

    }

    boolean validateProofNotWriteOff(ActivityClose.Arg arg) {
        SearchTemplateQuery proofQuery = new SearchTemplateQuery();
        proofQuery.setLimit(1);
        proofQuery.setOffset(0);
        proofQuery.setSearchSource("db");

        IFilter proofActivityFilter = new Filter();
        proofActivityFilter.setFieldName(TPMActivityProofFields.ACTIVITY_ID);
        proofActivityFilter.setOperator(Operator.EQ);
        proofActivityFilter.setFieldValues(Lists.newArrayList(arg.getObjectDataId()));

        IFilter dealerCostFilter = new Filter();
        dealerCostFilter.setFieldName(TPMActivityProofFields.DEALER_ACTIVITY_COST_ID);
        dealerCostFilter.setOperator(Operator.IS);
        dealerCostFilter.setFieldValues(Lists.newArrayList());

        proofQuery.setFilters(Lists.newArrayList(proofActivityFilter, dealerCostFilter));

        List<IObjectData> proofs = serviceFacade.findBySearchQuery(User.systemUser(actionContext.getTenantId()), ApiNames.TPM_ACTIVITY_PROOF_OBJ, proofQuery).getData();
        if (!proofs.isEmpty())
            return true;
        SearchTemplateQuery allProofQuery = new SearchTemplateQuery();
        allProofQuery.setLimit(-1);
        allProofQuery.setOffset(0);
        allProofQuery.setSearchSource("db");

        allProofQuery.setFilters(Lists.newArrayList(proofActivityFilter));
        proofs = CommonUtils.queryData(serviceFacade, User.systemUser(actionContext.getTenantId()), ApiNames.TPM_ACTIVITY_PROOF_OBJ, allProofQuery);

        List<String> costIds = proofs.stream().map(proof -> proof.get(TPMActivityProofFields.DEALER_ACTIVITY_COST_ID, String.class, "")).distinct().collect(Collectors.toList());
        costIds.remove("");
        proofs.clear();

        SearchTemplateQuery costQuery = new SearchTemplateQuery();
        int offset = 0;
        costQuery.setLimit(200);
        costQuery.setOffset(offset);
        costQuery.setSearchSource("db");

        IFilter idFilter = new Filter();
        idFilter.setFieldName(CommonFields.ID);
        idFilter.setOperator(Operator.IN);


        IFilter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.IN);
        lifeStatusFilter.setFieldValues(Lists.newArrayList("ineffective"));

        costQuery.setFilters(Lists.newArrayList(idFilter, lifeStatusFilter));
        for (List<String> ids : Lists.partition(costIds, 200)) {
            idFilter.setFieldValues(ids);
            if (serviceFacade.findBySearchQuery(User.systemUser(actionContext.getTenantId()), ApiNames.TPM_DEALER_ACTIVITY_COST, costQuery).getTotalNumber() > 0)
                return true;
        }
        return false;
    }

    @Override
    protected void finallyDo() {
        try {
            super.finallyDo();
        } finally {
            budgetService.unLockBudget(actionContext);
        }
    }

    @Override
    public ActivityClose.Result doActTransaction(ActivityClose.Arg arg) {

        String activityId = arg.getObjectDataId();
        IObjectData activity = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), activityId, ApiNames.TPM_ACTIVITY_OBJ);
        String closeStatus = activity.get(TPMActivityFields.CLOSED_STATUS, String.class);
        if (TPMActivityFields.CLOSE_STATUS__CLOSED.equals(closeStatus)) {
            return new ActivityClose.Result();
        }

        String budgetId = activity.get(TPMActivityFields.BUDGET_TABLE, String.class, "");
        if (!Strings.isNullOrEmpty(budgetId)) {
            budgetService.tryLockBudget(actionContext, budgetId);
            if (!Boolean.TRUE.equals(arg.getSkipDataValidate())) {
                validateEndActivity(arg, activity);
            }
            double activityAmount = activity.get(TPMActivityFields.ACTIVITY_AMOUNT, Double.class, 0.0);
            double actualAmount = activity.get(TPMActivityFields.ACTIVITY_ACTUAL_AMOUNT, Double.class, 0.0);
            IObjectData budget = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), budgetId, ApiNames.TPM_ACTIVITY_BUDGET);
            Map<String, Double> amountMap = new HashMap<>();
            double availableAmount = budgetService.getBudgetAvailableAmount(actionContext.getTenantId(), budget, amountMap);


            LogData logData = LogData.builder().data(JSON.toJSONString(arg)).actionContext(JSON.toJSONString(actionContext)).build();
            logData.setAttribute("budget", budget);
            String logId = operateInfoService.log(actionContext.getTenantId(), LogType.CLOSE_ACTIVITY.value(), JSON.toJSONString(logData), actionContext.getUser().getUserId(), ApiNames.TPM_ACTIVITY_OBJ, arg.getObjectDataId(), false);


            if (activityAmount - actualAmount < 0 && !TPMGrayUtils.excessDeductionForCost(actionContext.getTenantId()))
                throw new ValidateException(I18N.text(I18NKeys.AMOUNT_OVER_THE_LIMIT_CAN_NOT_CLOSE));
            if (activityAmount - actualAmount > 0) {
                budgetService.addBudgetDetail(actionContext.getTenantId(), actionContext.getUser().getUserId(),
                        "2",
                        budgetId,
                        String.format("活动结案：「%s」结案", activity.get("name")),
                        activityAmount - actualAmount,
                        availableAmount,
                        availableAmount + activityAmount - actualAmount,
                        System.currentTimeMillis(),
                        String.format("LogId:%s-traceId:%s", logId, TraceContext.get().getTraceId()),
                        arg.getObjectDataId(),
                        TraceContext.get().getTraceId(),
                        IdempotentArgBase.builder().idempotentKey(actionContext.getPostId() + ":" + budgetId).build());

            }
        }
        //change status
        Map<String, Object> update = new HashMap<>();
        update.put(TPMActivityFields.CLOSE_STATUS, TPMActivityFields.CLOSE_STATUS__CLOSED);
        serviceFacade.updateWithMap(actionContext.getUser(), activity, update);
        budgetService.calculateBudget(actionContext.getTenantId(), activity.get(TPMActivityFields.BUDGET_TABLE, String.class, ""));

        return new ActivityClose.Result();
    }
}

package com.facishare.crm.fmcg.tpm.action;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardIncrementUpdateAction;
import de.lab4inf.math.util.Strings;

/**
 * <AUTHOR>
 * @date 2021/7/21 下午3:16
 */
public class TPMActivityBudgetAdjustObjIncrementUpdateAction extends StandardIncrementUpdateAction {

    @Override
    protected void before(Arg arg) {

        if(actionContext.isFromFunction()||actionContext.isFromOpenAPI()){
            arg.getData().keySet().forEach(key -> {
                if (!Strings.isNullOrEmpty(key) && !key.endsWith("__c") && !key.equals("_id"))
                    throw new ValidateException("不允许函数更新预算调整的预置字段。");
            });
        }
        super.before(arg);
    }
}

package com.facishare.crm.fmcg.tpm.service;

import com.facishare.organization.api.model.employee.EmployeeDto;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/3/25 0:28
 */
public interface OrganizationService {

    EmployeeDto getEmployee(int tenantId, int employeeId);

    List<EmployeeDto> queryAllEmployee(int tenantId);

    List<Integer> getDepartmentIds(int tenantId, int employeeId);

    List<Integer> queryEmployeeIds(int tenantId, int departmentId);

    boolean employeeInRange(int tenantId, int employeeId, List<Integer> departmentIds);
}

package com.facishare.crm.fmcg.tpm.api.activity;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

public interface SearchSuggest {

    @Data
    @ToString
    class Arg implements Serializable {

        /**
         * object api name
         */
        @SerializedName("api_name")
        @JSONField(name = "api_name")
        @JsonProperty("api_name")
        private String apiName;

        /**
         * object record type
         */
        @SerializedName("record_type")
        @JSONField(name = "record_type")
        @JsonProperty("record_type")
        private String recordType;

        /**
         * keyword parameter
         */
        @SerializedName("keyword")
        @JSONField(name = "keyword")
        @JsonProperty("keyword")
        private String keyword;
    }

    @Data
    @ToString
    class Result implements Serializable {

        private List<SuggestVO> data = Lists.newArrayList();
    }

    @Data
    @ToString
    @Builder
    class SuggestVO implements Serializable {

        @SerializedName("object_id")
        @JSONField(name = "object_id")
        @JsonProperty("object_id")
        private String objectId;

        /**
         * data id
         */
        private String id;

        /**
         * data name
         */
        private String name;
    }
}
package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.common.release.GrayRelease;

/**
 * author: wuyx
 * description:
 * createTime: 2022/8/10 18:26
 */
public class TPMActivityAgreementObjListController extends StandardListController {

    @Override
    protected SearchTemplateQuery buildSearchTemplateQuery() {
        SearchTemplateQuery query = super.buildSearchTemplateQuery();
        if (GrayRelease.isAllow("fmcg", "TPM_USE_ES_QUERY", controllerContext.getTenantId())) {
            query.setSearchSource("es");
        }
        return query;
    }
}

package com.facishare.crm.fmcg.tpm.service;

import com.facishare.paas.appframework.core.model.ControllerContext;

/**
 * <AUTHOR>
 * @date 2021/6/30 下午2:25
 */
public interface PackTransactionProxy {

    Object pack(ControllerContext controllerContext, Object arg);

    <A, R> void packAfter(TransactionService<A, R> transactionService, A arg, R result);

    <A, R> void packBefore(TransactionService<A, R> transactionService, A arg);

    <A, R> R packAct(TransactionService<A, R> transactionService, A arg);
}

package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.facishare.crm.fmcg.tpm.api.activity.TPMActivityScript;
import com.facishare.crm.fmcg.tpm.apiname.*;
import com.facishare.crm.fmcg.tpm.business.BudgetService;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.dao.pg.mapper.metadata.SpecialTableMapper;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fmcg.framework.http.FmcgServiceProxy;
import com.fmcg.framework.http.contract.fmcgservice.ExistRecord;
import com.fxiaoke.api.IdGenerator;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/8/20 下午5:54
 */
public class TPMActivityObjScriptController extends PreDefineController<TPMActivityScript.Arg, TPMActivityScript.Result> {

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList();
    }

    private SpecialTableMapper specialTableMapper = SpringUtil.getContext().getBean(SpecialTableMapper.class);

    private static final FmcgServiceProxy fmcgServiceProxy = SpringUtil.getContext().getBean(FmcgServiceProxy.class);

    private static final BudgetService budgetService = SpringUtil.getContext().getBean(BudgetService.class);

    private static final long[] DECIMAL_BASE = new long[]{0, 10, 100, 1000, 10000, 100000, 1000000, 10000000, 100000000};


    @Override
    protected TPMActivityScript.Result doService(TPMActivityScript.Arg arg) {
        switch (arg.getModule()) {
            case "proof_audit_update_cost_id":
                return proofAuditUpdateCostId(arg);
            case "init_budget_button":
                return initBudgetButton(arg);
            case "proof_random_audit_status_update":
                return updateProofRandomAuditStatus(arg);
            case "disassociate_cost":
                return disassociateCost(arg);
            case "fix_adjust_no_budget_detail":
                return fixAdjustNoBudgetDetail(arg);
            case "open_activity":
                return openActivity(arg);
            case "adjust_statistics":
                return adjustStatistics(arg);
            case "invalid_adjust":
                return invalidAdjustBySpecial(arg);
            case "reset_before_after_amount":
                return resetBeforeAfterAmountForAdjust(arg);
            case "recalculate_budget":
                return reCalculateBudget(arg);
            case "invalid_budget":
                return invalidBudget(arg);
            default:
        }
        return new TPMActivityScript.Result();
    }

    TPMActivityScript.Result updateProofRandomAuditStatus(TPMActivityScript.Arg arg) {

        for (String tenantId : arg.getTenantIds()) {
            ExistRecord.Arg existRecordArg = new ExistRecord.Arg();
            existRecordArg.setKey("TPM_AUDIT_MODE");
            existRecordArg.setValue("1");
            ExistRecord.Result existResult = fmcgServiceProxy.existRecord(Integer.parseInt(tenantId), -10000, existRecordArg);
            if (!existResult.getHasSet()) {
                log.info("{} is not random audit tenant.", tenantId);
                continue;
            }
            SearchTemplateQuery query = new SearchTemplateQuery();
            query.setLimit(-1);
            query.setOffset(0);
            query.setSearchSource("db");
            Filter randomAuditFilter = new Filter();
            randomAuditFilter.setFieldName(TPMActivityProofAuditFields.RANDOM_AUDIT_STATUS);
            randomAuditFilter.setOperator(Operator.ISN);
            randomAuditFilter.setFieldValues(Lists.newArrayList());
            query.setFilters(Lists.newArrayList(randomAuditFilter));

            CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ, query, (dataLists) -> {
                Map<String, IObjectData> proofId2Data = new HashMap<>();
                dataLists.forEach(data -> {
                    String randomAuditStatus = data.get(TPMActivityProofAuditFields.RANDOM_AUDIT_STATUS, String.class);
                    if (!Strings.isNullOrEmpty(randomAuditStatus)) {
                        proofId2Data.put(data.get(TPMActivityProofAuditFields.ACTIVITY_PROOF_ID, String.class), data);
                    }
                });
                List<IObjectData> proofs = serviceFacade.findObjectDataByIds(tenantId, new ArrayList<>(proofId2Data.keySet()), ApiNames.TPM_ACTIVITY_PROOF_OBJ);
                if (!CollectionUtils.isEmpty(proofs)) {
                    proofs.forEach(proof -> proof.set(TPMActivityProofFields.RANDOM_AUDIT_STATUS, proofId2Data.get(proof.getId()).get(TPMActivityProofAuditFields.RANDOM_AUDIT_STATUS, String.class)));
                    serviceFacade.batchUpdateByFields(User.systemUser(tenantId), proofs, Lists.newArrayList(TPMActivityProofFields.RANDOM_AUDIT_STATUS));
                }
            });

        }


        return new TPMActivityScript.Result();
    }

    TPMActivityScript.Result initBudgetButton(TPMActivityScript.Arg arg) {
        String sqlTemplate = "insert into mt_udef_button(\"id\",\"tenant_id\",\"api_name\",\"label\",\"use_pages\",\"describe_api_name\",\"description\",\"wheres\",\"param_form\",\"actions\",\"button_type\",\"jump_url\",\"create_time\",\"last_modified_time\",\"created_by\",\"last_modified_by\",\"is_active\",\"is_deleted\",\"version\",\"define_type\",\"is_batch\",\"url\",\"display\",\"redirect_type\",\"lock_data_show_button\")values('%s','%s','%s','%s','[\"list\"]','%s','',null,'[]',null,'common','',null,null,'-10000','-10000','t','f',1,'system',null,null,null,null,null);";
        for (String tenantId : arg.getTenantIds()) {
            List<Map> list = getBudgetButtonList(tenantId);
            Set<String> buttonApiNameSet = new HashSet<>();
            list.forEach(v -> buttonApiNameSet.add((String) v.get("api_name")));
            if (!buttonApiNameSet.contains("BudgetTransfer_button_default")) {
                (specialTableMapper.setTenantId(tenantId)).insertBySql(String.format(sqlTemplate, IdGenerator.get(), tenantId, "BudgetTransfer_button_default", "预算调拨", "TPMActivityBudgetObj"));
            }
            if (!buttonApiNameSet.contains("BudgetTransferIn_button_default")) {
                (specialTableMapper.setTenantId(tenantId)).insertBySql(String.format(sqlTemplate, IdGenerator.get(), tenantId, "BudgetTransferIn_button_default", "预算追加", "TPMActivityBudgetObj"));
            }
            if (!buttonApiNameSet.contains("BudgetTransferOut_button_default")) {
                (specialTableMapper.setTenantId(tenantId)).insertBySql(String.format(sqlTemplate, IdGenerator.get(), tenantId, "BudgetTransferOut_button_default", "预算扣减", "TPMActivityBudgetObj"));
            }
        }
        return new TPMActivityScript.Result();
    }

    private List<Map> getBudgetButtonList(String tenantId) {
        String sql = "select * from mt_udef_button where tenant_id = '%s' and api_name in ('BudgetTransfer_button_default','BudgetTransferIn_button_default','BudgetTransferOut_button_default') and describe_api_name = 'TPMActivityBudgetObj'";
        List<Map> list = (specialTableMapper.setTenantId(tenantId)).findBySql(String.format(sql, tenantId));
        return CollectionUtils.isEmpty(list) ? Lists.newArrayList() : list;
    }

    TPMActivityScript.Result proofAuditUpdateCostId(TPMActivityScript.Arg arg) {
        for (String tenantId : arg.getTenantIds()) {

            SearchTemplateQuery query = new SearchTemplateQuery();
            query.setLimit(-1);
            query.setOffset(0);
            query.setSearchSource("db");

            Filter proofFilter = new Filter();
            proofFilter.setFieldName(TPMActivityProofAuditFields.ACTIVITY_PROOF_ID);
            proofFilter.setOperator(Operator.ISN);
            proofFilter.setFieldValues(Lists.newArrayList());

            Filter costFilter = new Filter();
            costFilter.setFieldName(TPMActivityProofAuditFields.DEALER_ACTIVITY_COST_ID);
            costFilter.setOperator(Operator.IS);
            costFilter.setFieldValues(Lists.newArrayList());

            query.setFilters(Lists.newArrayList(proofFilter, costFilter));

            CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ, query, partialData -> {
                List<IObjectData> proofs = serviceFacade.findObjectDataByIds(tenantId, partialData.stream().map(v -> v.get(TPMActivityProofAuditFields.ACTIVITY_PROOF_ID, String.class)).collect(Collectors.toList()), ApiNames.TPM_ACTIVITY_PROOF_OBJ);
                Map<String, IObjectData> auditMap = new HashMap<>();
                partialData.forEach(audit -> auditMap.put(audit.get(TPMActivityProofAuditFields.ACTIVITY_PROOF_ID, String.class, ""), audit));
                proofs.forEach(proof -> {
                    String costId = proof.get(TPMActivityProofFields.DEALER_ACTIVITY_COST_ID, String.class);
                    if (!Strings.isNullOrEmpty(costId)) {
                        auditMap.get(proof.getId()).set(TPMActivityProofAuditFields.DEALER_ACTIVITY_COST_ID, costId);
                    } else {
                        auditMap.remove(proof.getId());
                    }
                });
                if (auditMap.size() > 0) {
                    serviceFacade.batchUpdateByFields(User.systemUser(tenantId), new ArrayList<>(auditMap.values()), Lists.newArrayList(TPMActivityProofAuditFields.DEALER_ACTIVITY_COST_ID));
                }
            });
        }

        return new TPMActivityScript.Result();
    }

    TPMActivityScript.Result disassociateCost(TPMActivityScript.Arg arg) {
        log.info("disassociate cost arg:{}", arg);
        JSONArray idLists = arg.getParams().getJSONArray("ids");
        arg.getTenantIds().forEach(tenantId -> {
            idLists.forEach(id -> {
                String costId = (String) id;

                SearchTemplateQuery proofQuery = new SearchTemplateQuery();
                proofQuery.setLimit(-1);
                proofQuery.setOffset(0);
                proofQuery.setSearchSource("db");

                IFilter costFilter = new Filter();
                costFilter.setFieldName(TPMActivityProofFields.DEALER_ACTIVITY_COST_ID);
                costFilter.setOperator(Operator.EQ);
                costFilter.setFieldValues(Lists.newArrayList(costId));
                proofQuery.setFilters(Lists.newArrayList(costFilter));

                List<IObjectData> proofs = CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_PROOF_OBJ, proofQuery);

                List<String> proofUpdateFields = Lists.newArrayList(TPMActivityProofFields.DEALER_ACTIVITY_COST_ID);
                for (List<IObjectData> proofObjs : Lists.partition(proofs, 200)) {
                    proofObjs.forEach(v -> v.set(TPMActivityProofFields.DEALER_ACTIVITY_COST_ID, ""));
                    serviceFacade.batchUpdateByFields(User.systemUser(tenantId), proofObjs, proofUpdateFields);
                }


                SearchTemplateQuery proofAuditQuery = new SearchTemplateQuery();
                proofAuditQuery.setLimit(-1);
                proofAuditQuery.setOffset(0);

                IFilter costForAuditFilter = new Filter();
                costForAuditFilter.setFieldName(TPMActivityProofAuditFields.DEALER_ACTIVITY_COST_ID);
                costForAuditFilter.setOperator(Operator.EQ);
                costForAuditFilter.setFieldValues(Lists.newArrayList(costId));
                proofAuditQuery.setFilters(Lists.newArrayList(costForAuditFilter));

                List<IObjectData> proofAudits = CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ, proofAuditQuery);

                List<String> proofAuditUpdateFields = Lists.newArrayList(TPMActivityProofAuditFields.DEALER_ACTIVITY_COST_ID);
                for (List<IObjectData> proofAuditObjs : Lists.partition(proofAudits, 200)) {
                    proofAuditObjs.forEach(v -> v.set(TPMActivityProofAuditFields.DEALER_ACTIVITY_COST_ID, ""));
                    serviceFacade.batchUpdateByFields(User.systemUser(tenantId), proofAuditObjs, proofAuditUpdateFields);
                }
            });
        });

        return new TPMActivityScript.Result();
    }

    TPMActivityScript.Result fixAdjustNoBudgetDetail(TPMActivityScript.Arg arg) {
        String baseSql = "select *  from fmcg_tpm_activity_budget_adjust  adjust  left join  fmcg_tpm_activity_budget_detail  detail on detail.budget_adjust_id  = adjust.id  and  detail.is_deleted  = adjust.is_deleted  where adjust.is_deleted = 0  and detail.budget_adjust_id is null and adjust.tenant_id = '%s' order by adjust.create_time asc  limit %s offset %s";
        int limit = Integer.parseInt((String) arg.getParams().getOrDefault("limit", "100"));
        int offset = Integer.parseInt((String) arg.getParams().getOrDefault("offset", "0"));
        boolean isIncreaseMode = arg.getParams().getBooleanValue("isIncreaseMode");
        int maxNum = Integer.parseInt((String) arg.getParams().getOrDefault("maxNum", "1000"));
        Long sleepTime = arg.getParams().getLongValue("sleepTime");
        for (String tenantId : arg.getTenantIds()) {
            String querySql = String.format(baseSql, tenantId, limit, offset);
            List<Map> dataMapList;
            Map<String, IObjectData> budgetMap = new HashMap<>();
            int count = 0;
            while (!CollectionUtils.isEmpty(dataMapList = (specialTableMapper.setTenantId(tenantId)).findBySql(querySql))) {
                count += dataMapList.size();
                if (count > maxNum) {
                    break;
                }
                dataMapList.forEach(dataMap -> {
                    //1支出 2收入
                    String id = (String) dataMap.get("id");
                    String fromBudgetId = (String) dataMap.get(TPMActivityBudgetAdjustFields.FROM_BUDGET_TABLE_ID);
                    String toBudgetId = (String) dataMap.get(TPMActivityBudgetAdjustFields.TO_BUDGET_TABLE_ID);

                    double amount = getValue(dataMap, TPMActivityBudgetAdjustFields.AMOUNT, Double.class);
                    long createTime = getValue(dataMap, CommonFields.CREATE_TIME, Long.class);
                    if (!Strings.isNullOrEmpty(fromBudgetId)) {
                        IObjectData fromBudget = getBudget(budgetMap, tenantId, fromBudgetId);
                        String sourceId = id + "from";
                        if (!isExist(tenantId, sourceId)) {
                            double beforeAmount = getValue(dataMap, TPMActivityBudgetAdjustFields.BALANCE_BEFORE_TRANSFER_OUT, Double.class);
                            double afterAmount = getValue(dataMap, TPMActivityBudgetAdjustFields.BALANCE_AFTER_TRANSFER_OUT, Double.class);
                            IObjectData detail = addBudgetDetail(tenantId, (String) dataMap.get("owner"), "1", fromBudgetId,
                                    String.format("预算调整：「%s」调整", fromBudget.get("name")), -amount, beforeAmount, afterAmount,
                                    createTime, null, null, sourceId, id, createTime, createTime
                            );
                            updateCreateTime(tenantId, detail, createTime);
                        }
                    }

                    if (!Strings.isNullOrEmpty(toBudgetId)) {
                        IObjectData toBudget = getBudget(budgetMap, tenantId, toBudgetId);
                        String sourceId = id + "to";
                        if (!isExist(tenantId, sourceId)) {
                            double beforeAmount = getValue(dataMap, TPMActivityBudgetAdjustFields.BALANCE_BEFORE_TRANSFER_IN, Double.class);
                            double afterAmount = getValue(dataMap, TPMActivityBudgetAdjustFields.BALANCE_AFTER_TRANSFER_IN, Double.class);
                            IObjectData detail = addBudgetDetail(tenantId, (String) dataMap.get("owner"), "2", toBudgetId,
                                    String.format("预算调整：「%s」调整", toBudget.get("name")), amount, beforeAmount, afterAmount,
                                    createTime, null, null, sourceId, id, createTime, createTime
                            );
                            updateCreateTime(tenantId, detail, createTime);
                        }
                    }

                });

                if (sleepTime != 0) {
                    try {
                        Thread.sleep(sleepTime);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
                if (isIncreaseMode) {
                    offset += limit;
                    querySql = String.format(baseSql, tenantId, limit, offset);
                }
            }
            offset = Integer.parseInt((String) arg.getParams().getOrDefault("offset", "0"));
        }

        return new TPMActivityScript.Result();
    }

    private IObjectData getBudget(Map<String, IObjectData> dataMap, String tenantId, String id) {
        if (!dataMap.containsKey(id)) {
            dataMap.put(id, serviceFacade.findObjectData(User.systemUser(tenantId), id, ApiNames.TPM_ACTIVITY_BUDGET));
        }
        return dataMap.get(id);

    }

    public IObjectData addBudgetDetail(String tenantId, String owner, String type, String budgetId, String remark, double amount, double beforeBalance, double afterBalance, long operateTime, String extraData, String activityId, String sourceId, String adjustId, long createTime, long modifyTime) {
        IObjectData objectData = new ObjectData();
        objectData.setRecordType("default__c");
        objectData.set(CommonFields.LOCK_STATUS, CommonFields.LOCK_STATUS__LOCK);
        objectData.setTenantId(tenantId);
        objectData.setOwner(Lists.newArrayList(owner));
        objectData.setDescribeApiName(ApiNames.TPM_ACTIVITY_BUDGET_DETAIL_OBJ);
        objectData.set(TPMActivityBudgetDetailFields.ACTIVITY_ID, activityId);
        objectData.set(TPMActivityBudgetDetailFields.TYPE, type);
        objectData.set(TPMActivityBudgetDetailFields.BUDGET_TABLE_ID, budgetId);
        objectData.set(TPMActivityBudgetDetailFields.REMARK, remark);
        objectData.set(TPMActivityBudgetDetailFields.AMOUNT_BEFORE_OPERATION, keepNDecimal(beforeBalance, 3));
        objectData.set(TPMActivityBudgetDetailFields.AMOUNT_AFTER_OPERATION, keepNDecimal(afterBalance, 3));
        objectData.set(TPMActivityBudgetDetailFields.OPERATE_TIME, operateTime);
        objectData.set(TPMActivityBudgetDetailFields.AMOUNT, keepNDecimal(amount, 3));
        objectData.set(TPMActivityBudgetDetailFields.SOURCE_ID, sourceId);
        objectData.set(TPMActivityBudgetDetailFields.EXTRA_DATA, extraData);
        objectData.set(TPMActivityBudgetDetailFields.BUDGET_ADJUST_ID, adjustId);
        objectData.setCreateTime(createTime);
        objectData.setLastModifiedTime(modifyTime);

        return serviceFacade.saveObjectData(User.systemUser(tenantId), objectData);
    }

    private double keepNDecimal(double value, int n) {
        long base = 10;
        if (n >= DECIMAL_BASE.length) {
            for (int i = 0; i < n; i++) base *= 10;
        } else {
            base = DECIMAL_BASE[n];
        }
        return Math.round(value * base) * 1.0 / base;
    }

    private boolean isExist(String tenantId, String sourceId) {

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setOffset(0);
        query.setLimit(1);
        query.setSearchSource("db");
        Filter sourceIdFilter = new Filter();
        sourceIdFilter.setOperator(Operator.EQ);
        sourceIdFilter.setFieldName(TPMActivityBudgetDetailFields.SOURCE_ID);
        sourceIdFilter.setFieldValues(Lists.newArrayList(sourceId));
        query.setFilters(Lists.newArrayList(sourceIdFilter));

        return serviceFacade.findBySearchQuery(User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_BUDGET_DETAIL_OBJ, query).getTotalNumber() > 0;
    }

    private <T> T getValue(Map dataMap, String key, Class<T> clazz) {
        Object value = dataMap.get(key);
        if (value instanceof BigDecimal) {
            BigDecimal bigDecimal = ((BigDecimal) value);
            if (clazz.equals(Double.class)) {
                return clazz.cast(bigDecimal.doubleValue());
            } else if (clazz.equals(Integer.class)) {
                return clazz.cast(bigDecimal.intValue());
            } else if (clazz.equals(Long.class)) {
                return clazz.cast(bigDecimal.longValue());
            } else {
                return clazz.cast(bigDecimal.doubleValue());
            }
        }
        return clazz.cast(value);
    }

    private void updateCreateTime(String tenantId, IObjectData data, long time) {

        String baseSql = "update fmcg_tpm_activity_budget_detail set create_time=%s,last_modified_time=%s where id = '%s' and tenant_id = '%s'";

        (specialTableMapper.setTenantId(tenantId)).batchUpdateBySql(String.format(baseSql, time, time, data.getId(), tenantId));
    }

    TPMActivityScript.Result openActivity(TPMActivityScript.Arg arg) {
        arg.getTenantIds().forEach(tenantId -> {
            List<String> activityIds = arg.getParams().getObject("activityIds", new TypeReference<List<String>>() {
            });
            activityIds.forEach(activityId -> budgetService.openActivity(tenantId, activityId));
        });
        return new TPMActivityScript.Result();
    }


    TPMActivityScript.Result adjustStatistics(TPMActivityScript.Arg arg) {
        arg.getTenantIds().forEach(tenantId -> {

            Map<String, List<Double>> budgetMap = new HashMap<>();
            SearchTemplateQuery query = new SearchTemplateQuery();
            query.setLimit(-1);
            query.setOffset(0);
            query.setSearchSource("db");

            Filter valueFilter = new Filter();
            valueFilter.setFieldValues(Lists.newArrayList("true"));
            valueFilter.setOperator(Operator.EQ);
            valueFilter.setFieldName("is_historical_order_data__c");
            query.setFilters(Lists.newArrayList(valueFilter));


            CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_BUDGET_ADJUST_OBJ, query, partialData -> partialData.forEach(data -> {
                String fromBudgetId = data.get(TPMActivityBudgetAdjustFields.FROM_BUDGET_TABLE_ID, String.class);
                if (!Strings.isNullOrEmpty(fromBudgetId)) {
                    List<Double> amountList = budgetMap.getOrDefault(fromBudgetId, Lists.newArrayList(0D, 0D));
                    amountList.set(0, amountList.get(0) - data.get(TPMActivityBudgetAdjustFields.AMOUNT, Double.class));
                    budgetMap.putIfAbsent(fromBudgetId, amountList);
                }
                String toBudgetId = data.get(TPMActivityBudgetAdjustFields.TO_BUDGET_TABLE_ID, String.class);
                if (!Strings.isNullOrEmpty(toBudgetId)) {
                    List<Double> amountList = budgetMap.getOrDefault(toBudgetId, Lists.newArrayList(0D, 0D));
                    amountList.set(1, amountList.get(1) + data.get(TPMActivityBudgetAdjustFields.AMOUNT, Double.class));
                    budgetMap.putIfAbsent(toBudgetId, amountList);
                }
            }));

            String selfApiName = (String) arg.getParams().getOrDefault("apiName", "historical_order_statistics__c");
            budgetMap.forEach((budgetId, valueList) -> {
                IObjectData budget = serviceFacade.findObjectData(User.systemUser(tenantId), budgetId, ApiNames.TPM_ACTIVITY_BUDGET);
                //部门预算
                if ("default__c".equals(budget.getRecordType())) {

                    ObjectData selfData = new ObjectData();
                    selfData.setOwner(Lists.newArrayList("-10000"));
                    selfData.setRecordType("default__c");
                    selfData.setTenantId(tenantId);
                    selfData.setDescribeApiName(selfApiName);
                    selfData.set("department__c", budget.get(TPMActivityBudgetFields.BUDGET_DEPARTMENT));
                    selfData.set("in__c", CommonUtils.keepNDecimal(valueList.get(1), 3));
                    selfData.set("out__c", CommonUtils.keepNDecimal(valueList.get(0), 3));
                    serviceFacade.saveObjectData(User.systemUser(tenantId), selfData);

                } else if ("record_IF13q__c".equals(budget.getRecordType())) {
                    ObjectData selfData = new ObjectData();
                    selfData.setOwner(Lists.newArrayList("-10000"));
                    selfData.setRecordType("default__c");
                    selfData.setTenantId(tenantId);
                    selfData.setDescribeApiName(selfApiName);
                    selfData.set("account__c", budget.get("field_ZD7ii__c"));
                    selfData.set("in__c", CommonUtils.keepNDecimal(valueList.get(1), 3));
                    selfData.set("out__c", CommonUtils.keepNDecimal(valueList.get(0), 3));
                    serviceFacade.saveObjectData(User.systemUser(tenantId), selfData);
                }
            });
        });


        return new TPMActivityScript.Result();
    }

    TPMActivityScript.Result invalidAdjustBySpecial(TPMActivityScript.Arg arg) {

        arg.getTenantIds().forEach(tenantId -> {

            SearchTemplateQuery query = new SearchTemplateQuery();
            query.setOffset(0);
            query.setLimit(100);
            query.setSearchSource("db");

            Filter valueFilter = new Filter();
            valueFilter.setOperator(Operator.EQ);
            valueFilter.setFieldName(arg.getParams().getOrDefault("filterKey", "is_historical_order_data__c").toString());
            valueFilter.setFieldValues(Lists.newArrayList(arg.getParams().getOrDefault("filterValue", "true").toString()));

            long beginTime = Long.parseLong(arg.getParams().getOrDefault("beginTime", "0").toString());
            long endTime = Long.parseLong(arg.getParams().getOrDefault("endTime", "0").toString());
            Filter timeFilter = new Filter();
            timeFilter.setOperator(Operator.BETWEEN);
            timeFilter.setFieldName(CommonFields.CREATE_TIME);
            timeFilter.setFieldValues(Lists.newArrayList(Long.toString(beginTime), Long.toString(endTime)));
            query.setFilters(Lists.newArrayList(valueFilter, timeFilter));

            List<IObjectData> dataList;
            while ((dataList = serviceFacade.findBySearchQuery(User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_BUDGET_ADJUST_OBJ, query).getData()).size() != 0) {
                log.info("left total:{}", dataList.size());
                budgetService.invalidAdjust(tenantId, dataList);
            }
        });
        return new TPMActivityScript.Result();
    }


    TPMActivityScript.Result resetBeforeAfterAmountForAdjust(TPMActivityScript.Arg arg) {

        arg.getTenantIds().forEach(tenantId -> {

            if (CollectionUtils.isEmpty(arg.getParams().getJSONArray("ids"))) {
                SearchTemplateQuery query = new SearchTemplateQuery();
                query.setOffset(0);
                query.setLimit(-1);
                query.setSearchSource("db");


                List<IObjectData> dataList = CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_BUDGET, query);
                dataList.forEach(v -> budgetService.resetDetailOperationAmount(tenantId, v.getId()));
                dataList.forEach(v -> budgetService.calculateBudget(tenantId, v.getId()));

            } else {
                List<String> ids = arg.getParams().getObject("ids", new TypeReference<List<String>>() {
                });
                ids.forEach(id -> budgetService.resetDetailOperationAmount(tenantId, id));
                ids.forEach(id -> budgetService.calculateBudget(tenantId, id));
            }

        });
        return new TPMActivityScript.Result();
    }

    TPMActivityScript.Result reCalculateBudget(TPMActivityScript.Arg arg) {

        arg.getTenantIds().forEach(tenantId -> {

            if (CollectionUtils.isEmpty(arg.getParams().getJSONArray("ids"))) {
                SearchTemplateQuery query = new SearchTemplateQuery();
                query.setOffset(0);
                query.setLimit(-1);
                query.setSearchSource("db");

                List<IObjectData> dataList = CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_BUDGET, query);

                dataList.forEach(v -> {
                    String uuid = UUID.randomUUID().toString();
                    try {
                        budgetService.tryLockBudget(tenantId, v.getId(), uuid);
                        budgetService.calculateBudget(tenantId, v.getId());
                    } finally {
                        budgetService.unLockBudget(tenantId, v.getId(), uuid);
                    }
                });

            } else {
                List<String> ids = arg.getParams().getObject("ids", new TypeReference<List<String>>() {
                });
                ids.forEach(id -> {
                    String uuid = UUID.randomUUID().toString();
                    try {
                        budgetService.tryLockBudget(tenantId, id, uuid);
                        budgetService.calculateBudget(tenantId, id);
                    } finally {
                        budgetService.unLockBudget(tenantId, id, uuid);
                    }

                });
            }
        });
        return new TPMActivityScript.Result();
    }

    TPMActivityScript.Result invalidBudget(TPMActivityScript.Arg arg) {
        arg.getTenantIds().forEach(tenantId -> {
            List<String> ids = arg.getParams().getObject("ids", new TypeReference<List<String>>() {
            });
            List<IObjectData> budgets = serviceFacade.findObjectDataByIds(tenantId,ids,ApiNames.TPM_ACTIVITY_BUDGET);
            budgetService.invalidBudget(tenantId,budgets);
        });

        return new TPMActivityScript.Result();
    }
}

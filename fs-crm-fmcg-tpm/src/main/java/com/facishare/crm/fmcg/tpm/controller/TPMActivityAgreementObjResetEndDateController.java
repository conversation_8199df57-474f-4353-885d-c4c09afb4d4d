package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.apiname.CommonFields;
import com.facishare.crm.fmcg.tpm.apiname.TPMActivityAgreementFields;
import com.facishare.crm.fmcg.tpm.apiname.TPMActivityProofFields;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import de.lab4inf.math.util.Strings;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/1/7 14:16
 */
@SuppressWarnings("Duplicates")
@Slf4j
public class TPMActivityAgreementObjResetEndDateController extends PreDefineController<TPMActivityAgreementObjResetEndDateController.Arg, TPMActivityAgreementObjResetEndDateController.Result> {

    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");

    @Override
    protected Result doService(Arg arg) {

        try {
            log.info("agreement reset end date arg : {}", arg);

            if (Strings.isNullOrEmpty(arg.getObjectDataId())) {
                throw new ValidateException("参数[object_data_id]不能为空。");
            }

            IObjectData data = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()), arg.getObjectDataId(), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);
            if (Objects.isNull(data)) {
                throw new ValidateException("协议数据已作废或已删除。");
            }

            String oldStatus = data.get(TPMActivityAgreementFields.AGREEMENT_STATUS, String.class);
            if (TPMActivityAgreementFields.AGREEMENT_STATUS__INVALID.equals(oldStatus)) {
                throw new ValidateException("已终止协议不允许重置结束时间。");
            }

            long begin = data.get(TPMActivityAgreementFields.BEGIN_DATE, Long.class);
            long end = arg.getEndDate();

            if (end <= begin) {
                throw new ValidateException("协议结束时间不能早于协议开始时间。");
            }

            IObjectData lastProof = findLastProofOfThisAgreement(arg.getObjectDataId());
            if (!Objects.isNull(lastProof)) {
                long lastProofCreateTime = lastProof.getCreateTime();
                if (end < lastProofCreateTime) {
                    throw new ValidateException(String.format("协议结束时间不能早于最后一条举证[%s]的创建时间[%s]。",
                            lastProof.getName(),
                            dateFormat.format(new Date(lastProofCreateTime))));
                }
            }

            long now = System.currentTimeMillis();
            String status;
            if (now < begin) {
                status = TPMActivityAgreementFields.AGREEMENT_STATUS__SCHEDULE;
            } else if (now < end) {
                status = TPMActivityAgreementFields.AGREEMENT_STATUS__IN_PROGRESS;
            } else {
                status = TPMActivityAgreementFields.AGREEMENT_STATUS__END;
            }

            data.set(TPMActivityAgreementFields.END_DATE, end);
            data.set(TPMActivityAgreementFields.AGREEMENT_STATUS, status);

            data = serviceFacade.updateObjectData(controllerContext.getUser(), data, true);

            IObjectDescribe describe = serviceFacade.findObject(controllerContext.getTenantId(), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);
            serviceFacade.logWithCustomMessage(controllerContext.getUser(),
                    EventType.MODIFY,
                    ActionType.MODIFY,
                    describe,
                    data,
                    "修改结束时间");

            return Result.builder()
                    .success(true)
                    .errorMessage("")
                    .data(ObjectDataDocument.of(data)).build();
        } catch (Exception ex) {
            return TPMActivityAgreementObjResetEndDateController.Result.builder()
                    .success(false)
                    .errorMessage(ex.getMessage())
                    .data(null).build();
        }
    }

    private IObjectData findLastProofOfThisAgreement(String objectDataId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter agreementIdFilter = new Filter();
        agreementIdFilter.setFieldName(TPMActivityProofFields.ACTIVITY_AGREEMENT_ID);
        agreementIdFilter.setOperator(Operator.EQ);
        agreementIdFilter.setFieldValues(Lists.newArrayList(objectDataId));
        query.setFilters(Lists.newArrayList(agreementIdFilter));

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.CREATE_TIME);
        order.setIsAsc(false);
        query.setOrders(Lists.newArrayList(order));

        List<IObjectData> data = serviceFacade.findBySearchQuery(User.systemUser(controllerContext.getTenantId()), ApiNames.TPM_ACTIVITY_PROOF_OBJ, query).getData();

        if (CollectionUtils.isEmpty(data)) {
            return null;
        } else {
            return data.get(0);
        }
    }

    @Data
    @ToString
    static class Arg implements Serializable {

        @SerializedName("object_data_id")
        @JSONField(name = "object_data_id")
        @JsonProperty("object_data_id")
        private String objectDataId;

        @SerializedName("end_date")
        @JSONField(name = "end_date")
        @JsonProperty("end_date")
        private long endDate;
    }

    @Data
    @ToString
    @Builder
    static class Result implements Serializable {

        private ObjectDataDocument data;

        @SerializedName("success")
        @JSONField(name = "success")
        @JsonProperty("success")
        private boolean success;

        @SerializedName("error_message")
        @JSONField(name = "error_message")
        @JsonProperty("error_message")
        private String errorMessage;
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList();
    }
}

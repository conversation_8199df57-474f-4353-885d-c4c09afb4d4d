package com.facishare.crm.fmcg.tpm.api.proof;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public interface BatchEnable {

    @Data
    @ToString
    class Arg implements Serializable {

        @SerializedName("store_ids")
        @JSONField(name = "store_ids")
        @JsonProperty("store_ids")
        private List<String> storeIds;
    }

    @Data
    @ToString
    class Result implements Serializable {

        @SerializedName("store_id_enable")
        @JSONField(name = "store_id_enable")
        @JsonProperty("store_id_enable")
        private Map<String, Boolean> storeIdEnable;
    }
}
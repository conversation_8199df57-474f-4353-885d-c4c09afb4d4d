package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.crm.fmcg.tpm.api.proof.AuditPreAdd;
import com.facishare.crm.fmcg.tpm.apiname.*;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 12/2/20 4:12 PM
 */
@SuppressWarnings("Duplicates")
public class TPMActivityProofAuditObjPreAddController extends PreDefineController<AuditPreAdd.Arg, AuditPreAdd.Result> {

    @Override
    protected AuditPreAdd.Result doService(AuditPreAdd.Arg arg) {
        if (!serviceFacade.funPrivilegeCheck(controllerContext.getUser(), ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ, "Add")) {
            throw new ValidateException(I18N.text(I18NKeys.DO_NOT_HAVE_PROOF_AUDIT_CREATE_RIGHT));
        }

        if (Strings.isNullOrEmpty(arg.getVisitId()) || Strings.isNullOrEmpty(arg.getActionId())) {
            throw new ValidateException("visit_id or action_id can not be empty.");
        }

        IObjectData storeData = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()), arg.getStoreId(), ApiNames.ACCOUNT_OBJ);
        if (storeData == null) {
            throw new ValidateException("store not found.");
        }

        IObjectData proofData = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()), arg.getProofId(), ApiNames.TPM_ACTIVITY_PROOF_OBJ);
        if (proofData == null) {
            throw new ValidateException("proof not found.");
        }

        String activityId = (String) proofData.get(TPMActivityProofFields.ACTIVITY_ID);
        IObjectData activityData = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()), activityId, ApiNames.TPM_ACTIVITY_OBJ);
        if (activityData == null) {
            throw new ValidateException("activity not found.");
        }
        boolean agreementRequired = Boolean.TRUE.equals(activityData.get(TPMActivityFields.IS_AGREEMENT_REQUIRED));

        AuditPreAdd.ProofAuditDataVO proof = new AuditPreAdd.ProofAuditDataVO();

        proof.setDealerActivityId((String) proofData.get(TPMActivityProofFields.DEALER_ACTIVITY));
        IObjectData dealerActivityData = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()), proof.getDealerActivityId(), ApiNames.TPM_DEALER_ACTIVITY_OBJ);
        if (dealerActivityData != null) {
            proof.setDealerActivityName(dealerActivityData.getName());
        }

        proof.setActivityProofId(proofData.getId());
        proof.setActivityProofName(proofData.getName());
        proof.setProofImages(proofData.get(TPMActivityProofAuditFields.PROOF_IMAGES));

        proof.setVisitId(arg.getVisitId());
        proof.setActionId(arg.getActionId());

        proof.setStoreId((String) proofData.get(TPMActivityProofFields.STORE_ID));
        proof.setStoreName(storeData.getName());

        if (agreementRequired) {
            proof.setActivityAgreementId((String) proofData.get(TPMActivityProofFields.ACTIVITY_AGREEMENT_ID));
            if (!Strings.isNullOrEmpty(proof.getActivityAgreementId())) {
                IObjectData agreementData = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()), proof.getActivityAgreementId(), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);
                if (agreementData != null) {
                    proof.setActivityAgreementName(agreementData.getName());
                }
            }
        }

        proof.setDealerId((String) proofData.get(TPMActivityProofFields.DEALER_ID));
        IObjectData dealerData = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()), proof.getDealerId(), ApiNames.ACCOUNT_OBJ);
        if (dealerData != null) {
            proof.setDealerName(dealerData.getName());
        }

        proof.setActivityId(activityId);
        proof.setActivityName(activityData.getName());

        Double actualTotal = proofData.get(TPMActivityProofFields.ACTUAL_TOTAL, Double.class);
        if (actualTotal == null) {
            actualTotal = proofData.get(TPMActivityProofFields.TOTAL, Double.class);
        }

        proof.setTotal(actualTotal);
        proof.setOwner(Lists.newArrayList(controllerContext.getUser().getUserId()));

        AuditPreAdd.Result preAddResult = new AuditPreAdd.Result();

        preAddResult.setObjectData(proof);
        preAddResult.setDetails(new HashMap<>());

        List<IObjectData> activityProofDetails = queryActivityProofDetails(controllerContext, proofData.getId());
        Map<String, IObjectData> activityDetailMap = queryActivityDetail(controllerContext, activityData.getId()).stream().collect(Collectors.toMap(DBRecord::getId, v -> v));

        List<AuditPreAdd.ProofAuditDetailDataVO> details = Lists.newArrayList();

        for (IObjectData activityProofDetail : activityProofDetails) {
            AuditPreAdd.ProofAuditDetailDataVO detail = new AuditPreAdd.ProofAuditDetailDataVO();

            if (agreementRequired) {
                detail.setActivityAgreementDetailId((String) activityProofDetail.get(TPMActivityProofDetailFields.ACTIVITY_AGREEMENT_DETAIL_ID));
                detail.setAgreementAmountStandard(Double.parseDouble(activityProofDetail.get(TPMActivityProofDetailFields.PROOF_DETAIL_AMOUNT_STANDARD).toString()));
            }

            detail.setActivityItemId((String) activityProofDetail.get(TPMActivityProofDetailFields.ACTIVITY_ITEM_ID));

            IObjectData activityItem = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()), detail.getActivityItemId(), ApiNames.TPM_ACTIVITY_ITEM_OBJ);

            detail.setActivityDetailId((String) activityProofDetail.get(TPMActivityProofDetailFields.ACTIVITY_DETAIL_ID));
            detail.setActivityDetailName(activityDetailMap.containsKey(detail.getActivityDetailId()) ? activityDetailMap.get(detail.getActivityDetailId()).getName() : "--");
            detail.setActivityProofDetailId(activityProofDetail.getId());
            detail.setType((String) activityProofDetail.get(TPMActivityProofDetailFields.TYPE));
            detail.setAmountStandard(Double.parseDouble(activityProofDetail.get(TPMActivityProofDetailFields.PROOF_DETAIL_AMOUNT_STANDARD).toString()));
            detail.setActivityCostStandard(Double.parseDouble(activityProofDetail.get(TPMActivityProofDetailFields.PROOF_DETAIL_COST_STANDARD).toString()));
            detail.setAmount(Double.parseDouble(activityProofDetail.get(TPMActivityProofDetailFields.AMOUNT).toString()));
            detail.setSubtotal(Double.parseDouble(activityProofDetail.get(TPMActivityProofDetailFields.SUBTOTAL).toString()));

            detail.setProofDetailAmountStandard(Double.parseDouble(activityProofDetail.get(TPMActivityProofDetailFields.PROOF_DETAIL_AMOUNT_STANDARD).toString()));
            detail.setProofDetailCostStandard(Double.parseDouble(activityProofDetail.get(TPMActivityProofDetailFields.PROOF_DETAIL_COST_STANDARD).toString()));

            detail.setCalculatePattern((String) activityItem.get(TPMActivityProofDetailFields.CALCULATE_PATTERN));
            detail.setAmountStandardCheck((Boolean) activityItem.get(TPMActivityProofDetailFields.AMOUNT_STANDARD_CHECK));

            details.add(detail);
        }

        preAddResult.getDetails().put("TPMActivityProofAuditDetailObj", details);
        return preAddResult;
    }

    private List<IObjectData> queryActivityProofDetails(ControllerContext context, String proofId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter masterFilter = new Filter();
        masterFilter.setFieldName(TPMActivityProofDetailFields.ACTIVITY_PROOF_ID);
        masterFilter.setOperator(Operator.EQ);
        masterFilter.setFieldValues(Lists.newArrayList(proofId));

        query.setFilters(Lists.newArrayList(masterFilter));
        return CommonUtils.queryData(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ, query);
    }

    private List<IObjectData> queryActivityDetail(ControllerContext context, String activityId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter masterFilter = new Filter();
        masterFilter.setFieldName(TPMActivityDetailFields.ACTIVITY_ID);
        masterFilter.setOperator(Operator.EQ);
        masterFilter.setFieldValues(Lists.newArrayList(activityId));

        query.setFilters(Lists.newArrayList(masterFilter));
        return CommonUtils.queryData(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_DETAIL_OBJ, query);
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Collections.emptyList();
    }
}

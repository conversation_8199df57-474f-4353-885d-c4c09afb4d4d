package com.facishare.crm.fmcg.tpm.mq;

import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.facishare.crm.fmcg.tpm.api.enumeration.LogType;
import com.facishare.crm.fmcg.tpm.api.log.LogData;
import com.facishare.crm.fmcg.tpm.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.apiname.TPMActivityBudgetAdjustFields;
import com.facishare.crm.fmcg.tpm.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.tpm.apiname.TPMDealerActivityCostFields;
import com.facishare.crm.fmcg.tpm.business.BudgetService;
import com.facishare.crm.fmcg.tpm.business.OperateInfoService;
import com.facishare.crm.fmcg.tpm.mq.model.ApprovalEventOBJ;
import com.facishare.paas.appframework.core.exception.ObjectDataNotFoundException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.List;
import java.util.UUID;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2020/8/25 5:46 PM
 */
@Slf4j
@SuppressWarnings("Duplicates")
@Component
public class ApprovalEventConsumer implements ApplicationListener<ContextRefreshedEvent> {

    private AutoConfMQPushConsumer processor;

    @Resource
    private BudgetService budgetService;

    @Resource
    private ServiceFacade serviceFacade;

    private static final List<String> TPM_API_NAMES = Lists.newArrayList(ApiNames.TPM_ACTIVITY_OBJ, ApiNames.TPM_ACTIVITY_BUDGET_ADJUST_OBJ);
    private final OperateInfoService operateInfoService = SpringUtil.getContext().getBean(OperateInfoService.class);

    private static final String CONFIG_NAME = "fs-fmcg-framework-config";
    private static final String NAME_SERVER_KEY = "CRM_FMCG_APPROVAL_EVENT_NAMESERVER_NEW";
    private static final String TOPIC_KEY = "CRM_FMCG_APPROVAL_EVENT_TOPICS";
    private static final String GROUP_NAME = "CRM_FMCG_APPROVAL_EVENT_GROUP";

    @PostConstruct
    public void init() {
        log.info("ApprovalEventConsumer start init.");

        MessageListenerConcurrently listener = (messages, context) -> {
            for (MessageExt messageExt : messages) {
                try {
                    processMessage(messageExt);
                } catch (Exception ex) {
                    log.error("ApprovalEventConsumer consumer error.", ex);
                    return ConsumeConcurrentlyStatus.RECONSUME_LATER;
                }
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        };
        try {
            processor = new AutoConfMQPushConsumer(CONFIG_NAME, listener);
            processor.setGroupNameKey(GROUP_NAME);
            processor.setConsumeTopicKey(TOPIC_KEY);
            processor.setNameServerKey(NAME_SERVER_KEY);
        } catch (Exception e) {
            log.error("init notice consumer failed.", e);
        }
    }

    public void processMessage(MessageExt messageExt) {
        String dataJsonStr = new String(messageExt.getBody());
        ApprovalEventOBJ message = JSON.parseObject(dataJsonStr, ApprovalEventOBJ.class);
        try {
            if (ApiNames.TPM_ACTIVITY_OBJ.equals(message.getTag())) {
                dealTPMActivity(message);
            } else if (ApiNames.TPM_DEALER_ACTIVITY_COST.equals(message.getTag())) {
                dealTPMActivityCost(message);
            }

            if (ApiNames.TPM_ACTIVITY_BUDGET_ADJUST_OBJ.equals(message.getTag())) {
                dealTPMActivityAdjust(message);
            }
        } catch (Exception e) {
            if (messageExt.getReconsumeTimes() > 5 && !Strings.isNullOrEmpty(message.getTag()) && TPM_API_NAMES.contains(message.getTag())) {
                LogData logData = LogData.builder().data(JSON.toJSONString(message)).build();
                operateInfoService.log(message.getEventData().getString("tenantId"), LogType.APPROVAL_ERROR.value(), JSON.toJSONString(logData), null, message.getEventData().getString("entityId"), null, true);
            }
            throw e;
        }
    }

    private void dealTPMActivity(ApprovalEventOBJ message) {
        String eventType = message.getEventType();
        log.info("approval message:{}", message);

        if ("instance_complete".equals(eventType)) {
            String apiName = message.getEventData().getString("entityId");
            String dataId = message.getEventData().getString("dataId");
            String tenantId = message.getEventData().getString("tenantId");
            String approvalId = message.getEventData().getString("instanceId");
            String status = message.getEventData().getString("status");
            String lockVal = UUID.randomUUID().toString();
            String budgetId = "";
            try {
                //如果开通了tpm
                if (budgetService.isOpenBudge(Integer.parseInt(tenantId))) {
                    IObjectData activity = serviceFacade.findObjectData(User.systemUser(tenantId), dataId, apiName);
                    budgetId = activity.get(TPMActivityFields.BUDGET_TABLE, String.class, "");
                    if (Strings.isNullOrEmpty(budgetId)) {
                        return;
                    }
                    budgetService.tryLockBudget(tenantId, budgetId, lockVal);
                    switch (status) {
                        case "pass": {
                            budgetService.dealPassActivity(status, tenantId, approvalId, message, activity);
                            budgetService.calculateBudget(tenantId, budgetId);
                            break;
                        }
                        case "reject":
                        case "cancel": {
                            budgetService.dealRefund(status, tenantId, approvalId, message, activity);
                            budgetService.calculateBudget(tenantId, budgetId);
                            break;
                        }
                        default:
                    }
                }
            } finally {
                budgetService.unLockBudget(tenantId, budgetId, lockVal);
            }
        }
    }


    private void dealTPMActivityAdjust(ApprovalEventOBJ message) {
        String eventType = message.getEventType();
        log.info("approval message:{}", message);

        if ("instance_complete".equals(eventType)) {
            String apiName = message.getEventData().getString("entityId");
            String dataId = message.getEventData().getString("dataId");
            String tenantId = message.getEventData().getString("tenantId");
            String approvalId = message.getEventData().getString("instanceId");
            String status = message.getEventData().getString("status");
            String lockVal = UUID.randomUUID().toString();
            String budgetId = "";
            try {
                //如果开通了tpm
                if (budgetService.isOpenBudge(Integer.parseInt(tenantId))) {
                    IObjectData budgetAdjust = serviceFacade.findObjectData(User.systemUser(tenantId), dataId, apiName);
                    budgetId = budgetAdjust.get(TPMActivityBudgetAdjustFields.FROM_BUDGET_TABLE_ID, String.class, "");

                    budgetService.tryLockBudget(tenantId, budgetId, lockVal);
                    switch (status) {
                        case "pass": {
                            budgetService.dealPassRefundForBudgetAdjust(tenantId, approvalId, message, budgetAdjust);
                            break;
                        }
                        case "reject":
                        case "cancel": {
                            budgetService.dealRejectRefundForBudgetAdjust(status, tenantId, approvalId, message, budgetAdjust);
                            break;
                        }
                        default:
                    }
                    budgetService.calculateBudget(tenantId, budgetId);
                }
            } finally {
                budgetService.unLockBudget(tenantId, budgetId, lockVal);
            }
        }
    }

    private void dealTPMActivityCost(ApprovalEventOBJ message) {
        String eventType = message.getEventType();
        log.info("approval message:{}", message);
        if ("instance_complete".equals(eventType)) {

            String apiName = message.getEventData().getString("entityId");
            String dataId = message.getEventData().getString("dataId");
            String tenantId = message.getEventData().getString("tenantId");
            String lockVal = UUID.randomUUID().toString();
            String budgetId = "";
            //如果开通了tpm
            IObjectData cost;
            try {
                cost = serviceFacade.findObjectData(User.systemUser(tenantId), dataId, apiName);
            } catch (MetaDataBusinessException | ObjectDataNotFoundException e) {
                log.info("data not fund:{}", dataId);
                return;
            }
            IObjectData activity = serviceFacade.findObjectData(User.systemUser(tenantId), (String) cost.get(TPMDealerActivityCostFields.ACTIVITY_ID), ApiNames.TPM_ACTIVITY_OBJ);
            if (budgetService.isOpenBudge(Integer.parseInt(tenantId))) {
                try {
                    budgetId = activity.get(TPMActivityFields.BUDGET_TABLE, String.class, "");
                    budgetService.tryLockBudget(tenantId, budgetId, lockVal);
                    budgetService.dealPassCost(tenantId, activity.getId(), budgetId, message);
                } finally {
                    budgetService.unLockBudget(tenantId, budgetId, lockVal);
                }
            } else {
                budgetService.calculateActivity(tenantId, activity.getId());
            }
        }
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (processor != null && event.getApplicationContext().getParent() == null) {
            processor.start();
            log.info("object data mq v2 consumer started.");
        }
    }


    @PreDestroy
    public void shutDown() {
        processor.close();
    }
}

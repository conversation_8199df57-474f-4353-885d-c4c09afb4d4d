package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.apiname.*;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import de.lab4inf.math.util.Strings;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/1/7 14:16
 */
@SuppressWarnings("Duplicates")
@Slf4j
public class TPMActivityAgreementObjAbandonController extends PreDefineController<TPMActivityAgreementObjAbandonController.Arg, TPMActivityAgreementObjAbandonController.Result> {

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList();
    }

    public static final String IS_ABANDONED_FIELD_KEY = "is_abandoned__c";
    public static final String ABANDONED_TIME_FIELD_KEY = "abandoned_time__c";

    @Override
    protected TPMActivityAgreementObjAbandonController.Result doService(TPMActivityAgreementObjAbandonController.Arg arg) {
        try {
            log.info("agreement abandon arg : {}", arg);

            if (Strings.isNullOrEmpty(arg.getObjectDataId())) {
                throw new ValidateException("参数[object_data_id]不能为空。");
            }

            IObjectData data = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()), arg.getObjectDataId(), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);
            if (Objects.isNull(data)) {
                throw new ValidateException("协议数据已作废或已删除。");
            }

            data.set(TPMActivityAgreementFields.AGREEMENT_STATUS, TPMActivityAgreementFields.AGREEMENT_STATUS__INVALID);
            data.set(IS_ABANDONED_FIELD_KEY, true);
            data.set(ABANDONED_TIME_FIELD_KEY, System.currentTimeMillis());

            abandonRelatedProof(arg.getObjectDataId());
            abandonRelatedAudit(arg.getObjectDataId());
            abandonRelatedSummary(arg.getObjectDataId());

            IObjectDescribe describe = serviceFacade.findObject(controllerContext.getTenantId(), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);

            data = serviceFacade.updateObjectData(controllerContext.getUser(), data, true);
            serviceFacade.logWithCustomMessage(controllerContext.getUser(),
                    EventType.MODIFY,
                    ActionType.MODIFY,
                    describe,
                    data,
                    "终止协议");

            return Result.builder()
                    .success(true)
                    .errorMessage("")
                    .data(ObjectDataDocument.of(data)).build();
        } catch (Exception ex) {
            return TPMActivityAgreementObjAbandonController.Result.builder()
                    .success(false)
                    .errorMessage(ex.getMessage())
                    .data(null).build();
        }
    }

    private void abandonRelatedSummary(String objectDataId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(200);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter agreementIdFilter = new Filter();
        agreementIdFilter.setFieldName("activity_agreement_id__c");
        agreementIdFilter.setOperator(Operator.EQ);
        agreementIdFilter.setFieldValues(Lists.newArrayList(objectDataId));

        Filter approvalStatusFilter = new Filter();
        approvalStatusFilter.setFieldName("approval_status__c");
        approvalStatusFilter.setOperator(Operator.N);
        approvalStatusFilter.setFieldValues(Lists.newArrayList("pass"));

        Filter isReceiptSentFilter = new Filter();
        isReceiptSentFilter.setFieldName("is_receipt_sent__c");
        isReceiptSentFilter.setOperator(Operator.N);
        isReceiptSentFilter.setFieldValues(Lists.newArrayList("true"));

        query.setFilters(Lists.newArrayList(agreementIdFilter, approvalStatusFilter, isReceiptSentFilter));

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.CREATE_TIME);
        order.setIsAsc(false);
        query.setOrders(Lists.newArrayList(order));

        List<IObjectData> data = serviceFacade.findBySearchQuery(User.systemUser(controllerContext.getTenantId()), "agreement_proof_summary__c", query).getData();

        log.info("1.abandon agreement found {} proof summaries : {}", data.size(), JSON.toJSONString(data));

        if (CollectionUtils.isEmpty(data)) {
            return;
        }

        serviceFacade.bulkInvalid(data, User.systemUser(controllerContext.getTenantId()));
    }

    private void abandonRelatedAudit(String objectDataId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(200);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter agreementIdFilter = new Filter();
        agreementIdFilter.setFieldName(TPMActivityProofFields.ACTIVITY_AGREEMENT_ID);
        agreementIdFilter.setOperator(Operator.EQ);
        agreementIdFilter.setFieldValues(Lists.newArrayList(objectDataId));
        query.setFilters(Lists.newArrayList(agreementIdFilter));

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.CREATE_TIME);
        order.setIsAsc(false);
        query.setOrders(Lists.newArrayList(order));

        List<IObjectData> data = serviceFacade.findBySearchQuery(User.systemUser(controllerContext.getTenantId()), ApiNames.TPM_ACTIVITY_PROOF_OBJ, query).getData();

        List<String> fields = Lists.newArrayList(IS_ABANDONED_FIELD_KEY);
        List<List<IObjectData>> dataArray = Lists.partition(data, 50);

        for (List<IObjectData> dataDatum : dataArray) {
            for (IObjectData datum : dataDatum) {
                datum.set(IS_ABANDONED_FIELD_KEY, true);
            }
            serviceFacade.batchUpdateByFields(User.systemUser(controllerContext.getTenantId()), dataDatum, fields);
        }
    }

    private void abandonRelatedProof(String objectDataId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(200);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter agreementIdFilter = new Filter();
        agreementIdFilter.setFieldName(TPMActivityProofAuditFields.ACTIVITY_AGREEMENT_ID);
        agreementIdFilter.setOperator(Operator.EQ);
        agreementIdFilter.setFieldValues(Lists.newArrayList(objectDataId));
        query.setFilters(Lists.newArrayList(agreementIdFilter));

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.CREATE_TIME);
        order.setIsAsc(false);
        query.setOrders(Lists.newArrayList(order));

        List<IObjectData> data = serviceFacade.findBySearchQuery(User.systemUser(controllerContext.getTenantId()), ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ, query).getData();

        List<String> fields = Lists.newArrayList(IS_ABANDONED_FIELD_KEY);
        List<List<IObjectData>> dataArray = Lists.partition(data, 50);

        for (List<IObjectData> dataDatum : dataArray) {
            for (IObjectData datum : dataDatum) {
                datum.set(IS_ABANDONED_FIELD_KEY, true);
            }
            serviceFacade.batchUpdateByFields(User.systemUser(controllerContext.getTenantId()), dataDatum, fields);
        }
    }

    @Data
    @ToString
    static class Arg implements Serializable {

        @SerializedName("object_data_id")
        @JSONField(name = "object_data_id")
        @JsonProperty("object_data_id")
        private String objectDataId;

    }

    @Data
    @ToString
    @Builder
    static class Result implements Serializable {

        private ObjectDataDocument data;

        @SerializedName("success")
        @JSONField(name = "success")
        @JsonProperty("success")
        private boolean success;

        @SerializedName("error_message")
        @JSONField(name = "error_message")
        @JsonProperty("error_message")
        private String errorMessage;
    }
}

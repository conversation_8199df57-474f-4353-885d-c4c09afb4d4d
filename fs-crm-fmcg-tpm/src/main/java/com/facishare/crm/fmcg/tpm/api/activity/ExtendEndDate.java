package com.facishare.crm.fmcg.tpm.api.activity;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/3/16 2:36 PM
 */
public interface ExtendEndDate {

    @Data
    @ToString
    class Arg implements Serializable {

        @SerializedName("object_data_id")
        @JSONField(name = "object_data_id")
        @JsonProperty("object_data_id")
        private String objectDataId;

        @SerializedName("end_date")
        @J<PERSON><PERSON>ield(name = "end_date")
        @JsonProperty("end_date")
        private long endDate;
    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        @SerializedName("object_data")
        @JSONField(name = "object_data")
        @JsonProperty("object_data")
        private ObjectDataDocument objectData;
    }
}

package com.facishare.crm.fmcg.tpm.service;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.lang.reflect.UndeclaredThrowableException;

/**
 * <AUTHOR>
 * @date 2021/6/30 下午2:25
 */
@Slf4j
@Transactional
@Component
public class PackTransactionProxyImpl implements PackTransactionProxy {

    @Resource
    private ServiceFacade serviceFacade;

    @Override
    public Object pack(ControllerContext actionContext, Object arg) {

        return null;
    }

    @Override
    public <A, R> void packAfter(TransactionService<A, R> transactionService, A arg, R result) {
        transactionService.doAfterTransaction(arg, result);
    }

    @Override
    public <A, R> void packBefore(TransactionService<A, R> transactionService, A arg) {
        transactionService.doBeforeTransaction(arg);
    }

    @Override
    public <A, R> R packAct(TransactionService<A, R> transactionService, A arg) {
        try {
            return transactionService.doActTransaction(arg);
        } catch (UndeclaredThrowableException e) {
            log.error("发生未知异常:", e);
            log.info("appear unknown err:{}", e);
            throw new ValidateException("未知异常，请稍后再试。");
        }
    }
}

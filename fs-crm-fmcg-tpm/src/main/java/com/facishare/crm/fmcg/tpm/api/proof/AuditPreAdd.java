package com.facishare.crm.fmcg.tpm.api.proof;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 12/3/20 7:58 PM
 */
public interface AuditPreAdd {

    @Data
    @ToString
    class Arg {

        @SerializedName("visit_id")
        @JSONField(name = "visit_id")
        @JsonProperty("visit_id")
        private String visitId;

        @SerializedName("action_id")
        @JSONField(name = "action_id")
        @JsonProperty("action_id")
        private String actionId;

        @SerializedName("store_id")
        @JSONField(name = "store_id")
        @JsonProperty("store_id")
        private String storeId;

        @SerializedName("proof_id")
        @JSONField(name = "proof_id")
        @JsonProperty("proof_id")
        private String proofId;
    }

    @Data
    @ToString
    class Result {

        @SerializedName("object_data")
        @JSONField(name = "object_data")
        @JsonProperty("object_data")
        private ProofAuditDataVO objectData;

        @SerializedName("details")
        @JSONField(name = "details")
        @JsonProperty("details")
        private Map<String, List<ProofAuditDetailDataVO>> details;
    }

    @Data
    @ToString
    class ProofAuditDetailDataVO implements Serializable {

        @SerializedName("activity_agreement_detail_id")
        @JSONField(name = "activity_agreement_detail_id")
        @JsonProperty("activity_agreement_detail_id")
        private String activityAgreementDetailId;

        @SerializedName("activity_item_id")
        @JSONField(name = "activity_item_id")
        @JsonProperty("activity_item_id")
        private String activityItemId;

        @SerializedName("activity_detail_id")
        @JSONField(name = "activity_detail_id")
        @JsonProperty("activity_detail_id")
        private String activityDetailId;

        @SerializedName("activity_detail_id__r")
        @JSONField(name = "activity_detail_id__r")
        @JsonProperty("activity_detail_id__r")
        private String activityDetailName;

        @SerializedName("amount_standard")
        @JSONField(name = "amount_standard")
        @JsonProperty("amount_standard")
        private Double amountStandard;

        @SerializedName("agreement_amount_standard")
        @JSONField(name = "agreement_amount_standard")
        @JsonProperty("agreement_amount_standard")
        private Double agreementAmountStandard;

        private String type;

        @SerializedName("activity_cost_standard")
        @JSONField(name = "activity_cost_standard")
        @JsonProperty("activity_cost_standard")
        private Double activityCostStandard;

        private Double amount;

        @SerializedName("activity_proof_detail_id")
        @JSONField(name = "activity_proof_detail_id")
        @JsonProperty("activity_proof_detail_id")
        private String activityProofDetailId;

        private Double subtotal;

        @SerializedName("proof_detail_amount_standard")
        @JSONField(name = "proof_detail_amount_standard")
        @JsonProperty("proof_detail_amount_standard")
        private Double proofDetailAmountStandard;

        @SerializedName("proof_detail_cost_standard")
        @JSONField(name = "proof_detail_cost_standard")
        @JsonProperty("proof_detail_cost_standard")
        private Double proofDetailCostStandard;

        @SerializedName("calculate_pattern")
        @JSONField(name = "calculate_pattern")
        @JsonProperty("calculate_pattern")
        private String calculatePattern;

        @SerializedName("amount_standard_check")
        @JSONField(name = "amount_standard_check")
        @JsonProperty("amount_standard_check")
        private Boolean amountStandardCheck;
    }

    @Data
    @ToString
    class ProofAuditDataVO implements Serializable {

        @SerializedName("store_id")
        @JSONField(name = "store_id")
        @JsonProperty("store_id")
        private String storeId;

        @SerializedName("store_id__r")
        @JSONField(name = "store_id__r")
        @JsonProperty("store_id__r")
        private String storeName;

        @SerializedName("dealer_id")
        @JSONField(name = "dealer_id")
        @JsonProperty("dealer_id")
        private String dealerId;

        @SerializedName("dealer_id__r")
        @JSONField(name = "dealer_id__r")
        @JsonProperty("dealer_id__r")
        private String dealerName;

        @SerializedName("activity_id")
        @JSONField(name = "activity_id")
        @JsonProperty("activity_id")
        private String activityId;

        @SerializedName("activity_id__r")
        @JSONField(name = "activity_id__r")
        @JsonProperty("activity_id__r")
        private String activityName;

        @SerializedName("dealer_activity")
        @JSONField(name = "dealer_activity")
        @JsonProperty("dealer_activity")
        private String dealerActivityId;

        @SerializedName("dealer_activity__r")
        @JSONField(name = "dealer_activity__r")
        @JsonProperty("dealer_activity__r")
        private String dealerActivityName;

        @SerializedName("visit_id")
        @JSONField(name = "visit_id")
        @JsonProperty("visit_id")
        private String visitId;

        @SerializedName("action_id")
        @JSONField(name = "action_id")
        @JsonProperty("action_id")
        private String actionId;

        @SerializedName("activity_agreement_id")
        @JSONField(name = "activity_agreement_id")
        @JsonProperty("activity_agreement_id")
        private String activityAgreementId;

        @SerializedName("activity_agreement_id__r")
        @JSONField(name = "activity_agreement_id__r")
        @JsonProperty("activity_agreement_id__r")
        private String activityAgreementName;

        @SerializedName("activity_proof_id__r")
        @JSONField(name = "activity_proof_id__r")
        @JsonProperty("activity_proof_id__r")
        private String activityProofName;

        @SerializedName("activity_proof_id")
        @JSONField(name = "activity_proof_id")
        @JsonProperty("activity_proof_id")
        private String activityProofId;

        @SerializedName("proof_images")
        @JSONField(name = "proof_images")
        @JsonProperty("proof_images")
        private Object proofImages;

        private List<String> owner;

        private Double total;
    }
}

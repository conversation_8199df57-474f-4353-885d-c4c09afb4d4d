package com.facishare.crm.fmcg.tpm.api.proof;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2021/9/7 下午3:01
 */
public interface FunctionalAuthority {

    @Data
    @ToString
    class Arg {

        @SerializedName("function_code_query_map")
        @JSONField(name = "function_code_query_map")
        @JsonProperty("function_code_query_map")
        private Map<String, List<String>> functionCodeQueryMap;
    }


    @Data
    @ToString
    class Result {
        @SerializedName("function_code_result_map")
        @JSONField(name = "function_code_result_map")
        @JsonProperty("function_code_result_map")
        private Map<String, Map<String, Boolean>> functionCodeResultMap;
    }
}

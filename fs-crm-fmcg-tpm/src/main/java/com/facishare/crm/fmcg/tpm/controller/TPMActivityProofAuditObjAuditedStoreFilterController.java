package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.crm.fmcg.tpm.api.proof.AuditedStoreFilter;
import com.facishare.crm.fmcg.tpm.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.apiname.CommonFields;
import com.facishare.crm.fmcg.tpm.apiname.TPMActivityProofAuditFields;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 12/2/20 4:12 PM
 */
@SuppressWarnings("Duplicates")
public class TPMActivityProofAuditObjAuditedStoreFilterController extends PreDefineController<AuditedStoreFilter.Arg, AuditedStoreFilter.Result> {

    @Override
    protected AuditedStoreFilter.Result doService(AuditedStoreFilter.Arg arg) {
        AuditedStoreFilter.Result apiResult = new AuditedStoreFilter.Result();

        if (!serviceFacade.funPrivilegeCheck(controllerContext.getUser(), ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ, "Add")) {
            throw new ValidateException(I18N.text(I18NKeys.DO_NOT_HAVE_PROOF_AUDIT_CREATE_RIGHT));
        }

        if (arg.getCheckinIdList().isEmpty()) {
            return apiResult;
        }

        List<IObjectData> auditData = queryProofAudit(controllerContext, arg.getBegin(), arg.getEnd(), arg.getActivityId(), arg.getDealerId(), arg.getCheckinIdList());
        apiResult.setCheckinIdList(auditData.stream().map(m -> (String) m.get(TPMActivityProofAuditFields.VISIT_ID)).collect(Collectors.toList()));
        return apiResult;
    }

    private List<IObjectData> queryProofAudit(ControllerContext context, long begin, long end, String activityId, String dealerId, List<String> visitIds) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        //todo: 原先limit 0
        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter visitIdFilter = new Filter();
        visitIdFilter.setFieldName(TPMActivityProofAuditFields.VISIT_ID);
        visitIdFilter.setOperator(Operator.IN);
        visitIdFilter.setFieldValues(visitIds);

        query.setFilters(Lists.newArrayList(visitIdFilter));

        if (!Strings.isNullOrEmpty(activityId)) {
            Filter activityIdFilter = new Filter();
            activityIdFilter.setFieldName(TPMActivityProofAuditFields.ACTIVITY_ID);
            activityIdFilter.setOperator(Operator.EQ);
            activityIdFilter.setFieldValues(Lists.newArrayList(activityId));

            query.getFilters().add(activityIdFilter);
        }

        if (!Strings.isNullOrEmpty(dealerId)) {
            Filter dealerIdFilter = new Filter();
            dealerIdFilter.setFieldName(TPMActivityProofAuditFields.DEALER_ID);
            dealerIdFilter.setOperator(Operator.EQ);
            dealerIdFilter.setFieldValues(Lists.newArrayList(dealerId));

            query.getFilters().add(dealerIdFilter);
        }

        if (begin > 0 && end > 0) {
            Filter timeFilter = new Filter();
            timeFilter.setFieldName(CommonFields.CREATE_TIME);
            timeFilter.setOperator(Operator.BETWEEN);
            timeFilter.setFieldValues(Lists.newArrayList(String.valueOf(begin), String.valueOf(end)));

            query.getFilters().add(timeFilter);
        }

        return CommonUtils.queryData(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ, query);
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Collections.emptyList();
    }
}
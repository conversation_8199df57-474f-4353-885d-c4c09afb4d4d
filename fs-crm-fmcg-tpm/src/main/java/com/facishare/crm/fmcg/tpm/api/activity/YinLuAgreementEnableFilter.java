package com.facishare.crm.fmcg.tpm.api.activity;

import com.alibaba.fastjson.annotation.JSONField;
import com.beust.jcommander.internal.Lists;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/3/16 2:36 PM
 */
public interface YinLuAgreementEnableFilter {

    @Data
    @ToString
    class Arg implements Serializable {

        @SerializedName("account_id")
        @JSONField(name = "account_id")
        @JsonProperty("account_id")
        private String accountId;

        @SerializedName("user_id")
        @JSONField(name = "user_id")
        @JsonProperty("user_id")
        private String userId;
    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        @SerializedName("plan_ids")
        @JSONField(name = "plan_ids")
        @JsonProperty("plan_ids")
        private List<String> planIds;

        @SerializedName("parent_department_ids")
        @JSONField(name = "parent_department_ids")
        @JsonProperty("parent_department_ids")
        private List<String> parentDepartmentIds;

        @SerializedName("out_route")
        @JSONField(name = "out_route")
        @JsonProperty("out_route")
        private boolean outRoute;

        private boolean error;

        private String message;

        public static Result success(List<String> planIds) {
            return Result.builder().error(false).message("").planIds(planIds).build();
        }

        public static Result outRoute(List<String> parentDepartmentIds) {
            return Result.builder().error(false).message("").outRoute(true).parentDepartmentIds(parentDepartmentIds).build();
        }

        public static Result error(String message) {
            return Result.builder().error(true).message(message).planIds(Lists.newArrayList()).build();
        }
    }
}

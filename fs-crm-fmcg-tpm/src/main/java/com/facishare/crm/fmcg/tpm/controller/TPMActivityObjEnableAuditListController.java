package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.crm.fmcg.tpm.api.activity.EnableAuditList;
import com.facishare.crm.fmcg.tpm.apiname.*;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;

import java.util.*;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 12/2/20 4:12 PM
 */
@SuppressWarnings("Duplicates")
public class TPMActivityObjEnableAuditListController extends PreDefineController<EnableAuditList.Arg, EnableAuditList.Result> {

    private static final Map<String, String> GROUP_NAME_MAP = new HashMap<>();

    static {
        GROUP_NAME_MAP.put("completed", "已检核");
        GROUP_NAME_MAP.put("schedule", "待检核");
    }

    @Override
    protected EnableAuditList.Result doService(EnableAuditList.Arg arg) {
        if (!serviceFacade.funPrivilegeCheck(controllerContext.getUser(), ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ, "Add")) {
            throw new ValidateException(I18N.text(I18NKeys.DO_NOT_HAVE_PROOF_AUDIT_CREATE_RIGHT));
        }

        boolean visitCompleted = "4".equals(arg.getVisitStatus());

        if (Strings.isNullOrEmpty(arg.getVisitId()) || Strings.isNullOrEmpty(arg.getActionId())) {
            throw new ValidateException("visit_id or action_id can not be empty.");
        }

        if (Strings.isNullOrEmpty(arg.getStoreId())) {
            throw new ValidateException("store id can not be empty.");
        }

        IObjectData storeData = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()), arg.getStoreId(), ApiNames.ACCOUNT_OBJ);
        if (storeData == null) {
            throw new ValidateException("store not found.");
        }

        return visitCompleted ? listWhenCompleted(controllerContext, arg) : listWhenSchedule(controllerContext, arg);
    }

    private EnableAuditList.Result listWhenCompleted(ControllerContext context, EnableAuditList.Arg arg) {
        List<IObjectData> proofAuditList = queryProofAudit(context, arg.getStoreId(), arg.getVisitId(), arg.getActionId());

        Map<String, IObjectData> proofAuditMap = new HashMap<>();
        for (IObjectData proofAudit : proofAuditList) {
            String activityId = (String) proofAudit.get(TPMActivityProofAuditFields.ACTIVITY_ID);
            proofAuditMap.put(activityId, proofAudit);
        }

        List<String> activityIdList = Lists.newArrayList(proofAuditMap.keySet());
        List<String> agreementIdList = proofAuditMap.values().stream()
                .map(m -> (String) m.get(TPMActivityProofAuditFields.ACTIVITY_AGREEMENT_ID))
                .filter(f -> !Strings.isNullOrEmpty(f))
                .distinct()
                .collect(Collectors.toList());

        EnableAuditList.Result result = new EnableAuditList.Result();
        result.setNavigateStrategy("activity_list");
        if (activityIdList.isEmpty()) {
            return result;
        }

        EnableAuditList.ActivityGroupVO group = new EnableAuditList.ActivityGroupVO();
        group.setGroupKey("");
        group.setGroupName("");
        group.setActivityList(Lists.newArrayList());

        result.setData(Lists.newArrayList(group));

        Map<String, IObjectData> activityMap = queryActivity(context, activityIdList).stream().collect(Collectors.toMap(DBRecord::getId, v -> v));
        Map<String, IObjectData> agreementMap = queryAgreement(context, agreementIdList).stream().collect(Collectors.toMap(DBRecord::getId, v -> v));

        for (String id : activityIdList) {
            IObjectData activity = activityMap.get(id);
            if (activity != null) {
                EnableAuditList.ActivityVO datum = new EnableAuditList.ActivityVO();
                boolean agreementRequired = Boolean.TRUE.equals(activity.get(TPMActivityFields.IS_AGREEMENT_REQUIRED));
                datum.setId(id);
                datum.setName(activity.getName());
                datum.setAgreementRequired(agreementRequired);
                datum.setBeginDate((Long) activity.get(TPMActivityFields.BEGIN_DATE));
                datum.setEndDate((Long) activity.get(TPMActivityFields.END_DATE));
                if (proofAuditMap.containsKey(id)) {
                    IObjectData proofAudit = proofAuditMap.get(id);
                    datum.setStatus((String) proofAudit.get(TPMActivityProofAuditFields.AUDIT_STATUS));
                    datum.setDataId(proofAudit.getId());
                    datum.setDataApiName(ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ);

                    String agreementId = (String) proofAudit.get(TPMActivityProofAuditFields.ACTIVITY_AGREEMENT_ID);
                    if (!Strings.isNullOrEmpty(agreementId) && agreementMap.containsKey(agreementId)) {
                        IObjectData agreement = agreementMap.get(agreementId);
                        datum.setActivityAgreementId(agreementId);
                        datum.setAgreementBeginDate((long) agreement.get(TPMActivityAgreementFields.BEGIN_DATE));
                        datum.setAgreementEndDate((long) agreement.get(TPMActivityAgreementFields.END_DATE));
                    }
                }
                group.getActivityList().add(datum);
            }
        }
        return result;
    }

    private EnableAuditList.Result listWhenSchedule(ControllerContext context, EnableAuditList.Arg arg) {
        List<IObjectData> proofAuditList = queryProofAudit(context, arg.getStoreId(), arg.getVisitId(), arg.getActionId());

        Map<String, IObjectData> proofAuditMap = new HashMap<>();
        for (IObjectData proofAudit : proofAuditList) {
            String activityId = (String) proofAudit.get(TPMActivityProofAuditFields.ACTIVITY_ID);
            proofAuditMap.put(activityId, proofAudit);
        }

        List<IObjectData> proofList = queryProof(context, arg.getStoreId());
        Map<String, IObjectData> proofMap = new HashMap<>();
        for (IObjectData proof : proofList) {
            String activityId = (String) proof.get(TPMActivityProofFields.ACTIVITY_ID);
            if (proofAuditMap.containsKey(activityId)) {
                continue;
            }
            if (proofMap.containsKey(activityId)) {
                if (proof.getCreateTime() > proofMap.get(activityId).getCreateTime()) {
                    proofMap.put(activityId, proof);
                }
            } else {
                proofMap.put(activityId, proof);
            }
        }
        Set<String> scheduleList = proofMap.entrySet().stream()
                .filter(entry -> TPMActivityProofFields.AUDIT_STATUS__SCHEDULE.equals(entry.getValue().get(TPMActivityProofFields.AUDIT_STATUS)))
                .map(Map.Entry::getKey)
                .collect(Collectors.toSet());

        List<String> allIdList = Lists.newArrayList();
        allIdList.addAll(proofAuditMap.keySet());
        allIdList.addAll(scheduleList);

        List<String> agreementIdList = proofMap.values().stream()
                .map(m -> (String) m.get(TPMActivityProofFields.ACTIVITY_AGREEMENT_ID))
                .filter(f -> !Strings.isNullOrEmpty(f))
                .distinct()
                .collect(Collectors.toList());

        agreementIdList.addAll(proofAuditMap.values().stream()
                .map(m -> (String) m.get(TPMActivityProofAuditFields.ACTIVITY_AGREEMENT_ID))
                .filter(f -> !Strings.isNullOrEmpty(f))
                .distinct()
                .collect(Collectors.toList()));

        EnableAuditList.Result result = new EnableAuditList.Result();
        result.setNavigateStrategy("activity_list");
        if (allIdList.isEmpty()) {
            return result;
        }

        EnableAuditList.ActivityGroupVO group = new EnableAuditList.ActivityGroupVO();
        group.setGroupKey("");
        group.setGroupName("");
        group.setActivityList(Lists.newArrayList());
        result.setData(Lists.newArrayList(group));

        Map<String, IObjectData> activityMap = queryActivity(context, allIdList).stream().collect(Collectors.toMap(DBRecord::getId, v -> v));
        Map<String, IObjectData> agreementMap = queryAgreement(context, agreementIdList).stream().collect(Collectors.toMap(DBRecord::getId, v -> v));

        Map<String, List<String>> activityGroupMap = new HashMap<>();
        activityGroupMap.put("completed", Lists.newArrayList(proofAuditMap.keySet()));
        activityGroupMap.put("schedule", Lists.newArrayList(scheduleList));

        for (Map.Entry<String, List<String>> groupEntry : activityGroupMap.entrySet()) {
            if (groupEntry.getValue().isEmpty()) {
                continue;
            }
            for (String id : groupEntry.getValue()) {
                IObjectData activity = activityMap.get(id);
                if (activity != null) {
                    EnableAuditList.ActivityVO datum = new EnableAuditList.ActivityVO();
                    boolean agreementRequired = Boolean.TRUE.equals(activity.get(TPMActivityFields.IS_AGREEMENT_REQUIRED));
                    datum.setId(id);
                    datum.setName(activity.getName());
                    datum.setAgreementRequired(agreementRequired);
                    datum.setBeginDate((Long) activity.get(TPMActivityFields.BEGIN_DATE));
                    datum.setEndDate((Long) activity.get(TPMActivityFields.END_DATE));
                    if (groupEntry.getKey().equals("schedule")) {
                        datum.setStatus(groupEntry.getKey());
                    } else if (groupEntry.getKey().equals("completed") && proofAuditMap.containsKey(id)) {
                        IObjectData proofAudit = proofAuditMap.get(id);
                        datum.setStatus((String) proofAudit.get(TPMActivityProofAuditFields.AUDIT_STATUS));
                        datum.setDataApiName(ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ);
                        datum.setDataId(proofAudit.getId());
                        String agreementId = (String) proofAudit.get(TPMActivityProofAuditFields.ACTIVITY_AGREEMENT_ID);
                        if (!Strings.isNullOrEmpty(agreementId) && agreementMap.containsKey(agreementId)) {
                            IObjectData agreement = agreementMap.get(agreementId);
                            datum.setActivityAgreementId(agreementId);
                            datum.setAgreementBeginDate((long) agreement.get(TPMActivityAgreementFields.BEGIN_DATE));
                            datum.setAgreementEndDate((long) agreement.get(TPMActivityAgreementFields.END_DATE));
                        }
                    }
                    if (proofMap.containsKey(id)) {
                        IObjectData proofData = proofMap.get(id);
                        datum.setProofId(proofData.getId());
                        String agreementId = (String) proofData.get(TPMActivityProofFields.ACTIVITY_AGREEMENT_ID);
                        if (!Strings.isNullOrEmpty(agreementId) && agreementMap.containsKey(agreementId)) {
                            IObjectData agreement = agreementMap.get(agreementId);
                            datum.setActivityAgreementId(agreementId);
                            datum.setAgreementBeginDate((long) agreement.get(TPMActivityAgreementFields.BEGIN_DATE));
                            datum.setAgreementEndDate((long) agreement.get(TPMActivityAgreementFields.END_DATE));
                        }
                    }
                    group.getActivityList().add(datum);
                }
            }
        }

        if (allIdList.size() == 1 && scheduleList.size() == 1) {
            result.setNavigateStrategy("proof_audit");
        } else {
            result.setNavigateStrategy("activity_list");
        }
        return result;
    }

    private List<IObjectData> queryProofAudit(ControllerContext context, String storeId, String visitId, String actionId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        //todo: 原先limit 0
        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter storeIdFilter = new Filter();
        storeIdFilter.setFieldName(TPMActivityProofAuditFields.STORE_ID);
        storeIdFilter.setOperator(Operator.EQ);
        storeIdFilter.setFieldValues(Lists.newArrayList(storeId));

        Filter visitIdFilter = new Filter();
        visitIdFilter.setFieldName(TPMActivityProofAuditFields.VISIT_ID);
        visitIdFilter.setOperator(Operator.EQ);
        visitIdFilter.setFieldValues(Lists.newArrayList(visitId));

        Filter actionIdFilter = new Filter();
        actionIdFilter.setFieldName(TPMActivityProofAuditFields.ACTION_ID);
        actionIdFilter.setOperator(Operator.EQ);
        actionIdFilter.setFieldValues(Lists.newArrayList(actionId));

        query.setFilters(Lists.newArrayList(storeIdFilter, actionIdFilter, visitIdFilter));

        return CommonUtils.queryData(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ, query);
    }

    private List<IObjectData> queryActivity(ControllerContext context, List<String> ids) {
        if (ids.isEmpty()) {
            return new ArrayList<>();
        }

        SearchTemplateQuery query = new SearchTemplateQuery();
        //todo: 原先limit 0
        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter idFilter = new Filter();
        idFilter.setFieldName(CommonFields.ID);
        idFilter.setOperator(Operator.IN);
        idFilter.setFieldValues(ids);

        query.setFilters(Lists.newArrayList(idFilter));
        return CommonUtils.queryData(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_OBJ, query);
    }

    private List<IObjectData> queryAgreement(ControllerContext context, List<String> ids) {
        if (ids.isEmpty()) {
            return new ArrayList<>();
        }

        SearchTemplateQuery query = new SearchTemplateQuery();
        //todo: 原先limit 0
        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter idFilter = new Filter();
        idFilter.setFieldName(CommonFields.ID);
        idFilter.setOperator(Operator.IN);
        idFilter.setFieldValues(ids);

        query.setFilters(Lists.newArrayList(idFilter));
        return CommonUtils.queryData(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, query);
    }

    private List<IObjectData> queryProof(ControllerContext context, String storeId) {

        SearchTemplateQuery query = new SearchTemplateQuery();

        //todo: 原先limit 0
        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter storeIdFilter = new Filter();
        storeIdFilter.setFieldName(TPMActivityProofFields.STORE_ID);
        storeIdFilter.setOperator(Operator.EQ);
        storeIdFilter.setFieldValues(Lists.newArrayList(storeId));

        query.setFilters(Lists.newArrayList(storeIdFilter));

        return CommonUtils.queryData(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_PROOF_OBJ, query);
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Collections.emptyList();
    }
}

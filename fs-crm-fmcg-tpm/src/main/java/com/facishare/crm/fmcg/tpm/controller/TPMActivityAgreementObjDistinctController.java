package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.api.agreement.Transfer;
import com.facishare.crm.fmcg.tpm.apiname.ApiNames;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;
import com.google.common.collect.Lists;
import groovy.util.logging.Slf4j;
import org.springframework.util.ResourceUtils;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 *
 * <AUTHOR>
 * create time 2021/9/23 15:33
 */
@Slf4j
@SuppressWarnings("Duplicates")
public class TPMActivityAgreementObjDistinctController extends PreDefineController<Transfer.DistinctArg, Transfer.Result> {

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList();
    }

    @Override
    protected Transfer.Result doService(Transfer.DistinctArg arg) {
        String tenantId = "722872";

        if ("invalid".equals(arg.getFlag())) {

            List<List<String>> idsList = Lists.partition(loadInvalidIdsFromFile(), 50);
            for (List<String> ids : idsList) {

                List<IObjectData> objects = ids.stream().map(id -> {
                    ObjectData obj = new ObjectData();

                    obj.setId(id);
                    obj.setTenantId(tenantId);
                    obj.setDescribeApiName(ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);

                    return obj;
                }).collect(Collectors.toList());

                serviceFacade.bulkInvalid(objects, User.systemUser(tenantId));
            }
        } else {
            List<List<String>> idsList = Lists.partition(loadRevertIdsFromFile(), 20);
            for (List<String> ids : idsList) {

                List<IObjectData> objects = ids.stream().map(id -> {
                    ObjectData obj = new ObjectData();

                    obj.setId(id);
                    obj.setTenantId(tenantId);
                    obj.setDescribeApiName(ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);

                    return obj;
                }).collect(Collectors.toList());

                try {
                    serviceFacade.bulkRecover(objects, User.systemUser(tenantId));
                } catch (MetaDataBusinessException e) {
                    //error：非作废数据不可恢复
                    if (201112008 == e.getErrorCode()) {
                        log.info("data is not invalid:{}", JSON.toJSONString(ids));
                        continue;
                    }
                    log.error("revert data has biz exception:{}", JSON.toJSONString(ids));
                }
            }
        }

        return new Transfer.Result();
    }

    private List<String> loadRevertIdsFromFile() {
        try {
            File file = ResourceUtils.getFile("classpath:agreementRevert.txt");
            String fileString = new String(Files.readAllBytes(file.toPath()));
            return Arrays.asList(fileString.split("\n"));
        } catch (IOException ex) {
            log.error("load revert id from file cause io exception.", ex);
            throw new ValidateException("load revert id from file cause io exception.");
        }
    }

    private List<String> loadInvalidIdsFromFile() {
        try {
            File file = ResourceUtils.getFile("classpath:agreementDistinct.txt");
            String fileString = new String(Files.readAllBytes(file.toPath()));
            return Arrays.asList(fileString.split("\n"));
        } catch (IOException ex) {
            log.error("load invalid id from file cause io exception.", ex);
            throw new ValidateException("load invalid id from file cause io exception.");
        }
    }
}

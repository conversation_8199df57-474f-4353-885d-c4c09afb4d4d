package com.facishare.crm.fmcg.tpm.controller;

import com.beust.jcommander.internal.Lists;
import com.beust.jcommander.internal.Maps;
import com.facishare.crm.fmcg.tpm.api.activity.ExtendEndDate;
import com.facishare.crm.fmcg.tpm.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.apiname.TPMActivityFields;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.metadata.api.IObjectData;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2020/11/10 4:39 PM
 */
public class TPMActivityObjExtendEndDateController extends PreDefineController<ExtendEndDate.Arg, ExtendEndDate.Result> {


    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList();
    }

    @Override
    protected ExtendEndDate.Result doService(ExtendEndDate.Arg arg) {

        IObjectData data = serviceFacade.findObjectData(controllerContext.getUser(), arg.getObjectDataId(), ApiNames.TPM_ACTIVITY_OBJ);
        if (Objects.isNull(data)) {
            throw new ValidateException("Activity not found.");
        }

        Long currentEndDate = data.get(TPMActivityFields.END_DATE, Long.class);
        if (Objects.isNull(currentEndDate) || currentEndDate > arg.getEndDate()) {
            throw new ValidateException("The specified end date must be greater than the current end date.");
        }

        Map<String, Object> updater = Maps.newHashMap();
        updater.put(TPMActivityFields.END_DATE, arg.getEndDate());
        IObjectData newData = serviceFacade.updateWithMap(controllerContext.getUser(), data, updater);

        return ExtendEndDate.Result.builder().objectData(ObjectDataDocument.of(newData)).build();
    }
}

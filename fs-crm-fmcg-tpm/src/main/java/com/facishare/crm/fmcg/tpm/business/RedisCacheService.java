package com.facishare.crm.fmcg.tpm.business;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.business.abstraction.IRedisCacheService;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.github.jedis.support.MergeJedisCmd;
import de.lab4inf.math.util.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2021/6/21 上午10:46
 */
@Slf4j
@Component
public class RedisCacheService implements IRedisCacheService {

    @Resource(name = "redisCmd")
    private MergeJedisCmd redisCmd;

    @Override
    public void reBuildPostId(ActionContext actionContext) {
        try{
            String key = String.format("%s:%s:%s",actionContext.getObjectApiName(),actionContext.getActionCode(),actionContext.getPostId());
            if(!Strings.isNullOrEmpty(redisCmd.get(key))){
                RequestContext requestContext = actionContext.getRequestContext();
                Field postIdField = RequestContext.class.getDeclaredField("postId");
                postIdField.setAccessible(true);
                postIdField.set(requestContext,null);
            }
        }catch (Exception e){
            log.info("reBuildPostId fail .actionContext:{}", JSON.toJSONString(actionContext));
        }
    }

    @Override
    public void cacheErrorPostId(ActionContext actionContext) {
        String key = String.format("%s:%s:%s",actionContext.getObjectApiName(),actionContext.getActionCode(),actionContext.getPostId());
        redisCmd.set(key, UUID.randomUUID().toString(),"nx","px",300000);
    }
}

package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.apiname.CommonFields;
import com.facishare.crm.fmcg.tpm.apiname.TPMActivityBudgetFields;
import com.facishare.crm.fmcg.tpm.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.tpm.business.BudgetService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardIncrementUpdateAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import de.lab4inf.math.util.Strings;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2021/3/9 下午7:28
 */
@SuppressWarnings("Duplicates")
public class TPMActivityBudgetObjIncrementUpdateAction extends StandardIncrementUpdateAction {

    public static List<String> ALLOW_LIST = Lists.newArrayList(TPMActivityBudgetFields.BUDGET_DEPARTMENT, TPMActivityBudgetFields.PERIOD_YEAR, "_id");
    private static final BudgetService budgetService = SpringUtil.getContext().getBean(BudgetService.class);

    static {
        ConfigFactory.getConfig("gray-rel-fmcg", iConfig -> {
            List<String> allowList = new ArrayList<>();
            List<String> list = JSON.parseArray(iConfig.get("BUDGET_ALLOW_EDIT_LIST"), String.class);
            if (!CollectionUtils.isEmpty(list)) {
                allowList.addAll(list);
                allowList.addAll(Lists.newArrayList(TPMActivityBudgetFields.BUDGET_DEPARTMENT, TPMActivityBudgetFields.PERIOD_YEAR, "_id"));
                ALLOW_LIST = allowList;
            }
        });
    }

    @Override
    protected void before(Arg arg) {
        if (actionContext.isFromFunction() || actionContext.isFromOpenAPI()) {
            arg.getData().keySet().forEach(key -> {
                if (!Strings.isNullOrEmpty(key) && !key.endsWith("__c") && !ALLOW_LIST.contains(key))
                    throw new ValidateException("不允许函数更新预算表的预置字段。");
            });
        }
        super.before(arg);
    }

    @Override
    protected void finallyDo() {
        try {
            super.finallyDo();
        } finally {
            budgetService.unLockBudget(actionContext);
        }
    }
}

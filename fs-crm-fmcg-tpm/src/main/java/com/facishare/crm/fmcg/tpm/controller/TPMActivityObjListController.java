package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.crm.fmcg.tpm.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.apiname.CommonFields;
import com.facishare.crm.fmcg.tpm.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.tpm.common.TPMGrayUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ButtonDocument;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.common.release.GrayRelease;
import com.google.common.collect.Lists;

import java.util.HashMap;
import java.util.List;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2020/11/10 4:39 PM
 */
public class TPMActivityObjListController extends StandardListController {

    protected static final ButtonDocument CLOSE_ACTIVITY_BUTTON = new ButtonDocument();

    static {
        CLOSE_ACTIVITY_BUTTON.put("_id", "CloseActivity_button_default_60501de809e1dc00017c2024");
        CLOSE_ACTIVITY_BUTTON.put("api_name", "CloseActivity");
        CLOSE_ACTIVITY_BUTTON.put("action", "CloseActivity");
        CLOSE_ACTIVITY_BUTTON.put("label", "结案");
        CLOSE_ACTIVITY_BUTTON.put("action_type", "system");
        CLOSE_ACTIVITY_BUTTON.put("actions", Lists.newArrayList());
        CLOSE_ACTIVITY_BUTTON.put("button_type", "common");
        CLOSE_ACTIVITY_BUTTON.put("describe_api_name", "TPMActivityObj");
        CLOSE_ACTIVITY_BUTTON.put("is_active", true);
        CLOSE_ACTIVITY_BUTTON.put("is_deleted", false);
        CLOSE_ACTIVITY_BUTTON.put("use_pages", Lists.newArrayList("list", "detail"));
        CLOSE_ACTIVITY_BUTTON.put("where", Lists.newArrayList());
    }

    @Override
    protected Result after(Arg arg, Result result) {
        String functionCode = TPMGrayUtils.isSupportCloseActivityPrivileges(controllerContext.getTenantId()) ? ObjectAction.CLOSE_TPM_ACTIVITY.getActionCode() : "Edit";
        if (arg.isIncludeButtonInfo() && serviceFacade.funPrivilegeCheck(controllerContext.getUser(), ApiNames.TPM_ACTIVITY_OBJ, functionCode)) {
            if (result.getButtonInfo().getButtons() == null) {
                result.getButtonInfo().setButtons(Lists.newArrayList(CLOSE_ACTIVITY_BUTTON));
            } else {
                result.getButtonInfo().getButtons().add(CLOSE_ACTIVITY_BUTTON);
            }
            List<String> buttonArr = Lists.newArrayList("CloseActivity");
            if (result.getButtonInfo().getButtonMap() == null) {
                result.getButtonInfo().setButtonMap(new HashMap<>());
                for (ObjectDataDocument obj : result.getDataList()) {
                    String activityStatus = (String) obj.get(TPMActivityFields.ACTIVITY_STATUS);
                    String closedStatus = (String) obj.get(TPMActivityFields.CLOSED_STATUS);
                    String lifeStatus = (String) obj.get(CommonFields.LIFE_STATUS);
                    boolean statusJudge = TPMGrayUtils.isAllowProcessActivityClose(controllerContext.getTenantId()) ? "normal".equals(lifeStatus) : TPMActivityFields.ACTIVITY_STATUS__END.equals(activityStatus);
                    if (statusJudge && !TPMActivityFields.CLOSE_STATUS__CLOSED.equals(closedStatus)) {
                        result.getButtonInfo().getButtonMap().put(obj.getId(), buttonArr);
                    }
                }
            } else {
                for (ObjectDataDocument obj : result.getDataList()) {
                    String activityStatus = (String) obj.get(TPMActivityFields.ACTIVITY_STATUS);
                    String closedStatus = (String) obj.get(TPMActivityFields.CLOSED_STATUS);
                    String lifeStatus = (String) obj.get(CommonFields.LIFE_STATUS);
                    boolean statusJudge = TPMGrayUtils.isAllowProcessActivityClose(controllerContext.getTenantId()) ? "normal".equals(lifeStatus) : TPMActivityFields.ACTIVITY_STATUS__END.equals(activityStatus);
                    if (statusJudge && !TPMActivityFields.CLOSE_STATUS__CLOSED.equals(closedStatus)) {
                        if (result.getButtonInfo().getButtonMap().containsKey(obj.getId())) {
                            result.getButtonInfo().getButtonMap().get(obj.getId()).add("CloseActivity");
                        } else {
                            result.getButtonInfo().getButtonMap().put(obj.getId(), buttonArr);
                        }
                    }
                }
            }
        }
        return super.after(arg, result);
    }

    @Override
    protected SearchTemplateQuery buildSearchTemplateQuery() {
        SearchTemplateQuery query = super.buildSearchTemplateQuery();
        if (GrayRelease.isAllow("fmcg", "TPM_USE_ES_QUERY", controllerContext.getTenantId())) {
            query.setSearchSource("es");
        }
        return query;
    }
}

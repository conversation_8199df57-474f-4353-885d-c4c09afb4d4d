package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.JSONObject;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.fxiaoke.common.release.GrayRelease;

import java.util.List;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2020/11/10 4:39 PM
 */
public class TPMActivityProofObjListHeaderController extends StandardListHeaderController {

    @Override
    protected Result after(Arg arg, Result result) {
        if (!GrayRelease.isAllow("fmcg", "TPM_WEB_PROOF", controllerContext.getTenantId())) {
            List<JSONObject> buttons = (List<JSONObject>) result.getLayout().get("buttons");
            result.getLayout().put("buttons", buttons.stream().filter(v -> !v.getString("action").equals("Add") && !v.getString("action").equals("Import") && !v.getString("action").equals("Edit")).collect(Collectors.toList()));
        }
        return super.after(arg, result);
    }
}

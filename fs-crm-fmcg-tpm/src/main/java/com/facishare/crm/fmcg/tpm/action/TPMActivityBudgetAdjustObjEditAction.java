package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.api.enumeration.LogType;
import com.facishare.crm.fmcg.tpm.api.log.LogData;
import com.facishare.crm.fmcg.tpm.api.method.IdempotentArgBase;
import com.facishare.crm.fmcg.tpm.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.apiname.CommonFields;
import com.facishare.crm.fmcg.tpm.apiname.TPMActivityBudgetAdjustFields;
import com.facishare.crm.fmcg.tpm.business.BudgetService;
import com.facishare.crm.fmcg.tpm.business.OperateInfoService;
import com.facishare.crm.fmcg.tpm.common.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.service.PackTransactionProxy;
import com.facishare.crm.fmcg.tpm.service.PackTransactionProxyImpl;
import com.facishare.crm.fmcg.tpm.service.TransactionService;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.GlobalConstant;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.appframework.flow.ApprovalFlowStartResult;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.common.MapUtils;
import com.fxiaoke.common.release.GrayRelease;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/7/21 下午3:16
 */
public class TPMActivityBudgetAdjustObjEditAction extends StandardEditAction implements TransactionService<StandardEditAction.Arg, StandardEditAction.Result> {


    private static final Logger log = LoggerFactory.getLogger(TPMActivityBudgetAdjustObjEditAction.class);

    private static final BudgetService budgetService = SpringUtil.getContext().getBean(BudgetService.class);

    private final OperateInfoService operateInfoService = SpringUtil.getContext().getBean(OperateInfoService.class);

    private final PackTransactionProxy packTransactionProxy = SpringUtil.getContext().getBean(PackTransactionProxyImpl.class);

    private static final String SEND_BUDGET_IDS = "SEND_BUDGET_IDS:%s";

    private boolean needTriggerApproval;


    @Override
    protected void before(Arg arg) {


        if (Boolean.TRUE.equals(arg.getIncrementUpdate())) {
            super.before(arg);
        }
        if (!"ineffective".equals(arg.getObjectData().get(CommonFields.LIFE_STATUS))) {
            throw new ValidateException(I18N.text(I18NKeys.NORMAL_STATUS_ADJUST_CAN_NOT_BE_EDIT));
        }
        double amount = Double.parseDouble(String.valueOf(arg.getObjectData().get(TPMActivityBudgetAdjustFields.AMOUNT)));
        boolean transferInSupportNegative = TPMGrayUtils.budgetTransferInSupportNegative(actionContext.getTenantId());
        String recordType = (String) arg.getObjectData().get(CommonFields.RECORD_TYPE);
        if (!transferInSupportNegative && amount <= 0 || transferInSupportNegative && amount <= 0 && !"transform_in__c".equals(recordType)) {
            throw new ValidateException(I18N.text(I18NKeys.ADJUST_AMOUNT_MUST_MORE_THAN_ZERO));
        }
       /* if (arg.getObjectData().get(TPMActivityBudgetAdjustFields.TO_BUDGET_TABLE_ID) == null || arg.getObjectData().get(TPMActivityBudgetAdjustFields.FROM_BUDGET_TABLE_ID) == null) {
            actionContext.setAttribute("triggerFlow", false);
        }*/
        validateArg(arg);
        if (!Boolean.TRUE.equals(arg.getIncrementUpdate())) {
            super.before(arg);
        }
        buildApprovalCallback();
    }

    @Override
    protected void diffObjectDataWithDbData() {
        super.diffObjectDataWithDbData();
        buildApprovalCallback();
    }

    private void buildApprovalCallback() {
        if (!MapUtils.isNullOrEmpty(this.updatedFieldMap)) {
            if (this.updatedFieldMap.containsKey(GlobalConstant.BUDGET_APPROVAL_CALLBACK_MAP_KEY))
                return;
            Map<String, Object> callbackDatum = new HashMap<>();
            String repeatValue = actionContext.getAttribute(GlobalConstant.BUDGET_APPROVAL_CALLBACK_RELATION_KEY);
            String value = repeatValue == null ? IdGenerator.get() : repeatValue;
            callbackDatum.put(GlobalConstant.BUDGET_APPROVAL_CALLBACK_RELATION_KEY, value);
            actionContext.setAttribute(GlobalConstant.BUDGET_APPROVAL_CALLBACK_RELATION_KEY, value);
            this.updatedFieldMap.put(GlobalConstant.BUDGET_APPROVAL_CALLBACK_MAP_KEY, callbackDatum);
        } else {
            budgetService.buildCallbackKey(actionContext);
        }
    }

    @Override
    protected Map<String, Map<String, Object>> buildCallbackDataForAddAction(IObjectData objectData, Long detailCreateTime) {
        Map<String, Map<String, Object>> callbackData = super.buildCallbackDataForAddAction(objectData, detailCreateTime);
        String relationValue = actionContext.getAttribute(GlobalConstant.BUDGET_APPROVAL_CALLBACK_RELATION_KEY);
        if (!Strings.isNullOrEmpty(relationValue)) {
            Map<String, Object> instanceMap = callbackData.getOrDefault(objectData.getId(), new HashMap<>());
            callbackData.put(objectData.getId(), instanceMap);
            Map<String, Object> callbackDatum = new HashMap<>();
            callbackDatum.put(GlobalConstant.BUDGET_APPROVAL_CALLBACK_RELATION_KEY, relationValue);
            instanceMap.put(GlobalConstant.BUDGET_APPROVAL_CALLBACK_MAP_KEY, callbackDatum);
        }
        return callbackData;
    }

    void validateArg(Arg arg) {

        if (!TPMGrayUtils.isSupportOverrideAdjustTime(actionContext.getTenantId()))
            arg.getObjectData().put(TPMActivityBudgetAdjustFields.ADJUST_TIME, System.currentTimeMillis());
        Object fromId = arg.getObjectData().get(TPMActivityBudgetAdjustFields.FROM_BUDGET_TABLE_ID) != null ? arg.getObjectData().get(TPMActivityBudgetAdjustFields.FROM_BUDGET_TABLE_ID) : "";
        if (fromId.equals(arg.getObjectData().getOrDefault(TPMActivityBudgetAdjustFields.TO_BUDGET_TABLE_ID, ""))) {
            throw new ValidateException(I18N.text(I18NKeys.TRANSFER_IN_BUDGET_AND_TRANSFER_OUT_BUDGET_CAN_NOT_BE_THE_SAME));
        }
    }

    @Override
    protected Result doAct(Arg arg) {
        needTriggerApproval = budgetService.needApproval(actionContext.getTenantId(), ApiNames.TPM_ACTIVITY_BUDGET_ADJUST_OBJ, arg.getObjectData().getId(), "Create");
        tryLock();
        return packTransactionProxy.packAct(this, arg);
    }

    @Override
    public Result doActTransaction(Arg arg) {
        int mode = getAdjustMode(arg);

        String fromBudgetId = (String) arg.getObjectData().get(TPMActivityBudgetAdjustFields.FROM_BUDGET_TABLE_ID);
        String toBudgetId = (String) arg.getObjectData().get(TPMActivityBudgetAdjustFields.TO_BUDGET_TABLE_ID);


        List<String> budgetIds = Lists.newArrayList();
        long adjustTime = arg.getObjectData().get(TPMActivityBudgetAdjustFields.ADJUST_TIME) == null ? System.currentTimeMillis() : Long.parseLong(arg.getObjectData().get(TPMActivityBudgetAdjustFields.ADJUST_TIME).toString());

        actionContext.setAttribute("needTriggerApproval", needTriggerApproval);
        LogData logData = null;
        String logId = null;
        Map<String, Double> amountMap = new HashMap<>();
        double amount = Double.parseDouble(String.valueOf(arg.getObjectData().get(TPMActivityBudgetAdjustFields.AMOUNT)));
        amount = CommonUtils.keepNDecimal(amount, 3);
        if (mode == 1 || mode == 2) {
            budgetIds.add(fromBudgetId);
            //budgetService.tryLockBudget(actionContext, "fromBudget", fromBudgetId);
            IObjectData fromBudget = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), fromBudgetId, ApiNames.TPM_ACTIVITY_BUDGET);
            String fromLifeStatus = (String) fromBudget.get(CommonFields.LIFE_STATUS);
            if (!ObjectLifeStatus.NORMAL.getCode().equals(fromLifeStatus)) {
                throw new ValidateException("转出预算表未生效或处于审批中。");
            }

            double availableAmount = budgetService.getBudgetAvailableAmount(actionContext.getTenantId(), fromBudget, amountMap);
            if (GrayRelease.isAllow("fmcg", "IGNORE_BUDGET_ADJUST_AMOUNT_VALIDATE_WHEN_TRANSFER_OUT", actionContext.getTenantId())) {
                if (mode == 1 && CommonUtils.keepNDecimal(availableAmount - amount, 3) < 0) {
                    throw new ValidateException(I18N.text(I18NKeys.SOURCE_BUDGET_DON_NOT_HAVE_ENOUGH_MONEY));
                }
            } else {
                if (!GrayRelease.isAllow("fmcg", "IGNORE_BUDGET_ADJUST_AMOUNT_VALIDATE", actionContext.getTenantId()) && CommonUtils.keepNDecimal(availableAmount - amount, 3) < 0) {
                    throw new ValidateException(I18N.text(I18NKeys.SOURCE_BUDGET_DON_NOT_HAVE_ENOUGH_MONEY));
                }
            }

            logData = LogData.builder().data(JSON.toJSONString(arg)).updateMap(this.updatedFieldMap).originalData(this.dbMasterData).build();
            logData.setAttribute("fromBudget", fromBudget);
            logData.setAttribute("toAmountMap", amountMap);
            logId = operateInfoService.log(actionContext.getTenantId(), LogType.ADD.value(), JSON.toJSONString(logData), actionContext.getUser().getUserId(), ApiNames.TPM_ACTIVITY_BUDGET_ADJUST_OBJ, arg.getObjectData().getId(), needTriggerApproval);

            IObjectData detail = budgetService.addBudgetDetail(actionContext.getTenantId(), actionContext.getUser().getUserId(),
                    "1",
                    fromBudget.getId(),
                    String.format("预算调整：「%s」调整", fromBudget.get("name")),
                    -amount,
                    availableAmount,
                    availableAmount - amount,
                    adjustTime,
                    String.format("LogId:%s-traceId:%s", logId, TraceContext.get().getTraceId()),
                    null,
                    TraceContext.get().getTraceId(),
                    arg.getObjectData().getId(),
                    IdempotentArgBase.builder().idempotentKey(actionContext.getPostId() + ":" + fromBudget.getId()).build());
            actionContext.setAttribute("detail_id", detail.getId());
            budgetService.updateBudgetAdjustAmount(actionContext.getTenantId(), arg.getObjectData().toObjectData(), null, null, availableAmount, availableAmount - amount);
        }

        if ((!this.needTriggerApprovalFlow() && mode != 2) || (!needTriggerApproval && mode != 2)) {
            //budgetService.tryLockBudget(actionContext, "toBudget", toBudgetId);
            amountMap = budgetService.getBudgetAmountFields(actionContext.getTenantId(), toBudgetId);

            IObjectData toBudget = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), toBudgetId, ApiNames.TPM_ACTIVITY_BUDGET);

            String toLifeStatus = (String) toBudget.get(CommonFields.LIFE_STATUS);
            if (!ObjectLifeStatus.NORMAL.getCode().equals(toLifeStatus)) {
                throw new ValidateException("转入预算表未生效或处于审批中。");
            }

            double availableAmount = budgetService.getBudgetAvailableAmount(actionContext.getTenantId(), toBudget, amountMap);

            logData = logData == null ? LogData.builder().data(JSON.toJSONString(arg)).updateMap(this.updatedFieldMap).originalData(this.dbMasterData).build() : logData;
            logData.setAttribute("toBudget", toBudget);
            logData.setAttribute("toAmountMap", amountMap);
            Map<String, Object> updateField = new HashMap<>();
            updateField.put("log_data", JSON.toJSONString(logData));
            if (mode == 1 && logId != null) {
                operateInfoService.updateLog(logId, actionContext.getTenantId(), updateField);
            } else {
                logId = operateInfoService.log(actionContext.getTenantId(), LogType.ADD.value(), JSON.toJSONString(logData), actionContext.getUser().getUserId(), ApiNames.TPM_ACTIVITY_BUDGET_ADJUST_OBJ, arg.getObjectData().getId(), needTriggerApproval);
            }
            budgetService.addBudgetDetail(actionContext.getTenantId(), actionContext.getUser().getUserId(),
                    "2",
                    toBudget.getId(),
                    String.format("预算调整：「%s」调整", toBudget.get("name")),
                    amount,
                    availableAmount,
                    availableAmount + amount,
                    adjustTime,
                    String.format("LogId:%s-traceId:%s", logId, TraceContext.get().getTraceId()),
                    null,
                    TraceContext.get().getTraceId(),
                    arg.getObjectData().getId(),
                    IdempotentArgBase.builder().idempotentKey(actionContext.getPostId() + ":" + toBudget.getId()).build());

            budgetIds.add(toBudgetId);
            budgetService.updateBudgetAdjustAmount(actionContext.getTenantId(), arg.getObjectData().toObjectData(), availableAmount, availableAmount + amount, null, null);
            //budgetService.calculateBudget(actionContext.getTenantId(),toBudgetId);
        }

        Result result = super.doAct(arg);
        actionContext.setAttribute(String.format(SEND_BUDGET_IDS, actionContext.getPostId()), budgetIds);
        actionContext.setAttribute("operate_log_id", logId);
        // budgetService.calculateBudget(actionContext.getTenantId(),fromBudgetId);
        updateApprovalIdToDetail(result);
        return result;
    }

    private int getAdjustMode(Arg arg) {
        ObjectDataDocument argDoc = arg.getObjectData();
        if (!Strings.isNullOrEmpty((String) argDoc.get(TPMActivityBudgetAdjustFields.FROM_BUDGET_TABLE_ID)) && !Strings.isNullOrEmpty((String) argDoc.get(TPMActivityBudgetAdjustFields.TO_BUDGET_TABLE_ID))) {
            return 1;
        } else if (!Strings.isNullOrEmpty((String) argDoc.get(TPMActivityBudgetAdjustFields.FROM_BUDGET_TABLE_ID)) && Strings.isNullOrEmpty((String) argDoc.get(TPMActivityBudgetAdjustFields.TO_BUDGET_TABLE_ID))) {
            arg.getObjectData().put(TPMActivityBudgetAdjustFields.BALANCE_BEFORE_TRANSFER_IN, null);
            arg.getObjectData().put(TPMActivityBudgetAdjustFields.BALANCE_AFTER_TRANSFER_IN, null);
            return 2;
        } else if (Strings.isNullOrEmpty((String) argDoc.get(TPMActivityBudgetAdjustFields.FROM_BUDGET_TABLE_ID)) && !Strings.isNullOrEmpty((String) argDoc.get(TPMActivityBudgetAdjustFields.TO_BUDGET_TABLE_ID))) {
            arg.getObjectData().put(TPMActivityBudgetAdjustFields.BALANCE_AFTER_TRANSFER_OUT, null);
            arg.getObjectData().put(TPMActivityBudgetAdjustFields.BALANCE_BEFORE_TRANSFER_OUT, null);
            return 3;
        } else {
            throw new ValidateException(I18N.text(I18NKeys.BOTH_BUDGET_IN_ADJUST_CAN_NOT_BE_EMPTY));
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result result1 = super.after(arg, result);

        List<String> updateBudgetIds = actionContext.getAttribute(String.format(SEND_BUDGET_IDS, actionContext.getPostId()));
        log.info("key:{},updateBudgetIds:{},actionContext:{}", String.format(SEND_BUDGET_IDS, actionContext.getPostId()), updateBudgetIds, actionContext);

        if (!CollectionUtils.isEmpty(updateBudgetIds))
            updateBudgetIds.forEach(id -> budgetService.calculateBudget(actionContext.getTenantId(), id));
        return result1;
    }

    private void updateApprovalIdToDetail(Result result) {
        boolean needTriggerApproval = Boolean.TRUE.equals(actionContext.getAttribute("needTriggerApproval"));
        if (needTriggerApproval && !startApprovalFlowResult.containsValue(ApprovalFlowStartResult.APPROVAL_NOT_EXIST) && actionContext.getAttribute("operate_log_id") != null && this.needTriggerApprovalFlow()) {
            String approvalId = actionContext.getAttribute(GlobalConstant.BUDGET_APPROVAL_CALLBACK_RELATION_KEY);
            operateInfoService.updateLog(actionContext.getAttribute("operate_log_id"), actionContext.getTenantId(), approvalId);
            if (actionContext.getAttribute("detail_id") != null)
                budgetService.updateApprovalIdForDetail(actionContext.getTenantId(), actionContext.getAttribute("detail_id"), approvalId);
        }
    }


    @Override
    protected void finallyDo() {
        try {
            super.finallyDo();
        } finally {
            budgetService.unLockBudget(actionContext, "fromBudget");
            budgetService.unLockBudget(actionContext, "toBudget");
        }
    }

    public void tryLock() {
        String fromBudgetId = (String) arg.getObjectData().get(TPMActivityBudgetAdjustFields.FROM_BUDGET_TABLE_ID);
        String toBudgetId = (String) arg.getObjectData().get(TPMActivityBudgetAdjustFields.TO_BUDGET_TABLE_ID);
        int mode = getAdjustMode(arg);
        if (mode == 1 || mode == 2) {
            budgetService.tryLockBudget(actionContext, "fromBudget", fromBudgetId);
        }
        if ((!this.needTriggerApprovalFlow() && mode != 2) || (!needTriggerApproval && mode != 2)) {
            budgetService.tryLockBudget(actionContext, "toBudget", toBudgetId);
        }
    }
}

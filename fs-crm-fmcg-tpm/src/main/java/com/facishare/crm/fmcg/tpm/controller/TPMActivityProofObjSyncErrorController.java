package com.facishare.crm.fmcg.tpm.controller;

import com.beust.jcommander.internal.Sets;
import com.facishare.crm.fmcg.tpm.api.proof.SyncError;
import com.facishare.crm.fmcg.tpm.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.apiname.CommonFields;
import com.facishare.crm.fmcg.tpm.apiname.TPMActivityProofDetailFields;
import com.facishare.crm.fmcg.tpm.apiname.TPMActivityProofFields;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import de.lab4inf.math.util.Strings;

import java.math.BigDecimal;
import java.util.*;

@SuppressWarnings("Duplicates")
public class TPMActivityProofObjSyncErrorController extends PreDefineController<SyncError.Arg, SyncError.Result> {

    private static final int LIMIT = 200;
    private static final int LOOP_LIMIT = 1000;
    private static final Set<String> AUTO_NOT_PASS_ITEM = Sets.newHashSet();

    private static final Map<String, String> API_NAME_MAP = Maps.newHashMap();
    private static final Map<String, String> REF_FIELD_MAP = Maps.newHashMap();
    private static final Map<String, String> SKU_FIELD_MAP = Maps.newHashMap();
    private static final Map<String, String> ROW_FIELD_MAP = Maps.newHashMap();

    static {
        AUTO_NOT_PASS_ITEM.add("61cbd1642282650001c52e93");
        AUTO_NOT_PASS_ITEM.add("61cbd16d2282650001c53141");
        AUTO_NOT_PASS_ITEM.add("61cbd1772282650001c5378f");
        AUTO_NOT_PASS_ITEM.add("61cbd1385e5fd40001aaae20");
        AUTO_NOT_PASS_ITEM.add("61cbd1445e5fd40001aab130");

        REF_FIELD_MAP.put("61cac4ae0f3caa0001826e80", "field_08hbc__c");
        REF_FIELD_MAP.put("61cbd1155e5fd40001aaa1b0", "field_aux56__c");
        REF_FIELD_MAP.put("61cbd1232282650001c51169", "field_T1sKw__c");
        REF_FIELD_MAP.put("61cbd12d2282650001c51592", "field_oy4JD__c");

        API_NAME_MAP.put("61cac4ae0f3caa0001826e80", "object_y3a43__c");
        API_NAME_MAP.put("61cbd1155e5fd40001aaa1b0", "object_yilEa__c");
        API_NAME_MAP.put("61cbd1232282650001c51169", "object_nJ1XE__c");
        API_NAME_MAP.put("61cbd12d2282650001c51592", "object_q99z2__c");

        SKU_FIELD_MAP.put("61cac4ae0f3caa0001826e80", "normal_shelve_little_sku__c");
        SKU_FIELD_MAP.put("61cbd1155e5fd40001aaa1b0", "end_frame_little_sku__c");
        SKU_FIELD_MAP.put("61cbd1232282650001c51169", "yq_freeze_shelve_little_sku__c");
        SKU_FIELD_MAP.put("61cbd12d2282650001c51592", "other_freeze_shelve_little_sku__c");

        ROW_FIELD_MAP.put("61cac4ae0f3caa0001826e80", "normal_shelve_total_num__c");
        ROW_FIELD_MAP.put("61cbd1155e5fd40001aaa1b0", "end_frame_total_num__c");
        ROW_FIELD_MAP.put("61cbd1232282650001c51169", "yq_freeze_shelve_total_num__c");
        ROW_FIELD_MAP.put("61cbd12d2282650001c51592", "other_freeze_shelve_total_num__c");
    }

    @Override
    protected SyncError.Result doService(SyncError.Arg arg) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(LIMIT);
        query.setOffset(0);
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);

        Filter timeFilter = new Filter();
        timeFilter.setFieldName(CommonFields.CREATE_TIME);
        timeFilter.setOperator(Operator.BETWEEN);
        timeFilter.setFieldValues(Lists.newArrayList(String.valueOf(arg.getBegin()), String.valueOf(arg.getEnd())));

        Filter statusFilter = new Filter();
        statusFilter.setFieldName(TPMActivityProofFields.AUDIT_STATUS);
        statusFilter.setOperator(Operator.EQ);
        statusFilter.setFieldValues(Lists.newArrayList(TPMActivityProofFields.AUDIT_STATUS__REJECT));

        query.setFilters(Lists.newArrayList(timeFilter, statusFilter));

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.CREATE_TIME);
        order.setIsAsc(true);

        query.setOrders(Lists.newArrayList(order));

        int i = 0;
        do {
            i++;

            log.info("sync error loop : {}", i);

            QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(User.systemUser(controllerContext.getTenantId()), ApiNames.TPM_ACTIVITY_PROOF_OBJ, query);
            List<IObjectData> data = queryResult.getData();

            for (IObjectData datum : data) {
                try {
                    syncErrorData(datum);
                } catch (Exception ex) {
                    log.error("sync error data failed.", ex);
                }
            }

            query.setOffset(query.getOffset() + LIMIT);
            if (data.size() < LIMIT) {
                break;
            }
        } while (i < LOOP_LIMIT);

        return new SyncError.Result();
    }

    private void syncErrorData(IObjectData proof) {
        String proofId = proof.getId();
        String visitId = proof.get(TPMActivityProofFields.VISIT_ID, String.class);
        String storeId = proof.get(TPMActivityProofFields.STORE_ID, String.class);

        log.info("proof name : {}", proof.getName());

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(LIMIT);
        query.setOffset(0);
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);

        Filter proofIdFilter = new Filter();
        proofIdFilter.setFieldName(TPMActivityProofDetailFields.ACTIVITY_PROOF_ID);
        proofIdFilter.setOperator(Operator.EQ);
        proofIdFilter.setFieldValues(Lists.newArrayList(proofId));

        query.setFilters(Lists.newArrayList(proofIdFilter));

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.CREATE_TIME);
        order.setIsAsc(true);

        query.setOrders(Lists.newArrayList(order));

        List<IObjectData> details = serviceFacade.findBySearchQuery(User.systemUser(controllerContext.getTenantId()), ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ, query).getData();

        log.info("[{}] details count : {}", proof.getName(), details.size());

        if (details.isEmpty()) {
            return;
        }

        boolean anyAutoNotPass = details.stream().anyMatch(detail -> AUTO_NOT_PASS_ITEM.contains(detail.get(TPMActivityProofDetailFields.ACTIVITY_ITEM_ID, String.class)));
        log.info("[{}] auto not pass : {}", proof.getName(), anyAutoNotPass);

        if (anyAutoNotPass) {
            return;
        }

        IObjectData visitData = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()), visitId, "CheckinsObj");

        boolean anyNotMatch = details.stream().anyMatch(detail -> !match(visitData, detail));
        log.info("[{}] not match : {}", proof.getName(), anyNotMatch);

        if (anyNotMatch) {
            saveProofErrorRecord(visitData, storeId);
            return;
        }
        log.info("[{}] matched.", proof.getName());
    }

    private void saveProofErrorRecord(IObjectData visitData, String storeId) {
        IObjectData errorRecord = new ObjectData();

        errorRecord.setDescribeApiName("proof_error_record__c");
        errorRecord.setTenantId(controllerContext.getTenantId());
        errorRecord.setOwner(Lists.newArrayList("-10000"));
        errorRecord.set("visit_id__c", visitData.getId());
        errorRecord.set("store_id__c", storeId);
        errorRecord.set("finish_time__c", visitData.get("finish_time", Long.class));
        errorRecord.set("error_status__c", "error");
        errorRecord.set("error_type__c", "3");

        serviceFacade.saveObjectData(User.systemUser(controllerContext.getTenantId()), errorRecord);
    }

    private boolean match(IObjectData visitData, IObjectData detail) {
        String itemId = detail.get(TPMActivityProofDetailFields.ACTIVITY_ITEM_ID, String.class);

        BigDecimal actualSkuCount = detail.get("field_sku__c", BigDecimal.class);
        BigDecimal actualRowCount = detail.get(TPMActivityProofDetailFields.AMOUNT, BigDecimal.class);

        if (!API_NAME_MAP.containsKey(itemId)) {
            return true;
        }

        String dataId = visitData.get(REF_FIELD_MAP.get(itemId), String.class);
        if (Strings.isNullOrEmpty(dataId)) {
            return true;
        }

        IObjectData data = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()), dataId, API_NAME_MAP.get(itemId));
        if (Objects.isNull(data)) {
            return true;
        }

        BigDecimal skuCount = data.get(SKU_FIELD_MAP.get(itemId), BigDecimal.class);
        BigDecimal rowCount = data.get(ROW_FIELD_MAP.get(itemId), BigDecimal.class);

        if (Objects.isNull(skuCount) || Objects.isNull(rowCount)) {
            return true;
        }

        return skuCount.compareTo(actualSkuCount) == 0 && actualRowCount.compareTo(rowCount) == 0;
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Collections.emptyList();
    }
}
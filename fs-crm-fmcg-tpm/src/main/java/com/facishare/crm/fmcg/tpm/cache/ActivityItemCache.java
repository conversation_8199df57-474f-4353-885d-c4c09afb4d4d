package com.facishare.crm.fmcg.tpm.cache;

import com.facishare.crm.fmcg.tpm.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.utils.QueryDataUtil;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2023/1/11 17:37
 */
@Slf4j
@Component
@SuppressWarnings("Duplicates")
public class ActivityItemCache implements InitializingBean {

    @Resource
    private ServiceFacade serviceFacade;

    private Cache<String, Map<String, String>> activityIdNameCache;

    @Override
    public void afterPropertiesSet() {
        activityIdNameCache = CacheBuilder.newBuilder()
                .maximumSize(1000)
                .expireAfterWrite(30, TimeUnit.MINUTES)
                .build();
    }

    public Map<String, String> get(String tenantId) {
        String key = getKey(tenantId);
        Map<String, String> cache = activityIdNameCache.getIfPresent(key);
        if (Objects.nonNull(cache)) {
            return cache;
        } else {
            SearchTemplateQuery query = new SearchTemplateQuery();

            query.setLimit(-1);
            query.setOffset(0);
            query.setSearchSource("db");
            query.setNeedReturnCountNum(false);

            query.setFilters(Lists.newArrayList());

            Map<String, String> data = QueryDataUtil.find(
                    serviceFacade,
                    tenantId,
                    ApiNames.TPM_ACTIVITY_ITEM_OBJ,
                    query,
                    Lists.newArrayList("_id", "name")).stream()
                    .collect(Collectors.toMap(DBRecord::getId, IObjectData::getName, (oldOne, newOne) -> oldOne));

            activityIdNameCache.put(key, data);
            return data;
        }
    }

    private String getKey(String tenantId) {
        return String.format("fmcg_tpm.activity_item_cache.%s", tenantId);
    }
}

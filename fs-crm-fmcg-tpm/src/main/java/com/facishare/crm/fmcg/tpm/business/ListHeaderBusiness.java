package com.facishare.crm.fmcg.tpm.business;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.github.autoconf.ConfigFactory;
import de.lab4inf.math.util.Strings;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/5/19 上午11:07
 */
public class ListHeaderBusiness {

    private static Map<String, Map<String, List<String>>> LIST_HEADER_CONFIG_MAP = new HashMap<>();

    private static final String CONFIG_NAME = "gray-rel-fmcg";

    static {
        ConfigFactory.getConfig(CONFIG_NAME, config -> {
            String json = config.get("LIST_HEADER_FILTER_CONFIG");
            if (!Strings.isNullOrEmpty(json)) {
                Map<String, Map<String, List<String>>> map = JSON.parseObject(json, new TypeReference<Map<String, Map<String, List<String>>>>() {
                });
                LIST_HEADER_CONFIG_MAP = map;
            }
        });
    }

    public static void listHeaderFilter(String tenantId, String apiName, StandardListHeaderController.Result result) {
        List<JSONObject> buttons = (List<JSONObject>) result.getLayout().get("buttons");
        if (LIST_HEADER_CONFIG_MAP.get(tenantId) != null && LIST_HEADER_CONFIG_MAP.get(tenantId).get(apiName) != null) {
            List<String> filterList = LIST_HEADER_CONFIG_MAP.get(tenantId).get(apiName);
            result.getLayout().put("buttons", buttons.stream().filter(v -> !filterList.contains(v.getString("action"))).collect(Collectors.toList()));
        } else {
            result.getLayout().put("buttons", buttons.stream().filter(v -> {
                if (apiName.equals("TPMActivityBudgetDetailObj"))
                    return !v.getString("action").equals("Add") && !v.getString("action").equals("Edit") && !v.getString("action").equals("Import");
                return !v.getString("action").equals("Add") && !v.getString("action").equals("Import");
            }).collect(Collectors.toList()));
        }
    }
}

package com.facishare.crm.fmcg.tpm.api.proof;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.metadata.api.IObjectData;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public interface UnauditedStoreFilter {

    @Data
    @ToString
    class Arg implements Serializable {

        /**
         * begin time
         */
        private Long begin;

        /**
         * end time
         */
        private Long end;

        /**
         * dealer id
         */
        @SerializedName("dealer_id")
        @JSONField(name = "dealer_id")
        @JsonProperty("dealer_id")
        private String dealerId;

        /**
         * activity id
         */
        @SerializedName("activity_id")
        @JSONField(name = "activity_id")
        @JsonProperty("activity_id")
        private String activityId;

        @SerializedName("id_list")
        @JSONField(name = "id_list")
        @JsonProperty("id_list")
        private List<String> idList;
    }

    @Data
    @ToString
    class Result implements Serializable {

        private Map<String, UnauditedStoreVO> data = new HashMap<>();

        @SerializedName("total_proof_count")
        @JSONField(name = "total_proof_count")
        @JsonProperty("total_proof_count")
        private long totalProofCount;


        @SerializedName("audited_proof_count")
        @JSONField(name = "audited_proof_count")
        @JsonProperty("audited_proof_count")
        private long auditedProofCount;


        @SerializedName("unaudited_proof_count")
        @JSONField(name = "unaudited_proof_count")
        @JsonProperty("unaudited_proof_count")
        private long unauditedProofCount;
    }

    @Data
    @ToString
    class UnauditedStoreVO implements Serializable {

        @SerializedName("store_data")
        @JSONField(name = "store_data")
        @JsonProperty("store_data")
        private IObjectData storeData;

        /**
         * activity_list
         * proof_audit
         * proof
         * no_activity
         */
        @SerializedName("navigate_strategy")
        @JSONField(name = "navigate_strategy")
        @JsonProperty("navigate_strategy")
        private String navigateStrategy;

        @SerializedName("proof_data")
        @JSONField(name = "proof_data")
        @JsonProperty("proof_data")
        private IObjectData proofData;
    }
}
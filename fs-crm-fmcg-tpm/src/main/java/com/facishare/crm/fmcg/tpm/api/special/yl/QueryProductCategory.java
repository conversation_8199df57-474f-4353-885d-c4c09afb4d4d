package com.facishare.crm.fmcg.tpm.api.special.yl;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/9/1 11:01
 */
public interface QueryProductCategory {

    @Data
    @ToString
    class Arg {

        @SerializedName("activity_id")
        @JSONField(name = "activity_id")
        @JsonProperty("activity_id")
        private String activityId;
    }

    @Data
    @ToString
    @Builder
    @EqualsAndHashCode(callSuper = false)
    class Result {

        private List<ProductCategory> data;
    }

    @Data
    @ToString
    @Builder
    class ProductCategory {

        private String name;

        private String code;
    }
}

package com.facishare.crm.fmcg.tpm.api.visit;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 12/9/20 2:41 PM
 */

@Data
@ToString
public class VisitActionDataDTO {

    @JSONField(name = "activity_proof_list")
    private List<ActivityProofDTO> activityProofList;

    @JSONField(name = "activity_proof_list_size")
    private long activityProofListSize;
}

package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.api.enumeration.LogType;
import com.facishare.crm.fmcg.tpm.api.log.LogData;
import com.facishare.crm.fmcg.tpm.api.method.IdempotentArgBase;
import com.facishare.crm.fmcg.tpm.apiname.*;
import com.facishare.crm.fmcg.tpm.business.BudgetService;
import com.facishare.crm.fmcg.tpm.business.OperateInfoService;
import com.facishare.crm.fmcg.tpm.service.*;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/11/11 下午5:03
 */

@SuppressWarnings("Duplicates")
public class TPMActivityObjBulkInvalidAction extends StandardBulkInvalidAction implements TransactionService<StandardBulkInvalidAction.Arg, StandardBulkInvalidAction.Result> {

    private static final BudgetService budgetService = SpringUtil.getContext().getBean(BudgetService.class);

    private final PackTransactionProxy packTransactionProxy = SpringUtil.getContext().getBean(PackTransactionProxyImpl.class);

    private final OperateInfoService operateInfoService = SpringUtil.getContext().getBean(OperateInfoService.class);

    @Override
    protected void before(Arg arg) {

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(100);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter idListFilter = new Filter();
        idListFilter.setFieldName(TPMActivityProofFields.ACTIVITY_ID);
        idListFilter.setOperator(Operator.IN);
        idListFilter.setFieldValues(arg.getDataIds());

        Filter deletedFilter = new Filter();
        deletedFilter.setFieldName(CommonFields.IS_DELETED);
        deletedFilter.setOperator(Operator.EQ);
        deletedFilter.setFieldValues(Lists.newArrayList("false"));

        query.setFilters(Lists.newArrayList(idListFilter, deletedFilter));

        List<IObjectData> proofs = serviceFacade.findBySearchQuery(User.systemUser(actionContext.getTenantId()), ApiNames.TPM_ACTIVITY_PROOF_OBJ, query).getData();

        if (!proofs.isEmpty()) {

            List<String> activityIds = proofs.stream().map(record -> (String) record.get(TPMActivityProofFields.ACTIVITY_ID)).distinct().collect(Collectors.toList());
            List<IObjectData> data = serviceFacade.findObjectDataByIds(actionContext.getUser().getTenantId(), activityIds, ApiNames.TPM_ACTIVITY_OBJ);

            String names = data.stream().map(IObjectData::getName).collect(Collectors.joining(","));
            throw new ValidateException(I18N.text(I18NKeys.BATCH_INVALID_ACTIVITY_ERROR_ITEM_USED_BY_PROOF, names));
        }

        query.setLimit(100);
        query.setOffset(0);

        idListFilter = new Filter();
        idListFilter.setFieldName(TPMActivityAgreementFields.ACTIVITY_ID);
        idListFilter.setOperator(Operator.IN);
        idListFilter.setFieldValues(arg.getDataIds());

        deletedFilter = new Filter();
        deletedFilter.setFieldName(CommonFields.IS_DELETED);
        deletedFilter.setOperator(Operator.EQ);
        deletedFilter.setFieldValues(Lists.newArrayList("false"));

        query.setFilters(Lists.newArrayList(idListFilter, deletedFilter));

        List<IObjectData> agreements = serviceFacade.findBySearchQuery(User.systemUser(actionContext.getTenantId()), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, query).getData();

        if (!agreements.isEmpty()) {

            List<String> activityIds = agreements.stream().map(record -> (String) record.get(TPMActivityAgreementFields.ACTIVITY_ID)).distinct().collect(Collectors.toList());
            List<IObjectData> data = serviceFacade.findObjectDataByIds(actionContext.getUser().getTenantId(), activityIds, ApiNames.TPM_ACTIVITY_OBJ);

            String names = data.stream().map(IObjectData::getName).collect(Collectors.joining(","));
            throw new ValidateException(I18N.text(I18NKeys.BATCH_INVALID_ACTIVITY_ERROR_ITEM_USED_BY_AGREEMENT, names));
        }

        query.setLimit(100);
        query.setOffset(0);

        idListFilter = new Filter();
        idListFilter.setFieldName(TPMDealerActivityCostFields.ACTIVITY_ID);
        idListFilter.setOperator(Operator.IN);
        idListFilter.setFieldValues(arg.getDataIds());

        deletedFilter = new Filter();
        deletedFilter.setFieldName(CommonFields.IS_DELETED);
        deletedFilter.setOperator(Operator.EQ);
        deletedFilter.setFieldValues(Lists.newArrayList("false"));

        query.setFilters(Lists.newArrayList(idListFilter, deletedFilter));

        List<IObjectData> costs = serviceFacade.findBySearchQuery(User.systemUser(actionContext.getTenantId()), ApiNames.TPM_DEALER_ACTIVITY_COST, query).getData();

        if (!costs.isEmpty()) {
            List<String> activityIds = costs.stream().map(record -> (String) record.get(TPMDealerActivityCostFields.ACTIVITY_ID)).distinct().collect(Collectors.toList());
            List<IObjectData> data = serviceFacade.findObjectDataByIds(actionContext.getUser().getTenantId(), activityIds, ApiNames.TPM_ACTIVITY_OBJ);
            String names = data.stream().map(IObjectData::getName).collect(Collectors.joining(","));
            throw new ValidateException(I18N.text(I18NKeys.BATCH_INVALID_ACTIVITY_ERROR_ITEM_USED_BY_AGREEMENT, names));
        }

        List<IObjectData> activities = serviceFacade.findObjectDataByIds(actionContext.getTenantId(),arg.getDataIds(),ApiNames.TPM_ACTIVITY_OBJ);
        List<String> names = Lists.newArrayList();
        for (IObjectData activity: activities){
            if(Strings.isNullOrEmpty((String)activity.get(TPMActivityFields.BUDGET_TABLE)))
                names.add(activity.getName());
        }
        if(names.size()>1)
            throw new ValidateException(String.format(I18N.text(I18NKeys.ONLY_SUPPORT_INVALID_SINGLE_ACTIVITY),names.toString()));
        super.before(arg);
    }

    @Override
    protected Result doAct(Arg arg) {
        return packTransactionProxy.packAct(this, arg);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        int size = arg.getDataIds().size() - result.getFailureObjectDataList().size();
        for (int i = 0; i < size; i++) {
            BuryService.asyncTpmLog(Integer.valueOf(actionContext.getTenantId()), actionContext.getUser().getUserIdInt(), BuryModule.TPM.TPM_ACTIVITY, BuryOperation.DELETE);
        }
        List<ObjectDataDocument> all = new ArrayList<>(result.getObjectDataList());
        all.removeAll(result.getFailureObjectDataList());
        all.forEach(v -> {
            String type = (String) v.get("record_type");
            if ("default__c".equals(type)) {
                BuryService.asyncTpmLog(Integer.valueOf(actionContext.getTenantId()), actionContext.getUser().getUserIdInt(), BuryModule.TPM.TPM_ACTIVITY, BuryOperation.DELETE_CUSTOM);
            }
            if ("dealer_activity__c".equals(type)) {
                BuryService.asyncTpmLog(Integer.valueOf(actionContext.getTenantId()), actionContext.getUser().getUserIdInt(), BuryModule.TPM.TPM_ACTIVITY, BuryOperation.DELETE_PERSONAL);
            }
            BuryService.asyncTpmLog(Integer.valueOf(actionContext.getTenantId()), actionContext.getUser().getUserIdInt(), BuryModule.TPM.TPM_ACTIVITY, BuryOperation.DELETE);

        });

        return super.after(arg, result);
    }

    @Override
    public Result doActTransaction(Arg arg) {

        List<String> ineffectiveActivityIds = new ArrayList<>();
        List<IObjectData> activities = serviceFacade.findObjectDataByIds(actionContext.getTenantId(),arg.getDataIds(),ApiNames.TPM_ACTIVITY_OBJ);
        activities.forEach(activity->{
            if("ineffective".equals(activity.get(CommonFields.LIFE_STATUS)))
                ineffectiveActivityIds.add(activity.getId());
        });
        Result result = super.doAct(arg);
        if (budgetService.isOpenBudge(Integer.parseInt(actionContext.getTenantId()))) {
            List<ObjectDataDocument> all = new ArrayList<>(result.getObjectDataList());
            all.removeAll(result.getFailureObjectDataList());
            all.stream().filter(ac->!ineffectiveActivityIds.contains(ac.getId())).forEach(this::dealBudget);
        }
        return result;
    }

    private void dealBudget(ObjectDataDocument document) {
        String budgetId = (String) document.getOrDefault(TPMActivityFields.BUDGET_TABLE, "");

        String closeStatus = (String)document.get(TPMActivityFields.CLOSE_STATUS);
        if(TPMActivityFields.CLOSE_STATUS__CLOSED.equals(closeStatus)){
            return;
        }
        if (!"ineffective".equals(document.get(CommonFields.LIFE_STATUS))&& !Strings.isNullOrEmpty(budgetId)) {
            budgetService.tryLockBudget(actionContext, budgetId);
            double activityAmount = Double.parseDouble((String) document.getOrDefault(TPMActivityFields.ACTIVITY_AMOUNT, "0.0"));
            double actualAmount = Double.parseDouble((String) document.getOrDefault(TPMActivityFields.ACTIVITY_ACTUAL_AMOUNT, "0.0"));
            IObjectData budget = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), budgetId, ApiNames.TPM_ACTIVITY_BUDGET);
            double availableAmount = Double.parseDouble((String) budget.get(TPMActivityBudgetFields.AVAILABLE_AMOUNT));

           /* Map<String, Double> amountMap = new HashMap<>();
            double availableAmount = budgetService.getBudgetAvailableAmount(actionContext.getTenantId(), budget, amountMap);
*/
            LogData logData = LogData.builder().data(JSON.toJSONString(document)).build();
            logData.setAttribute("budget", budget);
            String logId = operateInfoService.log(actionContext.getTenantId(), LogType.INVALID.value(), JSON.toJSONString(logData), actionContext.getUser().getUserId(), ApiNames.TPM_ACTIVITY_OBJ, document.getId(), this.needTriggerApprovalFlow());

            budgetService.addBudgetDetail(actionContext.getTenantId(), actionContext.getUser().getUserId(),
                    "2",
                    budgetId,
                    String.format("个案活动作废：「%s」作废", document.get("name")),
                    activityAmount - actualAmount,
                    availableAmount,
                    availableAmount + activityAmount,
                    System.currentTimeMillis(),
                    String.format("LogId:%s-traceId:%s", logId, TraceContext.get().getTraceId()),
                    document.getId(),
                    TraceContext.get().getTraceId(),
                    IdempotentArgBase.builder().idempotentKey(actionContext.getPostId() + ":" + budgetId).build());
            budgetService.calculateBudget(actionContext.getTenantId(), (String) document.getOrDefault(TPMActivityFields.BUDGET_TABLE, ""));

        }
    }

    @Override
    protected void finallyDo() {
        try {
            super.finallyDo();
        } finally {
            budgetService.unLockBudget(actionContext);
        }
    }
}

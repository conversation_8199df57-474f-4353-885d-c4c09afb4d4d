package com.facishare.crm.fmcg.tpm.common;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.google.common.base.Strings;
import lombok.experimental.UtilityClass;

import java.util.Objects;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/6/16 15:31
 */
@UtilityClass
public class EasyValidator {

    public static void notNullOrEmpty(String data, String errorMessage) {
        if (Strings.isNullOrEmpty(data)) {
            throw new ValidateException(errorMessage);
        }
    }

    public static void notNullOrEmpty(String data, String errorMessage, int errorCode) {
        if (Strings.isNullOrEmpty(data)) {
            throw new ValidateException(errorMessage, errorCode);
        }
    }

    public static void notNull(Object data, String errorMessage) {
        if (Objects.isNull(data)) {
            throw new ValidateException(errorMessage);
        }
    }

    public static void notNull(Object data, String errorMessage, int errorCode) {
        if (Objects.isNull(data)) {
            throw new ValidateException(errorMessage, errorCode);
        }
    }
}
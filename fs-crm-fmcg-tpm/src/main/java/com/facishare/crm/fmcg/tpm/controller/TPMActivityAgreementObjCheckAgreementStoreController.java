package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.apiname.*;
import com.facishare.crm.fmcg.tpm.business.StoreBusiness;
import com.facishare.crm.fmcg.tpm.service.OrganizationService;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fmcg.framework.http.CustomFunctionProxy;
import com.fmcg.framework.http.contract.function.TriggerCustomFunctionByObjectData;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import de.lab4inf.math.util.Strings;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.collections.MapUtils;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/3/18 下午4:39
 */
public class TPMActivityAgreementObjCheckAgreementStoreController extends PreDefineController<TPMActivityAgreementObjCheckAgreementStoreController.Arg, TPMActivityAgreementObjCheckAgreementStoreController.Result> {


    private StoreBusiness storeBusiness = SpringUtil.getContext().getBean(StoreBusiness.class);

    private static final OrganizationService organizationService = SpringUtil.getContext().getBean(OrganizationService.class);

    private static final CustomFunctionProxy customFunctionProxy = SpringUtil.getContext().getBean(CustomFunctionProxy.class);

    private static Map<String, JSONObject> FUNCTION_MAP = new HashMap<>();

    static {
        ConfigFactory.getConfig("gray-rel-fmcg", iConfig -> {
            String json = iConfig.get("YINLU_FUNCTION_MAP");
            if (Strings.isNullOrEmpty(json)) {
                return;
            }
            JSONObject map = JSONObject.parseObject(json);
            Map<String, JSONObject> changeMap = new HashMap<>();
            map.keySet().forEach(key -> changeMap.put(key, map.getJSONObject(key)));
            FUNCTION_MAP = changeMap;
        });
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList();
    }

    @Override
    protected Result doService(Arg arg) {

        TriggerCustomFunctionByObjectData.Arg triggerArg = new TriggerCustomFunctionByObjectData.Arg();
        JSONObject function = FUNCTION_MAP.get(controllerContext.getTenantId());
        if (function == null) {
            log.info("get func is null.arg:{}", arg);
            return new Result(true);
        }

        triggerArg.setApiName(function.getString("api_name"));
        triggerArg.setNameSpace(function.getString("namespace"));
        triggerArg.setBindingObjectAPIName(function.getString("object_api_name"));
        triggerArg.setObjectData(function.getJSONObject("object_data"));
        triggerArg.setDetails(function.getObject("detail_map", Map.class));

        if (MapUtils.isEmpty(triggerArg.getObjectData())) {
            Map<String, Object> map = new HashMap<>();
            triggerArg.setObjectData(map);
        }
        triggerArg.getObjectData().put(TPMActivityAgreementFields.STORE_ID, arg.getStoreId());
        triggerArg.getObjectData().put("object_describe_api_name", function.getString("object_api_name"));

        TriggerCustomFunctionByObjectData.Result triggerResult = customFunctionProxy.triggerByObjectData(Integer.parseInt(controllerContext.getTenantId()), controllerContext.getUser().getUserIdInt(), triggerArg);
        if (triggerResult.getErrCode() != 0 || !triggerResult.getResult().getSuccess()) {
            log.info("trigger function err.arg:{},rst:{}", triggerArg, triggerResult);
            return new Result(true);
        }
        JSONObject filterList = (JSONObject) triggerResult.getResult().getFunctionResult();
        if(filterList == null){
            log.info("empty .arg:{},rst:{}", triggerArg, triggerResult);
            return new Result(false);
        }
        SqlSplit sqlSplit = new SqlSplit();
        fillSql(filterList, sqlSplit);

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setOffset(0);
        query.setLimit(-1);
        query.setSearchSource("db");

        query.setFilters(sqlSplit.getFilters());
        query.setPattern(sqlSplit.getSqlBuilder().toString());
        List<IObjectData> activities = CommonUtils.queryData(serviceFacade, User.systemUser(controllerContext.getTenantId()), ApiNames.TPM_ACTIVITY_OBJ, query);

        Set<String> ids = activities.stream().map(DBRecord::getId).collect(Collectors.toSet());
        List<IObjectData> aggList = getAgg(ids);

        aggList.forEach(v -> ids.remove(v.get(TPMActivityAgreementFields.ACTIVITY_ID)));
        if (CollectionUtils.isEmpty(ids)) {
            return new Result(false);
        }
        return new Result(true);

    }

    private void fillSql(JSONObject filterList, SqlSplit sqlSplit) {
        int height = filterList.getInteger("height");
        String type = filterList.getString("type").toLowerCase();
        StringBuilder sqlBuilder = sqlSplit.getSqlBuilder();
        sqlBuilder.append(" ").append("(");
        JSONArray array = filterList.getJSONArray("templates");
        if (height != 1) {
            for (int i = 0; i < array.size(); i++) {
                JSONObject template = array.getJSONObject(i);
                fillSql(template, sqlSplit);
                if (i != array.size() - 1) {
                    sqlBuilder.append(' ').append(type);
                }
            }
        }else {
            for (int i = 0; i < array.size(); i++) {
                JSONObject template = array.getJSONObject(i);
                IFilter filter = JSON.parseObject(template.getString("filter"),Filter.class);
                IFilter realFilter = new Filter();
                realFilter.setFieldName(filter.getFieldName());
                realFilter.setFieldValues(filter.getFieldValues());
                realFilter.setOperator(filter.getOperator());
                sqlSplit.getFilters().add(realFilter);
                sqlBuilder.append(' ').append(sqlSplit.count++);
                if (i != array.size() - 1) {
                    sqlBuilder.append(' ').append(type);
                }
            }
        }
        if (sqlBuilder.charAt(sqlBuilder.length() - 1) != '(') {
            sqlBuilder.append(')');
        } else {
            sqlBuilder.deleteCharAt(sqlBuilder.length() - 1);
        }
    }


    private Result filterV1(Arg arg) {

        IObjectData store = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()), arg.getStoreId(), ApiNames.ACCOUNT_OBJ);
        String dealerId = storeBusiness.findDealerId(controllerContext.getTenantId(), store);

        List<IObjectData> activities = queryActivity(dealerId);

        Set<String> usefulActivity = new HashSet<>();
        for (IObjectData activity : activities) {
            String json = activity.get(TPMActivityFields.STORE_RANGE, String.class, "{\"type\":\"ALL\",\"value\":\"ALL\"}");
            String dealerIdInActivity = activity.get(TPMActivityFields.DEALER_ID, String.class);
            if (!Strings.isNullOrEmpty(dealerIdInActivity)) {
                if (dealerIdInActivity.equals(dealerId)) {
                    usefulActivity.add(activity.getId());
                    continue;
                }
            }
            JSONObject storeRange = JSON.parseObject(json);
            switch (storeRange.getString("type")) {
                case "FIXED":
                    if (CollectionUtils.isEmpty(queryActivityStore(activity.getId(), arg.getStoreId()))) {
                        continue;
                    }
                    break;
                case "CONDITION":
                    if (CollectionUtils.isEmpty(queryConditionStore(storeRange, arg.getStoreId()))) {
                        continue;
                    }
                    break;
                default:
            }
            usefulActivity.add(activity.getId());
        }

        Result result = new Result();
        result.setShow(false);
        if (CollectionUtils.isEmpty(usefulActivity)) {
            return result;
        }


        List<IObjectData> aggList = getAgg(new ArrayList<>(usefulActivity));

        aggList.forEach(v -> usefulActivity.remove(v.get(TPMActivityAgreementFields.ACTIVITY_ID)));
        if (CollectionUtils.isEmpty(usefulActivity)) {
            return result;
        }
        result.setShow(true);
        return result;

    }

    private List<IObjectData> getAgg(Collection<String> ids) {

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(2000);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter activityFilter = new Filter();
        activityFilter.setFieldName(TPMActivityAgreementFields.ACTIVITY_ID);
        activityFilter.setOperator(Operator.IN);
        activityFilter.setFieldValues(new ArrayList<>(ids));

        Filter storeFilter = new Filter();
        storeFilter.setFieldName(TPMActivityAgreementFields.STORE_ID);
        storeFilter.setOperator(Operator.EQ);
        storeFilter.setFieldValues(Lists.newArrayList(arg.getStoreId()));

        query.setFilters(Lists.newArrayList(activityFilter, storeFilter));

        return serviceFacade.aggregateFindBySearchQuery(controllerContext.getTenantId(), query, ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, TPMActivityAgreementFields.ACTIVITY_ID, "count", "");
    }


    private List<IObjectData> queryActivity(String dealerId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        long now = System.currentTimeMillis();

        Filter beginTimeFilter = new Filter();
        beginTimeFilter.setFieldName(TPMActivityFields.BEGIN_DATE);
        beginTimeFilter.setOperator(Operator.LTE);
        beginTimeFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));


        Filter endTimeFilter = new Filter();
        endTimeFilter.setFieldName(TPMActivityFields.END_DATE);
        endTimeFilter.setOperator(Operator.GTE);
        endTimeFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

        Filter agreementFilter = new Filter();
        agreementFilter.setFieldName(TPMActivityFields.IS_AGREEMENT_REQUIRED);
        agreementFilter.setOperator(Operator.EQ);
        agreementFilter.setFieldValues(Lists.newArrayList("true"));

        List<Integer> departmentIds = organizationService.getDepartmentIds(controllerContext.getUser().getTenantIdInt(), controllerContext.getUser().getUserIdInt());

        Filter departmentFilter = new Filter();
        departmentFilter.setFieldName(TPMActivityFields.DEPARTMENT_RANGE);
        departmentFilter.setOperator(Operator.IN);
        departmentFilter.setFieldValues(departmentIds.stream().map(String::valueOf).collect(Collectors.toList()));

        query.setFilters(Lists.newArrayList(beginTimeFilter, endTimeFilter, agreementFilter, departmentFilter));

        StringBuilder pattern = new StringBuilder(" 1 and 2 and 3 and 4 ");

        if (Strings.isNullOrEmpty(dealerId)) {
            Filter dealerActivityFilter = new Filter();
            dealerActivityFilter.setFieldName(TPMActivityFields.DEALER_ID);
            dealerActivityFilter.setOperator(Operator.IS);
            dealerActivityFilter.setFieldValues(Lists.newArrayList());
            query.getFilters().add(dealerActivityFilter);
            pattern.append(" and 5 ");
        } else {
            Filter dealerActivityFilter = new Filter();
            dealerActivityFilter.setFieldName(TPMActivityFields.DEALER_ID);
            dealerActivityFilter.setOperator(Operator.EQ);
            dealerActivityFilter.setFieldValues(Lists.newArrayList(dealerId));

            Filter dealerActivityFilter2 = new Filter();
            dealerActivityFilter2.setFieldName(TPMActivityFields.DEALER_ID);
            dealerActivityFilter2.setOperator(Operator.IS);
            dealerActivityFilter2.setFieldValues(Lists.newArrayList());

            query.getFilters().add(dealerActivityFilter);
            query.getFilters().add(dealerActivityFilter2);

            pattern.append(" and (5 or 6)");
        }
        query.setPattern(pattern.toString());

        return CommonUtils.queryData(serviceFacade, User.systemUser(controllerContext.getTenantId()), ApiNames.TPM_ACTIVITY_OBJ, query);
    }

    private List<IObjectData> queryActivityStore(String activityId, String storeId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter activityFilter = new Filter();
        activityFilter.setFieldName(TPMActivityStoreFields.ACTIVITY_ID);
        activityFilter.setOperator(Operator.EQ);
        activityFilter.setFieldValues(Lists.newArrayList(activityId));

        Filter storeIdFilter = new Filter();
        storeIdFilter.setFieldName(TPMActivityStoreFields.STORE_ID);
        storeIdFilter.setOperator(Operator.EQ);
        storeIdFilter.setFieldValues(Lists.newArrayList(storeId));

        query.setFilters(Lists.newArrayList(activityFilter, storeIdFilter));
        return serviceFacade.findBySearchQuery(User.systemUser(controllerContext.getTenantId()), ApiNames.TPM_ACTIVITY_STORE_OBJ, query).getData();
    }


    private List<IObjectData> queryConditionStore(JSONObject storeRange, String storeId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(1);
        query.setOffset(0);
        query.setSearchSource("db");

        List<Wheres> conditionWheres = new ArrayList<>();
        String valueJson = storeRange.getString("value");
        JSONArray wheres = JSON.parseArray(valueJson);
        for (int whereIndex = 0; whereIndex < wheres.size(); whereIndex++) {
            JSONObject where = wheres.getJSONObject(0);
            Wheres conditionWhere = new Wheres();
            conditionWhere.setConnector(where.getString("connector"));
            conditionWhere.setFilters(Lists.newArrayList());
            JSONArray filters = where.getJSONArray("filters");
            for (int filterIndex = 0; filterIndex < filters.size(); filterIndex++) {
                JSONObject filter = filters.getJSONObject(filterIndex);
                Filter conditionFilter = new Filter();
                conditionFilter.setFieldName(filter.getString("field_name"));
                conditionFilter.setIndexName(filter.getString("index_name"));
                conditionFilter.setFieldValueType(filter.getString("field_value_type"));
                conditionFilter.setOperator(Operator.valueOf(filter.getString("operator")));
                conditionFilter.setFieldValues(filter.getJSONArray("field_values").toJavaList(String.class));
                conditionFilter.setConnector(filter.getString("connector"));
                conditionFilter.setValueType(filter.getInteger("value_type"));
                conditionFilter.setRefDescribeApiName(filter.getString("ref_describe_api_name"));
                conditionFilter.setRefFieldApiName(filter.getString("ref_field_api_name"));
                conditionFilter.setIsCascade(filter.getBoolean("is_cascade"));
                conditionFilter.setIsMasterField(filter.getBoolean("is_master_field"));
                conditionFilter.setFilterGroup(filter.getString("filterGroup"));
                conditionWhere.getFilters().add(conditionFilter);
            }

            Filter idFilter = new Filter();
            idFilter.setFieldName("_id");
            idFilter.setOperator(Operator.EQ);
            idFilter.setFieldValues(Lists.newArrayList(storeId));
            idFilter.setConnector("AND");

            conditionWhere.getFilters().add(idFilter);
            conditionWheres.add(conditionWhere);
        }
        query.setWheres(conditionWheres);
        return serviceFacade.findBySearchQuery(User.systemUser(controllerContext.getTenantId()), ApiNames.ACCOUNT_OBJ, query).getData();
    }


    @Data
    @ToString
    static class Arg implements Serializable {

        @SerializedName("store_id")
        @JSONField(name = "store_id")
        @JsonProperty("store_id")
        private String storeId;
    }


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @ToString
    static class Result implements Serializable {

        @SerializedName("show")
        @JSONField(name = "show")
        @JsonProperty("show")
        private Boolean show;
    }

    @Data
    @ToString
    static class SqlSplit {
        private StringBuilder sqlBuilder;
        private Integer count;
        private List<IFilter> filters;

        SqlSplit() {
            sqlBuilder = new StringBuilder();
            count = 1;
            filters = new ArrayList<>();
        }
    }
}

package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.tpm.api.proof.Enable;
import com.facishare.crm.fmcg.tpm.apiname.*;
import com.facishare.crm.fmcg.tpm.business.StoreBusiness;
import com.facishare.crm.fmcg.tpm.common.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.service.OrganizationService;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 12/2/20 4:12 PM
 */
@SuppressWarnings("Duplicates")
public class TPMActivityProofObjEnableController extends PreDefineController<Enable.Arg, Enable.Result> {

    private static final OrganizationService organizationService = SpringUtil.getContext().getBean(OrganizationService.class);
    private static final StoreBusiness storeBusiness = SpringUtil.getContext().getBean(StoreBusiness.class);

    @Override
    protected Enable.Result doService(Enable.Arg arg) {
        log.info("arg : {}", arg);

        Enable.Result enableResult = new Enable.Result();
        enableResult.setEnable(false);

        if (TPMGrayUtils.skipEnableCheck(controllerContext.getTenantId())) {
            enableResult.setEnable(true);
            return enableResult;
        }

        if (Strings.isNullOrEmpty(arg.getStoreId())) {
            return enableResult;
        }

        IObjectData storeData = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()), arg.getStoreId(), ApiNames.ACCOUNT_OBJ);
        if (storeData == null) {
            return enableResult;
        }

        String dealerId = storeBusiness.findDealerId(controllerContext.getTenantId(), storeData);
        if (!TPMGrayUtils.isYinLu(controllerContext.getTenantId()) && !TPMGrayUtils.dealerProofEnable(controllerContext.getTenantId())) {
            if (Strings.isNullOrEmpty(dealerId)) {
                return enableResult;
            }
            IObjectData dealerData = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()), dealerId, ApiNames.ACCOUNT_OBJ);
            if (dealerData == null) {
                return enableResult;
            }
        }

        enableResult.setEnable(enableCheck(controllerContext, arg, storeData, dealerId));

        log.info("result : {}", enableResult);

        return enableResult;
    }

    private boolean enableCheck(ControllerContext context, Enable.Arg arg, IObjectData storeData, String dealerId) {

        boolean isApplets = controllerContext.getUser().isOutUser();
        List<Integer> departmentIds = isApplets ? Lists.newArrayList() : organizationService.getDepartmentIds(controllerContext.getUser().getTenantIdInt(), controllerContext.getUser().getUserIdInt());
        if (!isApplets && CollectionUtils.isEmpty(departmentIds)) {
            return false;
        }

        long now = System.currentTimeMillis();

        List<IObjectData> dealerActivity = queryActivityByDealerStore(context, now, departmentIds, arg.getStoreId());
        if (!dealerActivity.isEmpty()) {
            return true;
        }
        List<IObjectData> activities;

        if (Strings.isNullOrEmpty(dealerId)) {
            activities = queryActivityByStore(context, now, departmentIds);
        } else {
            activities = queryActivityByStore(context, now, departmentIds, dealerId);
        }

        if (TPMGrayUtils.isYinLu(context.getTenantId())) {
            activities = queryHasAgreementActivity(context, now, storeData, activities);
        }

        for (IObjectData activity : activities) {
            String storeRangeJson = (String) activity.get(TPMActivityFields.STORE_RANGE);
            if (Strings.isNullOrEmpty(storeRangeJson)) {
                continue;
            }
            JSONObject storeRange = JSON.parseObject(storeRangeJson);
            String type = storeRange.getString("type");
            switch (type) {
                case "FIXED":
                    if (CollectionUtils.isEmpty(queryActivityStore(context, activity.getId(), arg.getStoreId()))) {
                        continue;
                    }
                    break;
                case "CONDITION":
                    if (CollectionUtils.isEmpty(queryConditionStore(context, storeRange, arg.getStoreId()))) {
                        continue;
                    }
                    break;
                default:
                case "ALL":
                    break;
            }
            return true;
        }
        return false;
    }

    /**
     * query activities that has agreement related
     * <p>
     * ## YINLU custom
     * @param context    controller context
     * @param now        current time stamp
     * @param storeData  store object data
     * @param activities all activity list
     *
     * @return has agreement related activities
     */
    private List<IObjectData> queryHasAgreementActivity(ControllerContext context, long now, IObjectData storeData, List<IObjectData> activities) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setNeedReturnCountNum(false);
        query.setSearchSource("db");

        Filter storeIdFilter = new Filter();
        storeIdFilter.setFieldName(TPMActivityAgreementFields.STORE_ID);
        storeIdFilter.setOperator(Operator.EQ);
        storeIdFilter.setFieldValues(Lists.newArrayList(storeData.getId()));

        Filter activityFilter = new Filter();
        activityFilter.setFieldName(TPMActivityAgreementFields.ACTIVITY_ID);
        activityFilter.setOperator(Operator.IN);
        activityFilter.setFieldValues(Lists.newArrayList(activities.stream().map(DBRecord::getId).collect(Collectors.toList())));

        Filter beginTimeFilter = new Filter();
        beginTimeFilter.setFieldName(TPMActivityAgreementFields.BEGIN_DATE);
        beginTimeFilter.setOperator(Operator.LTE);
        beginTimeFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

        Filter endTimeFilter = new Filter();
        endTimeFilter.setFieldName(TPMActivityAgreementFields.END_DATE);
        endTimeFilter.setOperator(Operator.GTE);
        endTimeFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

        query.setFilters(Lists.newArrayList(storeIdFilter, activityFilter, beginTimeFilter, endTimeFilter));

        // query agreements that related to this store and activity
        List<IObjectData> agreements = CommonUtils.queryData(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, query);
        List<String> hasAgreementActivityIds = agreements.stream().map(agreement -> agreement.get(TPMActivityAgreementFields.ACTIVITY_ID, String.class)).collect(Collectors.toList());

        return activities.stream().filter(activity -> hasAgreementActivityIds.contains(activity.getId())).collect(Collectors.toList());
    }

    private List<IObjectData> queryActivityByDealerStore(ControllerContext context, long now, List<Integer> departmentIds, String dealerId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnCountNum(false);
        query.setSearchSource("db");

        Filter dealerIdFilter = new Filter();
        dealerIdFilter.setFieldName(TPMActivityFields.DEALER_ID);
        dealerIdFilter.setOperator(Operator.EQ);
        dealerIdFilter.setFieldValues(Lists.newArrayList(dealerId));

        Filter beginDateFilter = new Filter();
        beginDateFilter.setFieldName(TPMActivityFields.BEGIN_DATE);
        beginDateFilter.setOperator(Operator.LT);
        beginDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

        Filter endDateFilter = new Filter();
        endDateFilter.setFieldName(TPMActivityFields.END_DATE);
        endDateFilter.setOperator(Operator.GT);
        endDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

        Filter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.EQ);
        lifeStatusFilter.setFieldValues(Lists.newArrayList("normal"));

        if (!controllerContext.getUser().isOutUser() && !TPMGrayUtils.denyDepartmentFilterOnActivity(context.getTenantId())) {
            Filter departmentRangeFilter = new Filter();
            departmentRangeFilter.setFieldName(TPMActivityFields.DEPARTMENT_RANGE);
            departmentRangeFilter.setOperator(Operator.IN);
            departmentRangeFilter.setFieldValues(departmentIds.stream().map(Object::toString).collect(Collectors.toList()));
            query.setFilters(Lists.newArrayList(dealerIdFilter, beginDateFilter, endDateFilter, departmentRangeFilter, lifeStatusFilter));
        } else {
            query.setFilters(Lists.newArrayList(dealerIdFilter, beginDateFilter, endDateFilter, lifeStatusFilter));
        }
        return CommonUtils.queryData(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_OBJ, query);
    }

    private List<IObjectData> queryActivityByStore(ControllerContext context, long now, List<Integer> departmentIds, String dealerId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnCountNum(false);
        query.setSearchSource("db");

        Filter beginDateFilter = new Filter();
        beginDateFilter.setFieldName(TPMActivityFields.BEGIN_DATE);
        beginDateFilter.setOperator(Operator.LT);
        beginDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

        Filter endDateFilter = new Filter();
        endDateFilter.setFieldName(TPMActivityFields.END_DATE);
        endDateFilter.setOperator(Operator.GT);
        endDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

        Filter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.EQ);
        lifeStatusFilter.setFieldValues(Lists.newArrayList("normal"));

        if (!controllerContext.getUser().isOutUser() && !TPMGrayUtils.denyDepartmentFilterOnActivity(context.getTenantId())) {
            Filter departmentRangeFilter = new Filter();
            departmentRangeFilter.setFieldName(TPMActivityFields.DEPARTMENT_RANGE);
            departmentRangeFilter.setOperator(Operator.IN);
            departmentRangeFilter.setFieldValues(departmentIds.stream().map(Object::toString).collect(Collectors.toList()));
            query.setFilters(Lists.newArrayList(beginDateFilter, endDateFilter, departmentRangeFilter, lifeStatusFilter));
        } else {
            query.setFilters(Lists.newArrayList(beginDateFilter, endDateFilter, lifeStatusFilter));
        }

        Wheres dealerIdEqualWhere = new Wheres();
        dealerIdEqualWhere.setConnector("OR");
        Filter dealerIdEqualFilter = new Filter();
        dealerIdEqualFilter.setFieldName(TPMActivityFields.DEALER_ID);
        dealerIdEqualFilter.setOperator(Operator.EQ);
        dealerIdEqualFilter.setFieldValues(Lists.newArrayList(dealerId));
        dealerIdEqualWhere.setFilters(Lists.newArrayList(dealerIdEqualFilter));

        Wheres dealerIdEmptyWhere = new Wheres();
        dealerIdEmptyWhere.setConnector("OR");
        Filter dealerIdEmptyFilter = new Filter();
        dealerIdEmptyFilter.setFieldName(TPMActivityFields.DEALER_ID);
        dealerIdEmptyFilter.setOperator(Operator.IS);
        dealerIdEmptyFilter.setFieldValues(Lists.newArrayList());
        dealerIdEmptyWhere.setFilters(Lists.newArrayList(dealerIdEmptyFilter));

        query.setWheres(Lists.newArrayList(dealerIdEmptyWhere, dealerIdEqualWhere));

        return CommonUtils.queryData(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_OBJ, query);
    }

    private List<IObjectData> queryActivityByStore(ControllerContext context, long now, List<Integer> departmentIds) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnCountNum(false);
        query.setSearchSource("db");

        Filter beginDateFilter = new Filter();
        beginDateFilter.setFieldName(TPMActivityFields.BEGIN_DATE);
        beginDateFilter.setOperator(Operator.LT);
        beginDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

        Filter endDateFilter = new Filter();
        endDateFilter.setFieldName(TPMActivityFields.END_DATE);
        endDateFilter.setOperator(Operator.GT);
        endDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

        Filter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.EQ);
        lifeStatusFilter.setFieldValues(Lists.newArrayList("normal"));

        if (!controllerContext.getUser().isOutUser() && !TPMGrayUtils.denyDepartmentFilterOnActivity(context.getTenantId())) {

            Filter departmentRangeFilter = new Filter();
            departmentRangeFilter.setFieldName(TPMActivityFields.DEPARTMENT_RANGE);
            departmentRangeFilter.setOperator(Operator.IN);
            departmentRangeFilter.setFieldValues(departmentIds.stream().map(Object::toString).collect(Collectors.toList()));

            query.setFilters(Lists.newArrayList(beginDateFilter, endDateFilter, departmentRangeFilter, lifeStatusFilter));
        } else {
            query.setFilters(Lists.newArrayList(beginDateFilter, endDateFilter, lifeStatusFilter));
        }

        return CommonUtils.queryData(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_OBJ, query);
    }

    private List<IObjectData> queryActivityStore(ControllerContext context, String activityId, String storeId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter activityFilter = new Filter();
        activityFilter.setFieldName(TPMActivityStoreFields.ACTIVITY_ID);
        activityFilter.setOperator(Operator.EQ);
        activityFilter.setFieldValues(Lists.newArrayList(activityId));

        Filter storeIdFilter = new Filter();
        storeIdFilter.setFieldName(TPMActivityStoreFields.STORE_ID);
        storeIdFilter.setOperator(Operator.EQ);
        storeIdFilter.setFieldValues(Lists.newArrayList(storeId));

        query.setFilters(Lists.newArrayList(activityFilter, storeIdFilter));
        return serviceFacade.findBySearchQuery(User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_STORE_OBJ, query).getData();
    }

    private List<IObjectData> queryConditionStore(ControllerContext context, JSONObject storeRange, String storeId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(1);
        query.setOffset(0);
        query.setSearchSource("db");

        List<Wheres> conditionWheres = new ArrayList<>();
        String valueJson = storeRange.getString("value");
        JSONArray wheres = JSON.parseArray(valueJson);
        for (int whereIndex = 0; whereIndex < wheres.size(); whereIndex++) {
            JSONObject where = wheres.getJSONObject(0);
            Wheres conditionWhere = new Wheres();
            conditionWhere.setConnector(where.getString("connector"));
            conditionWhere.setFilters(Lists.newArrayList());
            JSONArray filters = where.getJSONArray("filters");
            for (int filterIndex = 0; filterIndex < filters.size(); filterIndex++) {
                JSONObject filter = filters.getJSONObject(filterIndex);
                Filter conditionFilter = new Filter();
                conditionFilter.setFieldName(filter.getString("field_name"));
                conditionFilter.setIndexName(filter.getString("index_name"));
                conditionFilter.setFieldValueType(filter.getString("field_value_type"));
                conditionFilter.setOperator(Operator.valueOf(filter.getString("operator")));
                conditionFilter.setFieldValues(filter.getJSONArray("field_values").toJavaList(String.class));
                conditionFilter.setConnector(filter.getString("connector"));
                conditionFilter.setValueType(filter.getInteger("value_type"));
                conditionFilter.setRefDescribeApiName(filter.getString("ref_describe_api_name"));
                conditionFilter.setRefFieldApiName(filter.getString("ref_field_api_name"));
                conditionFilter.setIsCascade(filter.getBoolean("is_cascade"));
                conditionFilter.setIsMasterField(filter.getBoolean("is_master_field"));
                conditionFilter.setFilterGroup(filter.getString("filterGroup"));
                conditionWhere.getFilters().add(conditionFilter);
            }

            Filter idFilter = new Filter();
            idFilter.setFieldName("_id");
            idFilter.setOperator(Operator.EQ);
            idFilter.setFieldValues(Lists.newArrayList(storeId));
            idFilter.setConnector("AND");

            conditionWhere.getFilters().add(idFilter);
            conditionWheres.add(conditionWhere);
        }
        query.setWheres(conditionWheres);
        return serviceFacade.findBySearchQuery(User.systemUser(context.getTenantId()), ApiNames.ACCOUNT_OBJ, query).getData();
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Collections.emptyList();
    }
}
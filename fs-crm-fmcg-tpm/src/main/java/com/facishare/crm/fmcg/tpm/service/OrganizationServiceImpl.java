package com.facishare.crm.fmcg.tpm.service;

import com.facishare.organization.api.model.RunStatus;
import com.facishare.organization.api.model.department.arg.GetDepartmentDtoArg;
import com.facishare.organization.api.model.department.result.GetDepartmentDtoResult;
import com.facishare.organization.api.model.departmentmember.MainDepartment;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.organization.api.model.employee.arg.GetAllEmployeesDtoArg;
import com.facishare.organization.api.model.employee.arg.GetEmployeeDtoArg;
import com.facishare.organization.api.model.employee.arg.GetEmployeesDtoByDepartmentIdArg;
import com.facishare.organization.api.model.employee.result.GetEmployeeDtoResult;
import com.facishare.organization.api.model.employee.result.GetEmployeesDtoByDepartmentIdResult;
import com.facishare.organization.api.service.DepartmentProviderService;
import com.facishare.organization.api.service.EmployeeProviderService;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2020/11/12 7:19 PM
 */
@Component
public class OrganizationServiceImpl implements OrganizationService {

    public static final Logger log = LoggerFactory.getLogger(OrganizationService.class);

    @Resource
    private EmployeeProviderService employeeProviderService;

    @Resource
    private DepartmentProviderService departmentProviderService;

    public EmployeeDto getEmployee(int tenantId, int employeeId) {
        GetEmployeeDtoArg arg = new GetEmployeeDtoArg();
        arg.setEmployeeId(employeeId);
        arg.setEnterpriseId(tenantId);
        GetEmployeeDtoResult result = employeeProviderService.getEmployeeDto(arg);
        return result.getEmployeeDto();
    }

    public List<EmployeeDto> queryAllEmployee(int tenantId) {
        GetAllEmployeesDtoArg arg = new GetAllEmployeesDtoArg();
        arg.setRunStatus(RunStatus.ALL);
        arg.setEnterpriseId(tenantId);
        return employeeProviderService.getAllEmployees(arg).getEmployeeDtoList();
    }

    public List<Integer> getDepartmentIds(int tenantId, int employeeId) {
        GetEmployeeDtoArg empArg = new GetEmployeeDtoArg();
        empArg.setEnterpriseId(tenantId);
        empArg.setEmployeeId(employeeId);

        log.info("find employee arg : {}", empArg);

        GetEmployeeDtoResult empResult = employeeProviderService.getEmployeeDto(empArg);

        log.info("find employee arg : {}", empResult);

        if (empResult.getEmployeeDto().getMainDepartmentId() == null) {
            return Lists.newArrayList();
        }

        List<Integer> departmentIds = Lists.newArrayList(empResult.getEmployeeDto().getMainDepartmentId());

        GetDepartmentDtoArg depArg = new GetDepartmentDtoArg();
        depArg.setDepartmentId(empResult.getEmployeeDto().getMainDepartmentId());
        depArg.setEnterpriseId(tenantId);

        log.info("find department arg : {}", depArg);

        GetDepartmentDtoResult depResult = departmentProviderService.getDepartmentDto(depArg);
        departmentIds.addAll(depResult.getDepartment().getAncestors());

        if (!departmentIds.contains(999999)) {
            departmentIds.add(999999);
        }

        return departmentIds;
    }

    public List<Integer> queryEmployeeIds(int tenantId, int departmentId) {
        GetEmployeesDtoByDepartmentIdArg arg = new GetEmployeesDtoByDepartmentIdArg();

        arg.setEnterpriseId(tenantId);
        arg.setDepartmentId(departmentId);
        arg.setIncludeLowDepartment(true);
        arg.setRunStatus(RunStatus.ALL);
        arg.setMainDepartment(MainDepartment.ALL);

        GetEmployeesDtoByDepartmentIdResult result = employeeProviderService.getEmployeesByDepartmentId(arg);
        return result.getEmployeeDtos().stream().map(EmployeeDto::getEmployeeId).collect(Collectors.toList());
    }

    public boolean employeeInRange(int tenantId, int employeeId, List<Integer> departmentIds) {
        if (CollectionUtils.isEmpty(departmentIds)) {
            return false;
        }

        GetEmployeeDtoArg empArg = new GetEmployeeDtoArg();
        empArg.setEnterpriseId(tenantId);
        empArg.setEmployeeId(employeeId);

        log.info("find employee arg : {}", empArg);

        GetEmployeeDtoResult empResult = employeeProviderService.getEmployeeDto(empArg);

        log.info("find employee arg : {}", empResult);

        if (empResult.getEmployeeDto().getMainDepartmentId() == null) {
            return false;
        }

        if (departmentIds.contains(empResult.getEmployeeDto().getMainDepartmentId())) {
            return true;
        }

        GetDepartmentDtoArg depArg = new GetDepartmentDtoArg();
        depArg.setDepartmentId(empResult.getEmployeeDto().getMainDepartmentId());
        depArg.setEnterpriseId(tenantId);

        log.info("find department arg : {}", depArg);

        GetDepartmentDtoResult depResult = departmentProviderService.getDepartmentDto(depArg);

        log.info("find department result : {}", depResult);

        for (Integer departmentId : departmentIds) {
            if (depResult.getDepartment().getAncestors().contains(departmentId)) {
                return true;
            }
        }
        return false;
    }
}

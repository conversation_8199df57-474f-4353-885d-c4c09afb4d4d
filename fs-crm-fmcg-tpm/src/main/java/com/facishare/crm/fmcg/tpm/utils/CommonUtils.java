package com.facishare.crm.fmcg.tpm.utils;

import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

@SuppressWarnings("Duplicates")
public class CommonUtils {
    private static final long[] DECIMAL_BASE = new long[]{0, 10, 100, 1000, 10000, 100000, 1000000, 10000000, 100000000};

    private static final Logger log = LoggerFactory.getLogger(CommonUtils.class);

    private CommonUtils() {
    }

    public static final int DEFAULT_LIMIT = 250;

    public static <T> List<T> cast(Object obj, Class<T> clazz) {
        List<T> result = new ArrayList<>();
        if (obj instanceof List<?>) {
            for (Object o : (List<?>) obj) {
                result.add(clazz.cast(o));
            }
            return result;
        }
        throw new ClassCastException();
    }

    public static Object getOrDefault(Object value, Object defaultValue) {
        return value == null ? defaultValue : value;
    }

    public static List<IObjectData> queryData(ServiceFacade serviceFacade, User user, RequestContext context, String apiName, SearchTemplateQuery query, List<String> fields) {
        int max = query.getLimit() == 0 || query.getLimit() == -1 ? Integer.MAX_VALUE : query.getLimit();

        int limit = Math.min(250, max);
        int offset = 0;

        query.setLimit(limit);
        query.setNeedReturnCountNum(false);

        List<IObjectData> data = Lists.newArrayList();
        QueryResult<IObjectData> result;

        SearchTemplateQuery innerQuery = copy(query);
        while (data.size() < max && !(result = serviceFacade.findBySearchTemplateQueryWithFields(ActionContextExt.of(user, context).getContext(), apiName, innerQuery, fields)).getData().isEmpty()) {

            data.addAll(result.getData());
            offset += result.getData().size();

            innerQuery = copy(query);
            innerQuery.setOffset(offset);
        }
        return data;
    }

    public static List<IObjectData> queryData(ServiceFacade serviceFacade, User user, String apiName, SearchTemplateQuery query) {
        int maxSize = query.getLimit() == 0 || query.getLimit() == -1 ? Integer.MAX_VALUE : query.getLimit();
        int limit = Math.min(250, maxSize);
        int offset = 0;
        query.setLimit(limit);
        query.setNeedReturnCountNum(false);

        List<IObjectData> dataList = Lists.newArrayList();
        QueryResult<IObjectData> result;
        SearchTemplateQuery templateQuery = copy(query);
        while (dataList.size() < maxSize && !(result = serviceFacade.findBySearchQuery(user, apiName, templateQuery)).getData().isEmpty()) {
            dataList.addAll(result.getData());
            offset += result.getData().size();
            templateQuery = copy(query);
            templateQuery.setOffset(offset);
        }
        return dataList;
    }

    public static void queryData(ServiceFacade serviceFacade, User user, String apiName, SearchTemplateQuery query, DataExecutor executor) {
        int maxSize = query.getLimit() == 0 || query.getLimit() == -1 ? Integer.MAX_VALUE : query.getLimit();
        int limit = Math.min(250, maxSize);
        int offset = 0;
        query.setLimit(limit);
        query.setNeedReturnCountNum(false);

        QueryResult<IObjectData> result;
        SearchTemplateQuery templateQuery = copy(query);
        while (offset < maxSize && !(result = serviceFacade.findBySearchQuery(user, apiName, templateQuery)).getData().isEmpty()) {
            executor.execute(result.getData());
            templateQuery = copy(query);
            offset += result.getData().size();
            templateQuery.setOffset(offset);
        }
    }

    public static void queryDataWithoutOffset(ServiceFacade serviceFacade, User user, String apiName, SearchTemplateQuery query, int maxTimes, DataExecutor executor) {
        int maxSize = query.getLimit() == 0 || query.getLimit() == -1 ? Integer.MAX_VALUE : query.getLimit();
        int limit = Math.min(250, maxSize);
        query.setLimit(limit);
        query.setNeedReturnCountNum(false);
        query.setOffset(0);

        QueryResult<IObjectData> result;
        SearchTemplateQuery templateQuery = copy(query);
        int count = 0;
        while (!(result = serviceFacade.findBySearchQuery(user, apiName, templateQuery)).getData().isEmpty()) {
            executor.execute(result.getData());
            templateQuery = copy(query);
            count++;
            if (count > maxTimes) {
                log.info("reach max times,maxTimes:{},query:{}", maxTimes, query);
                break;
            }
        }
    }

    public static Long getEndDateTimeStamp(Long timeStamp) {
        return timeStamp == null ? null : timeStamp + 24 * 60 * 60 * 1000 - 1000;
    }

    public interface DataExecutor {
        void execute(List<IObjectData> partialData);
    }


    public static SearchTemplateQuery copy(SearchTemplateQuery query) {
        SearchTemplateQuery clone = new SearchTemplateQuery();
        clone.setFilters(query.getFilters());
        clone.setNeedReturnCountNum(query.getNeedReturnCountNum());
        clone.setOrders(query.getOrders());
        clone.setWheres(query.getWheres());
        clone.setLimit(query.getLimit());
        clone.setOffset(query.getOffset());
        clone.setPattern(query.getPattern());
        clone.setSearchSource(query.getSearchSource());
        return clone;
    }

    public static double keepNDecimal(double value, int n) {
        long base = 10;
        if (n >= DECIMAL_BASE.length) {
            for (int i = 0; i < n; i++) base *= 10;
        } else {
            base = DECIMAL_BASE[n];
        }
        return Math.round(value * base) * 1.0 / base;
    }
}
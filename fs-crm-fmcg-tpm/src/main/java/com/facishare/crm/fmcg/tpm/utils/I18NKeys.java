package com.facishare.crm.fmcg.tpm.utils;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2020/11/4 5:09 PM
 */
public interface I18NKeys {

    String BATCH_INVALID_ACTIVITY_ITEM_ERROR_ITEM_USED = "fmcg.crm.BATCH_INVALID_ACTIVITY_ITEM_ERROR_ITEM_USED";

    String BATCH_INVALID_ACTIVITY_ITEM_ERROR_ITEM_USED_BY_COST_STANDARD = "fmcg.crm.BATCH_INVALID_ACTIVITY_ITEM_ERROR_ITEM_USED_BY_COST_STANDARD";

    String INVALID_ACTIVITY_ITEM_ERROR_ITEM_USED = "fmcg.crm.INVALID_ACTIVITY_ITEM_ERROR_ITEM_USED";

    String ADD_ACTIVITY_DATE_ERROR = "fmcg.crm.ADD_ACTIVITY_DATE_ERROR";

    String ADD_AGREEMENT_DATE_ERROR = "fmcg.crm.ADD_AGREEMENT_DATE_ERROR";

    String ADD_ACTIVITY_DEALER_ID_ERROR = "fmcg.crm.ADD_ACTIVITY_DEALER_ID_ERROR";

    String AGREEMENT_TIME_OUT_OF_RANGE_ERROR = "fmcg.crm.AGREEMENT_TIME_OUT_OF_RANGE_ERROR";

    String FIELD_IS_EMPTY_ERROR = "fmcg.crm.FIELD_IS_EMPTY_ERROR";

    String TIME_OVERLAP_IN_ACTIVITY_AND_STORE_ERROR = "fmcg.crm.TIME_OVERLAP_IN_ACTIVITY_AND_STORE_ERROR";

    String BATCH_INVALID_AGREEMENT_ERROR_ITEM_USED = "fmcg.crm.BATCH_INVALID_AGREEMENT_ERROR_ITEM_USED";

    String INVALID_AGREEMENT_ERROR_ITEM_USED = "fmcg.crm.INVALID_AGREEMENT_ERROR_ITEM_USED";

    String INVALID_ACTIVITY_ERROR_ITEM_USED_BY_PROOF = "fmcg.crm.INVALID_ACTIVITY_ERROR_ITEM_USED_BY_PROOF";

    String INVALID_ACTIVITY_ERROR_ITEM_USED_BY_AGREEMENT = "fmcg.crm.INVALID_ACTIVITY_ERROR_ITEM_USED_BY_AGREEMENT";

    String BATCH_INVALID_ACTIVITY_ERROR_ITEM_USED_BY_PROOF = "fmcg.crm.BATCH_INVALID_ACTIVITY_ERROR_ITEM_USED_BY_PROOF";

    String BATCH_INVALID_ACTIVITY_ERROR_ITEM_USED_BY_AGREEMENT = "fmcg.crm.BATCH_INVALID_ACTIVITY_ERROR_ITEM_USED_BY_AGREEMENT";

    String ADD_ACTIVITY_TIME_IS_NOT_FIT_BUDGET = "fmcg.crm.ADD_ACTIVITY_TIME_IS_NOT_FIT_BUDGET";

    String ADD_ACTIVITY_BUDGET_INSUFFICIENT = "fmcg.crm.ADD_ACTIVITY_BUDGET_INSUFFICIENT";

    String ADD_ACTIVITY_BUDGET_DEPARTMENT_NOT_FIT = "fmcg.crm.BUDGET_DEPARTMENT_NOT_FIT";

    String ONLY_CASE_ACTIVITY_CAN_USE_BUDGET = "fmcg.crm.ONLY_CASE_ACTIVITY_CAN_USE_BUDGET";

    String ACTIVITY_AMOUNT_CAN_NOT_BE_ZERO = "fmcg.crm.ACTIVITY_AMOUNT_CAN_NOT_BE_ZERO";

    String ACTIVITY_ENDED_CAN_NOT_CREATE_AGREEMENT = "fmcg.crm.ACTIVITY_ENDED_CAN_NOT_CREATE_AGREEMENT";

    String ACTIVITY_WHICH_IS_NOT_AGREEMENT_ACTIVITY_CAN_NOT_CREATE_AGREEMENT =  "fmcg.crm.ACTIVITY_WHICH_IS_NOT_AGREEMENT_ACTIVITY_CAN_NOT_CREATE_AGREEMENT";

    String THIS_STORE_WHICH_IS_NOT_IN_THE_RANGE_CAN_NOT_CREATE_AGREEMENT = "fmcg.crm.THIS_STORE_WHICH_IS_NOT_IN_THE_RANGE_CAN_NOT_CREATE_AGREEMENT";

    String CURRENT_TIME_IS_NOT_FIT_THE_AGREEMENT_TIME = "fmcg.crm.CURRENT_TIME_IS_NOT_FIT_THE_AGREEMENT_TIME";

    String AGREEMENT_WHICH_HAS_RELATED_BY_PROOF_CAN_NOT_EDIT = "fmcg.crm.AGREEMENT_WHICH_HAS_RELATED_BY_PROOF_CAN_NOT_EDIT";

    String ACTIVITY_WHICH_HAS_NOT_STARTED_CAN_NOT_CREATE_AGREEMENT = "fmcg.crm.ACTIVITY_WHICH_HAS_NOT_STARTED_CAN_NOT_CREATE_AGREEMENT";

    String ADJUST_AMOUNT_MUST_MORE_THAN_ZERO = "fmcg.crm.ADJUST_AMOUNT_MUST_MORE_THAN_ZERO";

    String TRANSFER_IN_BUDGET_AND_TRANSFER_OUT_BUDGET_CAN_NOT_BE_THE_SAME = "fmcg.crm.TRANSFER_IN_BUDGET_AND_TRANSFER_OUT_BUDGET_CAN_NOT_BE_THE_SAME";

    String SOURCE_BUDGET_DON_NOT_HAVE_ENOUGH_MONEY = "fmcg.crm.SOURCE_BUDGET_DON_NOT_HAVE_ENOUGH_MONEY";

    String BOTH_BUDGET_IN_ADJUST_CAN_NOT_BE_EMPTY = "fmcg.crm.BOTH_BUDGET_IN_ADJUST_CAN_NOT_BE_EMPTY";

    String COMPLETED_ADJUST_CAN_NOT_BE_INVALID = "fmcg.crm.COMPLETED_ADJUST_CAN_NOT_BE_INVALID";

    String NORMAL_STATUS_ADJUST_CAN_NOT_BE_EDIT = "fmcg.crm.NORMAL_STATUS_ADJUST_CAN_NOT_BE_EDIT";

    String SINGLE_COMPLETED_ADJUST_CAN_NOT_BE_INVALID = "fmcg.crm.SINGLE_COMPLETED_ADJUST_CAN_NOT_BE_INVALID";

    String THOSE_BUDGET_RELATED_BY_ACTIVITY_CAN_NOT_BE_INVALID = "fmcg.crm.THOSE_BUDGET_RELATED_BY_ACTIVITY_CAN_NOT_BE_INVALID";

    String THOSE_BUDGET_RELATED_BY_ADJUST_CAN_NOT_BE_INVALID = "fmcg.crm.THOSE_BUDGET_RELATED_BY_ADJUST_CAN_NOT_BE_INVALID";

    String THE_REQUEST_FROM_OUTER_CAN_NOT_CREATE_BUDGET_DETAIL = "fmcg.crm.THE_REQUEST_FROM_OUTER_CAN_NOT_CREATE_BUDGET_DETAIL";

    String INVALID_BUDGET_DETAIL_CAN_NOT_BE_ALLOW_FRO_NORMAL_RIGHT  = "fmcg.crm.INVALID_BUDGET_DETAIL_CAN_NOT_BE_ALLOW_FRO_NORMAL_RIGHT";

    String BUDGET_HAS_RELATED_BY_OTHERS_CAN_NOT_BE_EDIT = "fmcg.crm.BUDGET_HAS_RELATED_BY_OTHERS_CAN_NOT_BE_EDIT";

    String NORMAL_STATUS_BUDGET_CAN_NOT_EDIT_AMOUNT_FIELD = "fmcg.crm.NORMAL_STATUS_BUDGET_CAN_NOT_EDIT_AMOUNT_FIELD";

    String ONLY_SUPPORT_INVALID_SINGLE_ACTIVITY = "fmcg.crm.ONLY_SUPPORT_INVALID_SINGLE_ACTIVITY";

    String ACTIVITY_RELATED_BUDGET_CAN_NOT_BE_RECOVER = "fmcg.crm.ACTIVITY_RELATED_BUDGET_CAN_NOT_BE_RECOVER";

    String ACTIVITY_HAS_NOT_END_CAN_NOT_CLOSE = "fmcg.crm.ACTIVITY_HAS_NOT_END_CAN_NOT_CLOSE";

    String ACTIVITY_HAS_UNCOMPLETED_COST_CAN_NOT_CLOSE = "fmcg.crm.ACTIVITY_HAS_UNCOMPLETED_COST_CAN_NOT_CLOSE";

    String ACTIVITY_HAS_CLOSED = "fmcg.crm.ACTIVITY_HAS_CLOSED";

    String AMOUNT_OVER_THE_LIMIT_CAN_NOT_CLOSE =  "fmcg.crm.AMOUNT_OVER_THE_LIMIT_CAN_NOT_CLOSE";

    String ACTIVITY_WHICH_HAS_RELATED_BY_PROOF_CAN_NOT_EDIT = "fmcg.crm.ACTIVITY_WHICH_HAS_RELATED_BY_PROOF_CAN_NOT_EDIT";

    String ACTIVITY_RELATED_BUDGET_CAN_NOT_CHANGE_BUDGET = "fmcg.crm.ACTIVITY_RELATED_BUDGET_CAN_NOT_CHANGE_BUDGET";

    String ACTIVITY_RELATED_A_NEW_BUDGET_IS_NOT_ALLOW =  "fmcg.crm.ACTIVITY_RELATED_A_NEW_BUDGET_IS_NOT_ALLOW";

    String ACTIVITY_RELATED_DEALER_ACTIVITY_CAN_NOT_BE_INVALID =  "fmcg.crm.ACTIVITY_RELATED_DEALER_ACTIVITY_CAN_NOT_BE_INVALID";

    String CURRENT_TENANT_DO_NOT_SUPPORT_COST_STANDARD = "fmcg.crm.CURRENT_TENANT_DO_NOT_SUPPORT_COST_STANDARD";

    String THOSE_COST_STANDARD_RELATED_BY_AUDIT_DETAIL = "fmcg.crm.THOSE_COST_STANDARD_RELATED_BY_AUDIT_DETAIL";

    String THOSE_COST_STANDARD_RELATED_BY_PROOF_DETAIL = "fmcg.crm.THOSE_COST_STANDARD_RELATED_BY_PROOF_DETAIL";

    String COST_STANDARD_RELATED_BY_AUDIT_DETAIL = "fmcg.crm.COST_STANDARD_RELATED_BY_AUDIT_DETAIL";

    String COST_STANDARD_RELATED_BY_PROOF_DETAIL = "fmcg.crm.COST_STANDARD_RELATED_BY_PROOF_DETAIL";

    String ACTIVITY_ITEM_RELATED_BY_COST_STANDARD = "fmcg.crm.ACTIVITY_ITEM_RELATED_BY_COST_STANDARD";

    String ACTIVITY_MAX_STORE_NUMBER_VALIDATE ="fmcg.crm.ACTIVITY_MAX_STORE_NUMBER_VALIDATE";

    String PROOF_FREQUENT_VALIDATE_TIME_OUT_OF_RANGE = "fmcg.crm.PROOF_FREQUENT_VALIDATE_TIME_OUT_OF_RANGE";

    String PROOF_FREQUENT_VALIDATE_HALF_DAY = "fmcg.crm.PROOF_FREQUENT_VALIDATE_HALF_DAY";

    String PROOF_FREQUENT_VALIDATE_ONE_DAY = "fmcg.crm.PROOF_FREQUENT_VALIDATE_ONE_DAY";

    String PROOF_FREQUENT_VALIDATE_TWO_DAY = "fmcg.crm.PROOF_FREQUENT_VALIDATE_TWO_DAY";

    String PROOF_FREQUENT_VALIDATE_ONE_MONTH = "fmcg.crm.PROOF_FREQUENT_VALIDATE_ONE_MONTH";

    String PROOF_FREQUENT_VALIDATE_TWO_MONTH = "fmcg.crm.PROOF_FREQUENT_VALIDATE_TWO_MONTH";

    String PROOF_FREQUENT_VALIDATE_ONE_ACTIVITY = "fmcg.crm.PROOF_FREQUENT_VALIDATE_ONE_ACTIVITY";

    String ACTIVITY_HAS_CLOSED_CAN_NOT_CREATE_PROOF = "fmcg.crm.ACTIVITY_HAS_CLOSED_CAN_NOT_CREATE_PROOF";

    String STORE_DEALER_CAN_NOT_MATCH_ACTIVITY_DEALER = "fmcg.crm.STORE_DEALER_CAN_NOT_MATCH_ACTIVITY_DEALER";

    String EMPLOYEE_IS_NOT_WITHIN_THE_SCOPE_OF_ACTIVITY = "fmcg.crm.EMPLOYEE_IS_NOT_WITHIN_THE_SCOPE_OF_ACTIVITY";

    String STORE_DEALER_IS_EMPTY_CAN_NOT_PROOF = "fmcg.crm.STORE_DEALER_IS_EMPTY_CAN_NOT_PROOF";

    String AGREEMENT_STATUS_IS_ABNORMAL = "fmcg.crm.AGREEMENT_STATUS_IS_ABNORMAL";

    String AGREEMENT_HAS_NOT_TAKEN_EFFECT = "fmcg.crm.AGREEMENT_HAS_NOT_TAKEN_EFFECT";

    String AGREEMENT_IS_NOT_FIT_THIS_STORE = "fmcg.crm.AGREEMENT_IS_NOT_FIT_THIS_STORE";

    String THIS_IS_A_PROTOCOL_ACTIVITY_SO_PROOF_ITEM_SHOULD_RELATED_WITH_AGREEMENT_ITEM = "fmcg.crm.THIS_IS_A_PROTOCOL_ACTIVITY_SO_PROOF_ITEM_SHOULD_RELATED_WITH_AGREEMENT_ITEM";

    String ACTIVITY_IS_NOT_ACTIVE_OR_HAS_EXPIRED = "fmcg.crm.ACTIVITY_IS_NOT_ACTIVE_OR_HAS_EXPIRED";

    String ACTIVITY_WITCH_IS_PROTOCOL_SHOULD_CHECK_UP_AGREEMENT_OPTION = "fmcg.crm.ACTIVITY_WITCH_IS_PROTOCOL_SHOULD_CHECK_UP_AGREEMENT_OPTION";

    String CHECKINS_INFO_IS_LOST_CAN_NOT_SUPPORT_PROOF = "fmcg.crm.CHECKINS_INFO_IS_LOST_CAN_NOT_SUPPORT_PROOF";

    String AUDIT_INVALID_FAIL_DUE_TO_RELATED_BY_COST = "fmcg.crm.AUDIT_INVALID_FAIL_DUE_TO_RELATED_BY_COST";

    String AUDIT_RECOVER_FAIL_DUE_TO_DISABLE = "fmcg.crm.AUDIT_RECOVER_FAIL_DUE_TO_DISABLE";

    String AUDIT_CAN_NOT_EDIT_DUE_TO_RELATE_COST =  "fmcg.crm.AUDIT_CAN_NOT_EDIT_DUE_TO_RELATE_COST";

    String SINGLE_AUDIT_INVALID_FAIL_DUE_TO_RELATED_BY_COST = "fmcg.crm.SINGLE_AUDIT_INVALID_FAIL_DUE_TO_RELATED_BY_COST";

    String BATCH_AUDITED_PROOF_CAN_NOT_INVALID_NAMES =  "fmcg.crm.BATCH_AUDITED_PROOF_CAN_NOT_INVALID_NAMES";

    String BATCH_PROOF_RELATED_COST_CAN_NOT_INVALID_NAMES =  "fmcg.crm.BATCH_PROOF_RELATED_COST_CAN_NOT_INVALID_NAMES";

    String AUDITED_PROOF_CAN_NOT_EDIT = "fmcg.crm.AUDITED_PROOF_CAN_NOT_EDIT";

    String CAN_NOT_EDIT_PROOF_DETAIL = "fmcg.crm.CAN_NOT_EDIT_PROOF_DETAIL";

    String CAN_NOT_EDIT_NON_IMAGE_FIELD = "fmcg.crm.CAN_NOT_EDIT_NON_IMAGE_FIELD";

    String SINGLE_AUDITED_PROOF_CAN_NOT_INVALID_NAMES =  "fmcg.crm.SINGLE_AUDITED_PROOF_CAN_NOT_INVALID_NAMES";

    String SINGLE_PROOF_RELATED_COST_CAN_NOT_INVALID_NAMES =  "fmcg.crm.SINGLE_PROOF_RELATED_COST_CAN_NOT_INVALID_NAMES";

    String ACTIVITY_HAS_CLOSED_CAN_NOT_CREATE_COST =  "fmcg.crm.ACTIVITY_HAS_CLOSED_CAN_NOT_CREATE_COST";

    String NO_DATA_IN_THIS_TIME_RANGE_WHEN_CREATE_COST = "fmcg.crm.NO_DATA_IN_THIS_TIME_RANGE_WHEN_CREATE_COST";

    String DO_NOT_SUPPORT_CREATE_COST_DUE_TO_NONE_OF_PROOF_HAS_BEEN_AUDIT_IN_TIME_RANGE =  "fmcg.crm.DO_NOT_SUPPORT_CREATE_COST_DUE_TO_NONE_OF_PROOF_HAS_BEEN_AUDIT_IN_TIME_RANGE";

    String CLOSED_ACTIVITY_DO_NOT_SUPPORT_RELATED_COST = "fmcg.crm.CLOSED_ACTIVITY_DO_NOT_SUPPORT_RELATED_COST";

    String CAN_NOT_FILL_AUDIT_AMOUNT_WHEN_CREATING_COST = "fmcg.crm.CAN_NOT_FILL_AUDIT_AMOUNT_WHEN_CREATING_COST";

    String AMOUNT_SHOULD_LESS_THAN_AUDIT_AMOUNT = "fmcg.crm.AMOUNT_SHOULD_LESS_THAN_AUDIT_AMOUNT";

    String COST_TIME_RANGE_IS_ERROR = "fmcg.crm.COST_TIME_RANGE_IS_ERROR";

    String THIS_COST_HAS_TRANSFER_IN_ACCOUNT_DO_NOT_SUPPORT_INVALID = "fmcg.crm.THIS_COST_HAS_TRANSFER_IN_ACCOUNT_DO_NOT_SUPPORT_INVALID";

    String THE_COST_RELATED_BUDGET_CAN_NOT_RECOVER =  "fmcg.crm.THE_COST_RELATED_BUDGET_CAN_NOT_RECOVER";

    String NORMAL_STATUS_COST_CAN_NOT_EDIT = "fmcg.crm.NORMAL_STATUS_COST_CAN_NOT_EDIT";

    String REJECT_COST_CAN_NOT_EDIT_TIME_FIELD = "fmcg.crm.REJECT_COST_CAN_NOT_EDIT_TIME_FIELD";

    String AUDIT_AMOUNT_CAN_NOT_OVER_THE_ACTIVITY_AMOUNT =  "fmcg.crm.AUDIT_AMOUNT_CAN_NOT_OVER_THE_ACTIVITY_AMOUNT";

    String THIS_SINGLE_COST_HAS_TRANSFER_IN_ACCOUNT_DO_NOT_SUPPORT_INVALID = "fmcg.crm.THIS_SINGLE_COST_HAS_TRANSFER_IN_ACCOUNT_DO_NOT_SUPPORT_INVALID";

    String DO_NOT_HAVE_PROOF_AUDIT_CREATE_RIGHT =  "fmcg.crm.DO_NOT_HAVE_PROOF_AUDIT_CREATE_RIGHT";

    String DEALER_ID_FIELD_IS_NULL_CAN_NOT_PROOF = "fmcg.crm.DEALER_ID_FIELD_IS_NULL_CAN_NOT_PROOF";

    public static final String DEALER_ACTIVITY_COST_OBJ_EDIT_ACTION_5 = "fmcg.crm.fmcg.tpm.DEALER_ACTIVITY_COST_OBJ_EDIT_ACTION.5";

}

package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.crm.fmcg.tpm.api.special.yl.QueryProductCategory;
import com.facishare.crm.fmcg.tpm.apiname.ApiNames;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.dto.FieldResult;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import de.lab4inf.math.util.Strings;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/8/20 下午5:54
 */
public class TPMActivityObjQueryProductCategoryController extends PreDefineController<QueryProductCategory.Arg, QueryProductCategory.Result> {

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList();
    }

    @Override
    protected QueryProductCategory.Result doService(QueryProductCategory.Arg arg) {
        String activityId = arg.getActivityId();

        IObjectData activity = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()), activityId, ApiNames.TPM_ACTIVITY_OBJ);
        if (Objects.isNull(activity)) {
            return QueryProductCategory.Result.builder().build();
        }

        String planId = activity.get("activity_time_span_plan_id__c", String.class);
        if (Strings.isNullOrEmpty(planId)) {
            return QueryProductCategory.Result.builder().build();
        }

        IObjectData plan = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()), planId, "tpm_activity_time_span_plan__c");
        if (Objects.isNull(plan)) {
            return QueryProductCategory.Result.builder().build();
        }

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(500);
        query.setOffset(0);
        query.setNeedReturnQuote(true);
        query.setSearchSource("db");

        Filter planFilter = new Filter();
        planFilter.setFieldName("tpm_activity_time_span_plan_id__c");
        planFilter.setOperator(Operator.EQ);
        planFilter.setFieldValues(Lists.newArrayList(planId));

        query.setFilters(Lists.newArrayList(planFilter));

        QueryResult<IObjectData> data = serviceFacade.findBySearchQuery(User.systemUser(controllerContext.getTenantId()), "tpm_activity_time_span_plan_product__c", query);

        FieldResult fieldResult = serviceFacade.findCustomFieldDescribe(controllerContext.getTenantId(), "tpm_activity_plan_product__c", "product_category__c");

        List<Map<String, String>> optionArray = (List<Map<String, String>>) fieldResult.getField().get("options");
        Map<String, String> value2label = new HashMap<>();
        if (!CollectionUtils.isEmpty(optionArray)) {
            optionArray.forEach(v -> value2label.put(v.get("value"), v.get("label")));
        }

        return QueryProductCategory.Result.builder()
                .data(data.getData()
                        .stream()
                        .map(datum -> {
                            String code = datum.get("product_category__c", String.class);
                            String name = value2label.get(code);
                            return QueryProductCategory.ProductCategory.builder()
                                    .code(code)
                                    .name(name)
                                    .build();
                        })
                        .collect(Collectors.toList()))
                .build();
    }
}

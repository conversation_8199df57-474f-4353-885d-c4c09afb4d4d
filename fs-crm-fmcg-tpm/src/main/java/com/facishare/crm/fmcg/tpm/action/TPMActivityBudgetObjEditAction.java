package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.api.enumeration.LogType;
import com.facishare.crm.fmcg.tpm.api.log.LogData;
import com.facishare.crm.fmcg.tpm.api.method.IdempotentArgBase;
import com.facishare.crm.fmcg.tpm.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.apiname.CommonFields;
import com.facishare.crm.fmcg.tpm.apiname.TPMActivityBudgetFields;
import com.facishare.crm.fmcg.tpm.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.tpm.business.BudgetService;
import com.facishare.crm.fmcg.tpm.business.OperateInfoService;
import com.facishare.crm.fmcg.tpm.service.PackTransactionProxy;
import com.facishare.crm.fmcg.tpm.service.PackTransactionProxyImpl;
import com.facishare.crm.fmcg.tpm.service.TransactionService;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;


/**
 * <AUTHOR>
 * @date 2021/3/9 下午7:28
 */
@SuppressWarnings("Duplicates")
public class TPMActivityBudgetObjEditAction extends StandardEditAction implements TransactionService<StandardEditAction.Arg, StandardEditAction.Result> {

    public static Logger log = LoggerFactory.getLogger(TPMActivityBudgetObjEditAction.class);

    private final PackTransactionProxy packTransactionProxy = SpringUtil.getContext().getBean(PackTransactionProxyImpl.class);

    private final BudgetService budgetService = SpringUtil.getContext().getBean(BudgetService.class);

    private final OperateInfoService operateInfoService = SpringUtil.getContext().getBean(OperateInfoService.class);

    @Override
    protected void before(Arg arg) {

        if (!"ineffective".equals(arg.getObjectData().get(CommonFields.LIFE_STATUS))) {
            IObjectData budget = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), arg.getObjectData().getId(), ApiNames.TPM_ACTIVITY_BUDGET);
            if (!budget.get(TPMActivityBudgetFields.AMOUNT).equals(arg.getObjectData().get(TPMActivityBudgetFields.AMOUNT)))
                throw new ValidateException(I18N.text(I18NKeys.NORMAL_STATUS_BUDGET_CAN_NOT_EDIT_AMOUNT_FIELD));
        }
        //考虑作废和恢复？
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter activityFilter = new Filter();
        activityFilter.setFieldName(TPMActivityFields.BUDGET_TABLE);
        activityFilter.setOperator(Operator.EQ);
        activityFilter.setFieldValues(Lists.newArrayList(arg.getObjectData().getId()));

        IFilter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.NIN);
        lifeStatusFilter.setFieldValues(Lists.newArrayList("ineffective", "invalid"));

        query.setFilters(Lists.newArrayList(activityFilter, lifeStatusFilter));
        QueryResult<IObjectData> activitiesRst = serviceFacade.findBySearchQuery(User.systemUser(actionContext.getTenantId()), ApiNames.TPM_ACTIVITY_OBJ, query);
        if (activitiesRst.getTotalNumber() > 0) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_HAS_RELATED_BY_OTHERS_CAN_NOT_BE_EDIT));
        }
        super.before(arg);
    }

    @Override
    protected Result doAct(Arg arg) {
        budgetService.tryLockBudget(actionContext, arg.getObjectData().getId());
        return packTransactionProxy.packAct(this, arg);
    }

    @Override
    public Result doActTransaction(Arg arg) {
        double amount = Double.parseDouble(arg.getObjectData().getOrDefault(TPMActivityBudgetFields.AMOUNT, "0.0").toString());
        IObjectData budget = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), arg.getObjectData().getId(), ApiNames.TPM_ACTIVITY_BUDGET);
        double oldAmount = budget.get(TPMActivityBudgetFields.AMOUNT, Double.class, 0.0);
        Result result = super.doAct(arg);
        LogData logData = LogData.builder().data(JSON.toJSONString(arg)).updateMap(this.updatedFieldMap).originalData(this.dbMasterData).attributes(new HashMap<>()).build();
        logData.getAttributes().put("oldObject", budget);
        if (amount != oldAmount) {
            String logId = operateInfoService.log(actionContext.getTenantId(), LogType.EDIT.value(), JSON.toJSONString(logData), actionContext.getUser().getUserId(), ApiNames.TPM_ACTIVITY_BUDGET, arg.getObjectData().getId(), this.needTriggerApprovalFlow());
            double difference = amount - oldAmount;
            IObjectData detail = budgetService.addBudgetDetail(actionContext.getTenantId(),
                    actionContext.getUser().getUserId(),
                    difference < 0 ? "1" : "2",
                    budget.getId(),
                    String.format("初期预算表编辑：「%s」新建", budget.get("name")),
                    difference,
                    oldAmount,
                    amount,
                    System.currentTimeMillis(),
                    String.format("LogId:%s-traceId:%s", logId, TraceContext.get().getTraceId()),
                    null,
                    TraceContext.get().getTraceId(),
                    IdempotentArgBase.builder().idempotentKey(actionContext.getPostId() + ":" + budget.getId()).build()
            );

            log.info("amount change detail : {}", detail.toJsonString());
        }

        return result;
    }

    @Override
    protected void finallyDo() {
        try {
            super.finallyDo();
        } finally {
            budgetService.unLockBudget(actionContext);
        }
    }
}

package com.facishare.crm.fmcg.tpm.api.visit;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 12/9/20 2:43 PM
 */
@Data
@ToString
public class ActivityProofDTO implements Serializable {

    @JSONField(name = "proof_id")
    private String proofId;

    @JSONField(name = "activity_name")
    private String activityName;

    private List<ActivityProofImageDTO> images;

    @JSONField(name = "images_total_count")
    private long imagesTotalCount;

    private String remark;

    private String opinion;

    private String randomAuditStatus;

    @J<PERSON>NField(name = "audit_status")
    private String auditStatus;

    private List<ActivityProofDetailDTO> details;
}

package com.facishare.crm.fmcg.tpm.apiname;

public interface TPMDealerActivityCostFields {

    String END_DATE = "end_date";

    String DEALER_ACTIVITY_ID = "dealer_activity_id";

    String BEGIN_DATE = "begin_date";

    String ACTIVITY_ID = "activity_id";

    String AUDITED_AMOUNT = "audited_amount";

    String REMARK = "remark";

    String CONFIRMED_AMOUNT = "confirmed_amount";

    String DEALER_ID = "dealer_id";

    String ACTIVITY_ACTUAL_AMOUNT = "activity_actual_amount";

    String ACTIVITY_AMOUNT = "activity_amount";

    String FUND_ACCOUNT_ID = "fund_account_id";

    String ENTER_INTO_ACCOUNT = "enter_into_account";

    String WRITE_OFF_STATUS = "write_off_status";


    class WriteOffStatus{
        public static final String TO_BE_WRITE_OFF = "to_be_written_off";

        public static final String PASS = "pass";

        public static final String REJECT = "reject";

    }

}
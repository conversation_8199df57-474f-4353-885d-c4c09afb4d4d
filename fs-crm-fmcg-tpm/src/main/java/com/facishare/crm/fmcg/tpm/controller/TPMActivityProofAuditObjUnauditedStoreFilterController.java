package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.api.proof.UnauditedStoreFilter;
import com.facishare.crm.fmcg.tpm.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.apiname.CommonFields;
import com.facishare.crm.fmcg.tpm.apiname.TPMActivityProofFields;
import com.facishare.crm.fmcg.tpm.service.BuryModule;
import com.facishare.crm.fmcg.tpm.service.BuryOperation;
import com.facishare.crm.fmcg.tpm.service.BuryService;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang.StringUtils;

import java.util.*;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 12/2/20 4:12 PM
 */
@SuppressWarnings("Duplicates")
public class TPMActivityProofAuditObjUnauditedStoreFilterController extends PreDefineController<UnauditedStoreFilter.Arg, UnauditedStoreFilter.Result> {


    @Override
    protected UnauditedStoreFilter.Result doService(UnauditedStoreFilter.Arg arg) {
        log.info("unaudited store filter arg : {}", JSON.toJSONString(arg));

        UnauditedStoreFilter.Result result = new UnauditedStoreFilter.Result();

        List<IObjectData> proofList = queryProof(controllerContext, arg.getActivityId(), arg.getDealerId(), arg.getBegin(), arg.getEnd(), arg.getIdList());

        result.setTotalProofCount(proofList.size());
        result.setAuditedProofCount(proofList.stream().filter(proof -> !TPMActivityProofFields.AUDIT_STATUS__SCHEDULE.equals(proof.get(TPMActivityProofFields.AUDIT_STATUS))).count());
        result.setUnauditedProofCount(result.getTotalProofCount() - result.getAuditedProofCount());

        Map<String, Map<String, IObjectData>> proofCountMap = queryProofMap(proofList);
        Map<String, IObjectData> storeMap = queryStoreMap(controllerContext, new ArrayList<>(proofCountMap.keySet()));

        for (Map.Entry<String, Map<String, IObjectData>> storeEntry : proofCountMap.entrySet()) {
            UnauditedStoreFilter.UnauditedStoreVO datum = new UnauditedStoreFilter.UnauditedStoreVO();
            String storeId = storeEntry.getKey();
            Map<String, IObjectData> proofMap = storeEntry.getValue();

            int activityCount = 0;
            IObjectData tempProof = null;
            for (Map.Entry<String, IObjectData> proofEntry : proofMap.entrySet()) {
                if (TPMActivityProofFields.AUDIT_STATUS__SCHEDULE.equals(proofEntry.getValue().get(TPMActivityProofFields.AUDIT_STATUS))) {
                    tempProof = proofEntry.getValue();
                    activityCount = activityCount + 1;
                }
            }

            if (activityCount <= 0) {
                continue;
            } else if (activityCount == 1) {
                datum.setNavigateStrategy("proof_audit");
                datum.setProofData(tempProof);
            } else {
                datum.setNavigateStrategy("activity_list");
            }

            datum.setStoreData(storeMap.get(storeId));
            result.getData().put(storeEntry.getKey(), datum);
        }
        //埋点
        if ((arg.getBegin() != null && arg.getBegin() > 0) || (arg.getEnd() != null && arg.getEnd() > 0)) {
            BuryService.asyncTpmLog(Integer.valueOf(controllerContext.getTenantId()), Integer.parseInt(controllerContext.getUser().getUserIdOrOutUserIdIfOutUser()), BuryModule.TPM.TPM_ACTIVITY_PROOF_AUDIT, String.format(BuryOperation.SOMETHING_FILTER, "scene_audit_time"));
        }
        if (!StringUtils.isNotEmpty(arg.getDealerId())) {
            BuryService.asyncTpmLog(Integer.valueOf(controllerContext.getTenantId()), Integer.parseInt(controllerContext.getUser().getUserIdOrOutUserIdIfOutUser()), BuryModule.TPM.TPM_ACTIVITY_PROOF_AUDIT, String.format(BuryOperation.SOMETHING_FILTER, "scene_audit_dealer"));
        }
        if (!StringUtils.isNotEmpty(arg.getActivityId())) {
            BuryService.asyncTpmLog(Integer.valueOf(controllerContext.getTenantId()), Integer.parseInt(controllerContext.getUser().getUserIdOrOutUserIdIfOutUser()), BuryModule.TPM.TPM_ACTIVITY_PROOF_AUDIT, String.format(BuryOperation.SOMETHING_FILTER, "scene_audit_activity"));
        }
        return result;
    }

    private List<IObjectData> queryProof(ControllerContext context,
                                         String activityId,
                                         String dealerId,
                                         Long begin,
                                         Long end,
                                         List<String> idList) {
        if (idList.isEmpty()) {
            return new ArrayList<>();
        }

        SearchTemplateQuery query = new SearchTemplateQuery();

        //todo: 原先limit 0
        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter storeIdFilter = new Filter();
        storeIdFilter.setFieldName(TPMActivityProofFields.STORE_ID);
        storeIdFilter.setOperator(Operator.IN);
        storeIdFilter.setFieldValues(idList);

        Filter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.EQ);
        lifeStatusFilter.setFieldValues(Lists.newArrayList(CommonFields.LIFE_STATUS__NORMAL));

        query.setFilters(Lists.newArrayList(storeIdFilter, lifeStatusFilter));

        if (!Strings.isNullOrEmpty(activityId)) {

            Filter activityIdFilter = new Filter();
            activityIdFilter.setFieldName(TPMActivityProofFields.ACTIVITY_ID);
            activityIdFilter.setOperator(Operator.EQ);
            activityIdFilter.setFieldValues(Lists.newArrayList(activityId));

            query.getFilters().add(activityIdFilter);
        }

        if (!Strings.isNullOrEmpty(dealerId)) {

            Filter dealerIdFilter = new Filter();
            dealerIdFilter.setFieldName(TPMActivityProofFields.DEALER_ID);
            dealerIdFilter.setOperator(Operator.EQ);
            dealerIdFilter.setFieldValues(Lists.newArrayList(dealerId));

            query.getFilters().add(dealerIdFilter);
        }

        if ((begin != null && begin > 0) && (end != null && end > 0)) {
            Filter timeFilter = new Filter();
            timeFilter.setFieldName(CommonFields.CREATE_TIME);
            timeFilter.setOperator(Operator.BETWEEN);
            timeFilter.setFieldValues(Lists.newArrayList(begin.toString(), end.toString()));

            query.getFilters().add(timeFilter);
        }

        return CommonUtils.queryData(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_PROOF_OBJ, query);
    }

    private Map<String, Map<String, IObjectData>> queryProofMap(List<IObjectData> data) {

        Map<String, Map<String, IObjectData>> result = Maps.newHashMap();

        for (IObjectData datum : data) {

            String storeId = (String) datum.get(TPMActivityProofFields.STORE_ID);
            String activityId = (String) datum.get(TPMActivityProofFields.ACTIVITY_ID);
            long createTime = (long) datum.get(CommonFields.CREATE_TIME);

            if (result.containsKey(storeId)) {
                Map<String, IObjectData> activityProofMap = result.get(storeId);
                if (activityProofMap.containsKey(activityId)) {
                    long preCreateTime = (long) result.get(storeId).get(activityId).get(CommonFields.CREATE_TIME);
                    if (createTime > preCreateTime) {
                        result.get(storeId).put(activityId, datum);
                    }
                } else {
                    result.get(storeId).put(activityId, datum);
                }
            } else {
                result.put(storeId, Maps.newHashMap());
                result.get(storeId).put(activityId, datum);
            }
        }

        return result;
    }

    private Map<String, IObjectData> queryStoreMap(ControllerContext context, List<String> idList) {
        if (idList.isEmpty()) {
            return new HashMap<>();
        }

        Map<String, IObjectData> result = new HashMap<>();
        List<IObjectData> data = serviceFacade.findObjectDataByIds(context.getTenantId(), idList, ApiNames.ACCOUNT_OBJ);
        for (IObjectData datum : data) {
            result.put(datum.getId(), datum);
        }
        return result;
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Collections.emptyList();
    }
}
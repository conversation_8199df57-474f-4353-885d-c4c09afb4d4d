package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Sets;
import com.facishare.crm.fmcg.tpm.apiname.*;
import com.facishare.crm.fmcg.tpm.common.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.utils.TimeUtils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/12/16 下午4:02
 */
@SuppressWarnings("Duplicates")
public class TPMDealerActivityCostObjEditAction extends StandardEditAction {

    private static final Set<String> ALLOW_EDIT_FIELD = Sets.newHashSet();

    static {
        ALLOW_EDIT_FIELD.add(TPMDealerActivityCostFields.CONFIRMED_AMOUNT);

        ConfigFactory.getConfig("fs-fmcg-tpm-config", config -> {
            String allowEditFields = config.get("DEALER_COST_ALLOW_EDIT_FIELDS_1");
            if (!StringUtils.isEmpty(allowEditFields)) {
                ALLOW_EDIT_FIELD.addAll(JSON.parseArray(allowEditFields, String.class));
            }
        });
    }

    @Override
    protected void before(Arg arg) {

        //arg.getObjectData().put(TPMDealerActivityCostFields.CONFIRMED_AMOUNT, 0);
        super.before(arg);
        validateEndDate(arg);
        Long startTime = (Long) arg.getObjectData().get(TPMDealerActivityCostFields.BEGIN_DATE);
        Long endTime = (Long) arg.getObjectData().get(TPMDealerActivityCostFields.END_DATE);
        if (startTime == null || endTime == null) {
            throw new ValidateException("begin time or end time could not be empty.");
        }

        String activityId = (String) arg.getObjectData().get(TPMDealerActivityCostFields.ACTIVITY_ID);
        IObjectData activity = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), activityId, ApiNames.TPM_ACTIVITY_OBJ);
        String closedStatus = (String) activity.get(TPMActivityFields.CLOSED_STATUS);
        if (TPMActivityFields.CLOSE_STATUS__CLOSED.equals(closedStatus)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_HAS_CLOSED_CAN_NOT_CREATE_COST));
        }
        validateActivity(arg);


        if (TPMGrayUtils.dealerCostSupportFlowLayout(actionContext.getTenantId())) {
            IObjectData master = arg.getObjectData().toObjectData();
            String masterLifeFStatus = master.get(CommonFields.LIFE_STATUS, String.class);
            if (CommonFields.LIFE_STATUS__UNDER_REVIEW.equals(masterLifeFStatus) || CommonFields.LIFE_STATUS__IN_CHANGE.equals(masterLifeFStatus)) {
                this.updatedFieldMap.keySet().forEach(key -> {
                    if (!key.endsWith("__c") && !ALLOW_EDIT_FIELD.contains(key)) {
                        throw new ValidateException(I18N.text(I18NKeys.DEALER_ACTIVITY_COST_OBJ_EDIT_ACTION_5));
                    }
                });
            } else {
                if (!"ineffective".equals(arg.getObjectData().get(CommonFields.LIFE_STATUS))) {
                    throw new ValidateException(I18N.text(I18NKeys.NORMAL_STATUS_COST_CAN_NOT_EDIT));
                }
            }
        } else {
            if (!"ineffective".equals(arg.getObjectData().get(CommonFields.LIFE_STATUS))) {
                throw new ValidateException(I18N.text(I18NKeys.NORMAL_STATUS_COST_CAN_NOT_EDIT));
            }
        }

        if (this.updatedFieldMap.get(TPMDealerActivityCostFields.BEGIN_DATE) != null || this.updatedFieldMap.get(TPMDealerActivityCostFields.END_DATE) != null) {
            throw new ValidateException(I18N.text(I18NKeys.REJECT_COST_CAN_NOT_EDIT_TIME_FIELD));
        }
    }

    private void validateEndDate(Arg arg) {
        long begin = (long) arg.getObjectData().get(TPMDealerActivityCostFields.BEGIN_DATE);
        long end = TimeUtils.convertToDayEndIfTimeWasDayBegin((long) arg.getObjectData().get(TPMDealerActivityCostFields.END_DATE));
        arg.getObjectData().put(TPMDealerActivityCostFields.END_DATE, end);

        if (end <= begin) {
            throw new ValidateException(I18N.text(I18NKeys.COST_TIME_RANGE_IS_ERROR));
        }
    }


    private void validateActivity(Arg arg) {
        double amount = Double.parseDouble(arg.getObjectData().getOrDefault(TPMDealerActivityCostFields.AUDITED_AMOUNT, 0.0).toString());
        String activityId = (String) arg.getObjectData().get(TPMDealerActivityCostFields.ACTIVITY_ID);
        IObjectData activity = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), activityId, ApiNames.TPM_ACTIVITY_OBJ);

        String activityBudgetTableId = activity.get(TPMActivityFields.BUDGET_TABLE, String.class);
        Double activityAmount = activity.get(TPMActivityFields.ACTIVITY_AMOUNT, Double.class);

        if (!TPMGrayUtils.excessDeductionForCost(actionContext.getTenantId()) && !Strings.isNullOrEmpty(activityBudgetTableId) &&
                activityAmount != null &&
                amount > activityAmount) {
            throw new ValidateException(I18N.text(I18NKeys.AMOUNT_SHOULD_LESS_THAN_AUDIT_AMOUNT));
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        deal1(arg, result);
        return super.after(arg, result);
    }

    private void deal1(Arg arg, Result result) {
        Long startTime = (Long) result.getObjectData().get(TPMDealerActivityCostFields.BEGIN_DATE);
        Long endTime = (Long) result.getObjectData().get(TPMDealerActivityCostFields.END_DATE);

        String dealerId = (String) result.getObjectData().get(TPMDealerActivityCostFields.DEALER_ID);
        String activityId = (String) result.getObjectData().get(TPMDealerActivityCostFields.ACTIVITY_ID);
        String dealerActivityId = (String) arg.getObjectData().get(TPMDealerActivityCostFields.DEALER_ACTIVITY_ID);

        SearchTemplateQuery query = new SearchTemplateQuery();
        int offset = 0;
        query.setLimit(500);
        query.setOffset(offset);
        query.setSearchSource("db");

        Filter proofCreateTimeFilter = new Filter();
        proofCreateTimeFilter.setFieldName(TPMActivityProofFields.CREATE_TIME);
        proofCreateTimeFilter.setOperator(Operator.BETWEEN);
        proofCreateTimeFilter.setFieldValues(Lists.newArrayList(startTime.toString(), endTime.toString()));

        Filter proofCostFilter = new Filter();
        proofCostFilter.setFieldName(TPMActivityProofFields.DEALER_ACTIVITY_COST_ID);
        proofCostFilter.setOperator(Operator.IS);
        proofCostFilter.setFieldValues(Lists.newArrayList());

        Filter dealerActivityFilter = new Filter();
        dealerActivityFilter.setFieldName(TPMActivityProofFields.DEALER_ACTIVITY);
        dealerActivityFilter.setFieldValues(Lists.newArrayList(dealerActivityId));
        dealerActivityFilter.setOperator(Operator.EQ);


        Filter dealerIdFilter = new Filter();
        dealerIdFilter.setFieldName(TPMActivityProofFields.DEALER_ID);
        dealerIdFilter.setOperator(Operator.EQ);
        dealerIdFilter.setFieldValues(Lists.newArrayList(dealerId));

        Filter activityFilter = new Filter();
        activityFilter.setFieldName(TPMActivityProofFields.ACTIVITY_ID);
        activityFilter.setOperator(Operator.EQ);
        activityFilter.setFieldValues(Lists.newArrayList(activityId));


        Filter auditStatusFilter = new Filter();
        auditStatusFilter.setFieldName(TPMActivityProofFields.AUDIT_STATUS);
        auditStatusFilter.setFieldValues(Lists.newArrayList(TPMActivityProofFields.AUDIT_STATUS__PASS, TPMActivityProofFields.AUDIT_STATUS__REJECT));
        auditStatusFilter.setOperator(Operator.IN);

        query.setFilters(Lists.newArrayList(proofCostFilter, proofCreateTimeFilter, dealerIdFilter, activityFilter, auditStatusFilter, dealerActivityFilter));

        OrderBy createTimeOrder = new OrderBy();
        createTimeOrder.setFieldName(CommonFields.CREATE_TIME);
        createTimeOrder.setIsAsc(true);
        query.setOrders(Lists.newArrayList(createTimeOrder));
        List<IObjectData> proofs = Lists.newArrayList();
        List<IObjectData> tmpList;
        while (!(tmpList = serviceFacade.findBySearchQuery(User.systemUser(actionContext.getTenantId()), ApiNames.TPM_ACTIVITY_PROOF_OBJ, query).getData()).isEmpty()) {
            proofs.addAll(tmpList);
            offset += tmpList.size();
            query.setOffset(offset);
        }
        if (!proofs.isEmpty()) {
            query.setLimit(-1);
            query.setOffset(0);
            query.getFilters().clear();
            Filter proofIdFilter = new Filter();
            proofIdFilter.setFieldName(TPMActivityProofAuditFields.ACTIVITY_PROOF_ID);
            proofIdFilter.setOperator(Operator.IN);
            proofIdFilter.setFieldValues(proofs.stream().map(DBRecord::getId).collect(Collectors.toList()));
            query.setFilters(Lists.newArrayList(proofIdFilter));
            List<IObjectData> proofAuditList = CommonUtils.queryData(serviceFacade, User.systemUser(actionContext.getTenantId()), ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ, query);

            List<List<IObjectData>> listArr = Lists.partition(proofAuditList, 50);
            List<String> updateField = Lists.newArrayList(TPMActivityProofAuditFields.DEALER_ACTIVITY_COST_ID);
            for (List<IObjectData> list : listArr) {
                for (IObjectData datum : list) {
                    datum.set(TPMActivityProofAuditFields.DEALER_ACTIVITY_COST_ID, result.getObjectData().getId());
                }
                serviceFacade.batchUpdateByFields(User.systemUser(actionContext.getTenantId()), list, updateField);
            }

            updateField = Lists.newArrayList(TPMActivityProofFields.DEALER_ACTIVITY_COST_ID);
            listArr = Lists.partition(proofs, 50);
            for (List<IObjectData> list : listArr) {
                for (IObjectData datum : list) {
                    datum.set(TPMActivityProofFields.DEALER_ACTIVITY_COST_ID, result.getObjectData().getId());
                }
                serviceFacade.batchUpdateByFields(User.systemUser(actionContext.getTenantId()), list, updateField);
            }
        }
    }
}

package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.crm.fmcg.tpm.apiname.TPMActivityFields;
import com.facishare.paas.appframework.core.predef.controller.StandardDescribeLayoutController;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.IFieldSection;
import com.facishare.paas.metadata.ui.layout.IFormComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 12/23/20 8:42 PM
 */
@Slf4j
@SuppressWarnings("Duplicates")
public class TPMActivityObjDescribeLayoutController extends StandardDescribeLayoutController {

    @Override
    protected Result after(Arg arg, Result result) {
        if ("add".equals(arg.getLayout_type()) && Boolean.TRUE.equals(arg.getInclude_layout())) {
            ILayout layout = result.getLayout().toLayout();
            try {
                List<IComponent> components = layout.getComponents();
                for (IComponent component : components) {
                    if (component instanceof IFormComponent) {
                        List<IFieldSection> sections = ((IFormComponent) component).getFieldSections();
                        for (IFieldSection section : sections) {
                            section.setFields(section.getFields().stream().filter(f -> !f.getFieldName().equals(TPMActivityFields.ACTIVITY_STATUS)).collect(Collectors.toList()));
                        }
                    }
                }
            } catch (MetadataServiceException ex) {
                log.info("after task cause error.", ex);
            }
        }
        return super.after(arg, result);
    }
}

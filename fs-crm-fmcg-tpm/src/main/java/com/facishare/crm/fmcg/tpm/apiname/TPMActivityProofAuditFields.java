package com.facishare.crm.fmcg.tpm.apiname;

public interface TPMActivityProofAuditFields {

    String AUDIT_TIME = "audit_time";

    String STORE_ID = "store_id";

    String DEALER_ID = "dealer_id";

    String TOTAL = "total";

    String ACTIVITY_ID = "activity_id";

    String PROOF_IMAGES = "proof_images";

    String DEALER_ACTIVITY = "dealer_activity";

    String AUDITOR = "auditor";

    String INSPECTOR = "inspector";

    String ACTIVITY_AGREEMENT_ID = "activity_agreement_id";

    String AUDIT_STATUS = "audit_status";
    String AUDIT_TOTAL = "audit_total";

    String AUDIT_STATUS__PASS = "pass";

    String AUDIT_STATUS__REJECT = "reject";

    String DEALER_ACTIVITY_COST_ID = "dealer_activity_cost_id";

    String VISIT_ID = "visit_id";

    String ACTION_ID = "action_id";

    String ACTIVITY_PROOF_ID = "activity_proof_id";

    String OPINION = "opinion";

    String CREATE_TIME = "create_time";

    String RANDOM_AUDIT_STATUS = "random_audit_status";
}
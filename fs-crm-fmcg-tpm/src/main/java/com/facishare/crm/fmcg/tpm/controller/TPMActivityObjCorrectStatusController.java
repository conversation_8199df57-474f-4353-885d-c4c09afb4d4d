package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.common.parallel.ParallelUtils;
import com.facishare.crm.fmcg.tpm.api.proof.CorrectActivityStatus;
import com.facishare.crm.fmcg.tpm.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.apiname.CommonFields;
import com.facishare.crm.fmcg.tpm.apiname.TPMActivityAgreementFields;
import com.facishare.crm.fmcg.tpm.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.github.jedis.support.MergeJedisCmd;
import com.google.common.collect.Lists;
import redis.clients.jedis.params.SetParams;

import java.util.Collections;
import java.util.List;
import java.util.UUID;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 12/2/20 4:12 PM
 */
@SuppressWarnings("Duplicates")
public class TPMActivityObjCorrectStatusController extends PreDefineController<CorrectActivityStatus.Arg, CorrectActivityStatus.Result> {

    private static final MergeJedisCmd redisCmd = SpringUtil.getContext().getBean("redisCmd", MergeJedisCmd.class);

    private static final String REDIS_UNIQUE_TEMPLATE = "TPM.CorrectStatus.%s";

    private static final Long LOCK_TIME = 1000 * 60 * 30L;

    private static final int MAX_QUERY_TIMES = 2000;

    @Override
    protected CorrectActivityStatus.Result doService(CorrectActivityStatus.Arg arg) {
        this.controllerContext.getRequestContext().setAttribute("not_validate", true);

        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createBackgroundTask();
        parallelTask.submit(() -> {
            String lockKey = String.format(REDIS_UNIQUE_TEMPLATE, controllerContext.getTenantId());
            String value = UUID.randomUUID().toString();

            try {
                String lockResult = redisCmd.set(lockKey, value, SetParams.setParams().nx().ex(LOCK_TIME));
                if (!"ok".equalsIgnoreCase(lockResult)) {
                    log.info("correct task is running ,ei:{}", controllerContext.getTenantId());
                    return;
                }
                stopWatch.lap("correctScheduleActivity");
                correctScheduleActivity(controllerContext, serviceFacade);
                stopWatch.lap("correctEndActivity");
                correctEndActivity(controllerContext, serviceFacade);
                stopWatch.lap("correctScheduleAgreement");
                correctScheduleAgreement(controllerContext, serviceFacade);
                stopWatch.lap("correctEndAgreement");
                correctEndAgreement(controllerContext, serviceFacade);
                stopWatch.lap("correctIneffectiveAgreement");
                correctIneffectiveAgreement();
            } finally {
                redisCmd.del(lockKey);
            }
        });
        parallelTask.run();

        return new CorrectActivityStatus.Result();
    }

    private void correctScheduleActivity(ControllerContext context, ServiceFacade facade) {
        long now = System.currentTimeMillis();

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter beginDateFilter = new Filter();
        beginDateFilter.setFieldName(TPMActivityFields.BEGIN_DATE);
        beginDateFilter.setOperator(Operator.LT);
        beginDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

        Filter endDateFilter = new Filter();
        endDateFilter.setFieldName(TPMActivityFields.END_DATE);
        endDateFilter.setOperator(Operator.GT);
        endDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

        Filter statusFilter = new Filter();
        statusFilter.setFieldName(TPMActivityFields.ACTIVITY_STATUS);
        statusFilter.setOperator(Operator.IN);
        statusFilter.setFieldValues(Lists.newArrayList(
                TPMActivityFields.ACTIVITY_STATUS__SCHEDULE,
                TPMActivityFields.ACTIVITY_STATUS__END)
        );

        query.setFilters(Lists.newArrayList(beginDateFilter, endDateFilter, statusFilter));

        CommonUtils.queryDataWithoutOffset(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_OBJ, query, MAX_QUERY_TIMES, activities -> {
            for (IObjectData activity : activities) {
                activity.set(TPMActivityFields.ACTIVITY_STATUS, TPMActivityFields.ACTIVITY_STATUS__IN_PROGRESS);
            }
            List<String> updateFields = Lists.newArrayList(TPMActivityFields.ACTIVITY_STATUS);
            for (List<IObjectData> parts : Lists.partition(activities, 100)) {
                facade.batchUpdateByFields(ActionContextExt.of(User.systemUser(controllerContext.getTenantId()), this.controllerContext.getRequestContext()).getContext(), parts, updateFields);
            }
        });
    }

    private void correctEndActivity(ControllerContext context, ServiceFacade facade) {
        long now = System.currentTimeMillis();

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter endDateFilter = new Filter();
        endDateFilter.setFieldName(TPMActivityFields.END_DATE);
        endDateFilter.setOperator(Operator.LT);
        endDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

        Filter statusFilter = new Filter();
        statusFilter.setFieldName(TPMActivityFields.ACTIVITY_STATUS);
        statusFilter.setOperator(Operator.IN);
        statusFilter.setFieldValues(Lists.newArrayList(
                TPMActivityFields.ACTIVITY_STATUS__SCHEDULE,
                TPMActivityFields.ACTIVITY_STATUS__IN_PROGRESS));

        query.setFilters(Lists.newArrayList(endDateFilter, statusFilter));

        CommonUtils.queryDataWithoutOffset(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_OBJ, query, MAX_QUERY_TIMES, activities -> {
            for (IObjectData activity : activities) {
                activity.set(TPMActivityFields.ACTIVITY_STATUS, TPMActivityFields.ACTIVITY_STATUS__END);
            }
            List<String> updateFields = Lists.newArrayList(TPMActivityFields.ACTIVITY_STATUS);
            for (List<IObjectData> parts : Lists.partition(activities, 100)) {
                facade.batchUpdateByFields(ActionContextExt.of(User.systemUser(controllerContext.getTenantId()), this.controllerContext.getRequestContext()).getContext(), parts, updateFields);
            }
        });

    }

    private void correctScheduleAgreement(ControllerContext context, ServiceFacade facade) {
        long now = System.currentTimeMillis();

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter beginDateFilter = new Filter();
        beginDateFilter.setFieldName(TPMActivityAgreementFields.BEGIN_DATE);
        beginDateFilter.setOperator(Operator.LT);
        beginDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

        Filter endDateFilter = new Filter();
        endDateFilter.setFieldName(TPMActivityAgreementFields.END_DATE);
        endDateFilter.setOperator(Operator.GT);
        endDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

        Filter statusFilter = new Filter();
        statusFilter.setFieldName(TPMActivityAgreementFields.AGREEMENT_STATUS);
        statusFilter.setOperator(Operator.IN);
        statusFilter.setFieldValues(Lists.newArrayList(
                TPMActivityAgreementFields.AGREEMENT_STATUS__END,
                TPMActivityAgreementFields.AGREEMENT_STATUS__SCHEDULE));

        Filter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.EQ);
        lifeStatusFilter.setFieldValues(Lists.newArrayList(CommonFields.LIFE_STATUS__NORMAL));

        query.setFilters(Lists.newArrayList(beginDateFilter, endDateFilter, statusFilter, lifeStatusFilter));

        CommonUtils.queryDataWithoutOffset(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, query, MAX_QUERY_TIMES, agreements -> {
            for (IObjectData activity : agreements) {
                activity.set(TPMActivityAgreementFields.AGREEMENT_STATUS, TPMActivityAgreementFields.AGREEMENT_STATUS__IN_PROGRESS);
            }
            List<String> updateFields = Lists.newArrayList(TPMActivityAgreementFields.AGREEMENT_STATUS);
            for (List<IObjectData> parts : Lists.partition(agreements, 100)) {
                facade.batchUpdateByFields(ActionContextExt.of(User.systemUser(controllerContext.getTenantId()), this.controllerContext.getRequestContext()).getContext(), parts, updateFields);
            }
        });
    }

    private void correctEndAgreement(ControllerContext context, ServiceFacade facade) {
        long now = System.currentTimeMillis();

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter endDateFilter = new Filter();
        endDateFilter.setFieldName(TPMActivityAgreementFields.END_DATE);
        endDateFilter.setOperator(Operator.LT);
        endDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

        Filter statusFilter = new Filter();
        statusFilter.setFieldName(TPMActivityAgreementFields.AGREEMENT_STATUS);
        statusFilter.setOperator(Operator.IN);
        statusFilter.setFieldValues(Lists.newArrayList(
                TPMActivityAgreementFields.AGREEMENT_STATUS__IN_PROGRESS,
                TPMActivityAgreementFields.AGREEMENT_STATUS__SCHEDULE));

        query.setFilters(Lists.newArrayList(endDateFilter, statusFilter));

        CommonUtils.queryDataWithoutOffset(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, query, MAX_QUERY_TIMES, agreements -> {
            for (IObjectData activity : agreements) {
                activity.set(TPMActivityAgreementFields.AGREEMENT_STATUS, TPMActivityAgreementFields.AGREEMENT_STATUS__END);
            }
            List<String> updateFields = Lists.newArrayList(TPMActivityAgreementFields.AGREEMENT_STATUS);
            for (List<IObjectData> parts : Lists.partition(agreements, 50)) {
                facade.batchUpdateByFields(ActionContextExt.of(User.systemUser(controllerContext.getTenantId()), this.controllerContext.getRequestContext()).getContext(), parts, updateFields);
            }
        });
    }


    private void correctIneffectiveAgreement() {
        long now = System.currentTimeMillis();

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter beginDateFilter = new Filter();
        beginDateFilter.setFieldName(TPMActivityAgreementFields.BEGIN_DATE);
        beginDateFilter.setOperator(Operator.LT);
        beginDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

        Filter endDateFilter = new Filter();
        endDateFilter.setFieldName(TPMActivityAgreementFields.END_DATE);
        endDateFilter.setOperator(Operator.GT);
        endDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

        Filter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.EQ);
        lifeStatusFilter.setFieldValues(Lists.newArrayList(ObjectLifeStatus.INEFFECTIVE.getCode()));

        Filter statusFilter = new Filter();
        statusFilter.setFieldName(TPMActivityAgreementFields.AGREEMENT_STATUS);
        statusFilter.setOperator(Operator.IN);
        statusFilter.setFieldValues(Lists.newArrayList(
                TPMActivityAgreementFields.AGREEMENT_STATUS__IN_PROGRESS,
                TPMActivityAgreementFields.AGREEMENT_STATUS__END));

        query.setFilters(Lists.newArrayList(beginDateFilter, endDateFilter, lifeStatusFilter, statusFilter));

        CommonUtils.queryDataWithoutOffset(serviceFacade, User.systemUser(controllerContext.getTenantId()), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, query, MAX_QUERY_TIMES, agreements -> {
            for (IObjectData activity : agreements) {
                activity.set(TPMActivityAgreementFields.AGREEMENT_STATUS, TPMActivityAgreementFields.AGREEMENT_STATUS__SCHEDULE);
            }

            List<String> updateFields = Lists.newArrayList(TPMActivityAgreementFields.AGREEMENT_STATUS);
            for (List<IObjectData> parts : Lists.partition(agreements, 50)) {
                serviceFacade.batchUpdateByFields(ActionContextExt.of(User.systemUser(controllerContext.getTenantId()), this.controllerContext.getRequestContext()).getContext(), parts, updateFields);
            }
        });

    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Collections.emptyList();
    }
}

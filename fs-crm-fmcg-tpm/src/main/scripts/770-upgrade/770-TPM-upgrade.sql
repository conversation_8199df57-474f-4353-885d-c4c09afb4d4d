-- add dealer_id,activity_type filed to TPMActivityObj
alter table fmcg_tpm_activity add column if not exists dealer_id varchar(64);
alter table fmcg_tpm_activity add column if not exists activity_type varchar(64);
alter table fmcg_tpm_activity add column if not exists proof_record_type varchar(64);
alter table fmcg_tpm_activity add column if not exists write_off_policy varchar(128);

-- add actual_total field to TPMActivityProofObj
alter table fmcg_tpm_activity_proof add column if not exists actual_total numeric;
update fmcg_tpm_activity_proof set actual_total = total where actual_total is null;

alter table fmcg_tpm_activity_budget add  column if not exists transfer_in_amount numeric;
alter table fmcg_tpm_activity_budget add  column if not exists transfer_out_amount numeric;
alter table fmcg_tpm_activity_budget add  column if not exists total_amount VARCHAR;


alter table fmcg_tpm_dealer_activity_cost add column if not exists enter_into_account boolean;
--update fmcg_tpm_dealer_activity_cost set enter_into_account = false;

alter table fmcg_tpm_dealer_activity_cost add column if not exists fund_account_id varchar(100);


create index concurrently if not exists fmcg_tpm_activity_budget_adjust_fbti_ls on fmcg_tpm_activity_budget_adjust using btree(from_budget_table_id,life_status);
create index concurrently if not exists fmcg_tpm_activity_budget_adjust_tbti_ls on fmcg_tpm_activity_budget_adjust using btree(to_budget_table_id,life_status);
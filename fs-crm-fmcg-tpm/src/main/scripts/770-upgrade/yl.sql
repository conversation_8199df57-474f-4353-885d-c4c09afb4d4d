select distinct e.value4
from tpm_area_budget__c a
         inner join tpm_activity_plan_store__c b on a.tenant_id = b.tenant_id
    and a.value4 = b.value4
    and b.is_deleted = 0
    and b.value6 = 'normal'
    and b.value3 = 'a'
         inner join tpm_activity_plan_channel__c c on a.tenant_id = c.tenant_id
    and a.value4 = c.value4
    and c.is_deleted = 0
    and c.value6 = 'normal'
    and c.value3 = '619b36a545671c0001f0973f'
         inner join tpm_business_center_budget__c d on a.tenant_id = d.tenant_id
    and a.value4 = d.value3
    and d.is_deleted = 0
    and d.value4 = 'normal'
    and d.value13 = '2645'
         inner join tpm_activity_plan_time_span__c e on a.tenant_id = e.tenant_id
    and a.value4 = e.value3
    and e.is_deleted = 0
    and e.value4 = 'normal'
    and e.value14 <= 1657637657581
    and e.value13 >= 1657637657581 limit
where a.tenant_id = '721787'
  and a.is_deleted = 0
  and a.value5 = 'normal'
  and a.value14 = '';
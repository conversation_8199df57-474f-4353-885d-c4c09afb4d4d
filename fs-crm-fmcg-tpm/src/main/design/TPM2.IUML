@startuml TPM2



abstract Component {
}

class ObjectComponent {
    + PaasObject object
    + ComponentAbility basicAbilities;
    + ComponentAbility[] abilities
}

ObjectComponent -up-> Component

interface ComponentAbility {
}

ComponentAbility -right-> ObjectComponent

abstract DataValidateAbility {
}

abstract DataInsureAbility {
}

abstract CallbackAbility {
}

DataValidateAbility -up-> ComponentAbility
DataInsureAbility   -up-> ComponentAbility
CallbackAbility     -up-> ComponentAbility

@enduml
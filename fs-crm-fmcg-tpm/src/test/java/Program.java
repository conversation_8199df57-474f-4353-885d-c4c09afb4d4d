import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 12/9/20 2:48 PM
 */
public class Program {

    public static void main(String[] args) {

        long begin = 1608739200000L;
        long end = 1609344000000L;

        long agreementBegin = 1608912000000L;
        long agreementEnd = 1609171199999L;

        if ((begin > agreementBegin && begin < agreementEnd) || (end > agreementBegin && end < agreementEnd)) {
            System.out.println("a");
            throw new ValidateException(I18N.text(I18NKeys.TIME_OVERLAP_IN_ACTIVITY_AND_STORE_ERROR));
        }
        System.out.println("f");
    }
}
